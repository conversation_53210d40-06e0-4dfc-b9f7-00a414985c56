<?php
declare(strict_types=1);

namespace Models;

require_once __DIR__ . '/Model.php';

/**
 * Translation model for managing multi-language content
 */
class Translation extends Model
{
    /** @var string Table name */
    protected string $table = 'translations';
    
    /** @var string Primary key field */
    protected string $primaryKey = 'id';
    
    /** @var array<string> Mass assignable fields */
    protected array $fillable = [
        'language_code',
        'translation_key',
        'translation_value',
        'category'
    ];

    /**
     * Get translation by key and language
     * 
     * @param string $key
     * @param string $language
     * @return string|null
     */
    public static function getTranslation(string $key, string $language = 'en'): ?string
    {
        $instance = new self();
        $db = $instance->getConnection();
        
        $stmt = $db->prepare("SELECT translation_value FROM translations WHERE translation_key = ? AND language_code = ?");
        $stmt->execute([$key, $language]);
        
        $result = $stmt->fetchColumn();
        return $result !== false ? $result : null;
    }

    /**
     * Get all translations for a language
     * 
     * @param string $language
     * @return array<string, string>
     */
    public static function getAllForLanguage(string $language): array
    {
        $instance = new self();
        $db = $instance->getConnection();
        
        $stmt = $db->prepare("SELECT translation_key, translation_value FROM translations WHERE language_code = ?");
        $stmt->execute([$language]);
        
        return $stmt->fetchAll(\PDO::FETCH_KEY_PAIR);
    }

    /**
     * Get translations by category
     * 
     * @param string $category
     * @param string $language
     * @return array<string, string>
     */
    public static function getByCategory(string $category, string $language = 'en'): array
    {
        $instance = new self();
        $db = $instance->getConnection();
        
        $stmt = $db->prepare("SELECT translation_key, translation_value FROM translations WHERE category = ? AND language_code = ?");
        $stmt->execute([$category, $language]);
        
        return $stmt->fetchAll(\PDO::FETCH_KEY_PAIR);
    }

    /**
     * Set translation
     * 
     * @param string $key
     * @param string $value
     * @param string $language
     * @param string $category
     * @return bool
     */
    public static function setTranslation(string $key, string $value, string $language = 'en', string $category = 'general'): bool
    {
        $instance = new self();
        $db = $instance->getConnection();
        
        $stmt = $db->prepare("
            INSERT OR REPLACE INTO translations (translation_key, translation_value, language_code, category, updated_at) 
            VALUES (?, ?, ?, ?, strftime('%s', 'now'))
        ");
        
        return $stmt->execute([$key, $value, $language, $category]);
    }

    /**
     * Delete translation
     * 
     * @param string $key
     * @param string|null $language If null, deletes for all languages
     * @return bool
     */
    public static function deleteTranslation(string $key, ?string $language = null): bool
    {
        $instance = new self();
        $db = $instance->getConnection();
        
        if ($language) {
            $stmt = $db->prepare("DELETE FROM translations WHERE translation_key = ? AND language_code = ?");
            return $stmt->execute([$key, $language]);
        } else {
            $stmt = $db->prepare("DELETE FROM translations WHERE translation_key = ?");
            return $stmt->execute([$key]);
        }
    }

    /**
     * Import translations from array (file-based fallback)
     *
     * @param array<string, string> $translations
     * @param string $language
     * @param string $category
     * @return int Number of imported translations
     */
    public static function importTranslations(array $translations, string $language = 'en', string $category = 'general'): int
    {
        try {
            // Create translations directory
            $translationsDir = APP_ROOT . '/database/translations';
            if (!is_dir($translationsDir)) {
                mkdir($translationsDir, 0755, true);
            }

            // Load existing translations
            $filePath = $translationsDir . "/{$language}.json";
            $existingTranslations = [];

            if (file_exists($filePath)) {
                $content = file_get_contents($filePath);
                if ($content !== false) {
                    $existingTranslations = json_decode($content, true) ?? [];
                }
            }

            // Merge with new translations
            $mergedTranslations = array_merge($existingTranslations, $translations);

            // Save back to file
            $result = file_put_contents($filePath, json_encode($mergedTranslations, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

            return $result !== false ? count($translations) : 0;

        } catch (Exception $e) {
            error_log("Failed to import translations: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Get available languages
     * 
     * @return array<string>
     */
    public static function getAvailableLanguages(): array
    {
        $instance = new self();
        $db = $instance->getConnection();
        
        $stmt = $db->query("SELECT DISTINCT language_code FROM translations ORDER BY language_code");
        return $stmt->fetchAll(\PDO::FETCH_COLUMN);
    }

    /**
     * Get translation statistics
     * 
     * @return array<string, mixed>
     */
    public static function getStats(): array
    {
        $instance = new self();
        $db = $instance->getConnection();
        
        $stats = [];
        
        // Total translations
        $stmt = $db->query("SELECT COUNT(*) FROM translations");
        $stats['total'] = $stmt->fetchColumn();
        
        // By language
        $stmt = $db->query("SELECT language_code, COUNT(*) as count FROM translations GROUP BY language_code");
        $stats['by_language'] = $stmt->fetchAll(\PDO::FETCH_KEY_PAIR);
        
        // By category
        $stmt = $db->query("SELECT category, COUNT(*) as count FROM translations GROUP BY category");
        $stats['by_category'] = $stmt->fetchAll(\PDO::FETCH_KEY_PAIR);
        
        return $stats;
    }

    /**
     * Search translations
     * 
     * @param string $search
     * @param string|null $language
     * @return array<array<string, mixed>>
     */
    public static function search(string $search, ?string $language = null): array
    {
        $instance = new self();
        $db = $instance->getConnection();
        
        $sql = "SELECT * FROM translations WHERE (translation_key LIKE ? OR translation_value LIKE ?)";
        $params = ["%{$search}%", "%{$search}%"];
        
        if ($language) {
            $sql .= " AND language_code = ?";
            $params[] = $language;
        }
        
        $sql .= " ORDER BY language_code, translation_key";
        
        $stmt = $db->prepare($sql);
        $stmt->execute($params);
        
        return $stmt->fetchAll(\PDO::FETCH_ASSOC);
    }
}
