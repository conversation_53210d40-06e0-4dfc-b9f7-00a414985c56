<?php
declare(strict_types=1);

namespace Security;

/**
 * CSRF Protection class for preventing Cross-Site Request Forgery attacks
 */
class CsrfProtection
{
    /** @var string Session key for CSRF token */
    private const TOKEN_KEY = 'csrf_token';
    
    /** @var string Form field name for CSRF token */
    private const FIELD_NAME = '_token';
    
    /** @var int Token lifetime in seconds (1 hour) */
    private const TOKEN_LIFETIME = 3600;

    /**
     * Generate CSRF token
     * 
     * @return string
     */
    public static function generateToken(): string
    {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        $token = bin2hex(random_bytes(32));
        
        $_SESSION[self::TOKEN_KEY] = [
            'token' => $token,
            'created' => time()
        ];
        
        return $token;
    }

    /**
     * Get current CSRF token (generate if not exists)
     * 
     * @return string
     */
    public static function getToken(): string
    {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        // Check if token exists and is valid
        if (isset($_SESSION[self::TOKEN_KEY])) {
            $tokenData = $_SESSION[self::TOKEN_KEY];
            
            if (isset($tokenData['token'], $tokenData['created'])) {
                // Check if token is not expired
                if (time() - $tokenData['created'] < self::TOKEN_LIFETIME) {
                    return $tokenData['token'];
                }
            }
        }
        
        // Generate new token if not exists or expired
        return self::generateToken();
    }

    /**
     * Validate CSRF token
     * 
     * @param string $token Token to validate
     * @return bool
     */
    public static function validateToken(string $token): bool
    {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        if (!isset($_SESSION[self::TOKEN_KEY])) {
            return false;
        }
        
        $tokenData = $_SESSION[self::TOKEN_KEY];
        
        if (!isset($tokenData['token'], $tokenData['created'])) {
            return false;
        }
        
        // Check if token is expired
        if (time() - $tokenData['created'] >= self::TOKEN_LIFETIME) {
            unset($_SESSION[self::TOKEN_KEY]);
            return false;
        }
        
        // Compare tokens using timing-safe comparison
        return hash_equals($tokenData['token'], $token);
    }

    /**
     * Validate CSRF token from request
     * 
     * @return bool
     */
    public static function validateRequest(): bool
    {
        // Get token from request
        $token = $_POST[self::FIELD_NAME] ?? 
                $_GET[self::FIELD_NAME] ?? 
                $_SERVER['HTTP_X_CSRF_TOKEN'] ?? '';
        
        if (empty($token)) {
            return false;
        }
        
        return self::validateToken($token);
    }

    /**
     * Require valid CSRF token (throw exception if invalid)
     * 
     * @throws \Exception
     * @return void
     */
    public static function requireValidToken(): void
    {
        if (!self::validateRequest()) {
            throw new \Exception('Invalid CSRF token', 403);
        }
    }

    /**
     * Generate HTML input field for CSRF token
     * 
     * @return string
     */
    public static function getTokenField(): string
    {
        $token = self::getToken();
        return '<input type="hidden" name="' . self::FIELD_NAME . '" value="' . htmlspecialchars($token) . '">';
    }

    /**
     * Get token for JavaScript/AJAX requests
     * 
     * @return array<string, string>
     */
    public static function getTokenForAjax(): array
    {
        return [
            'name' => self::FIELD_NAME,
            'value' => self::getToken()
        ];
    }

    /**
     * Clear CSRF token from session
     * 
     * @return void
     */
    public static function clearToken(): void
    {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        unset($_SESSION[self::TOKEN_KEY]);
    }

    /**
     * Middleware function to check CSRF for POST requests
     * 
     * @return bool
     */
    public static function checkPostRequest(): bool
    {
        // Only check CSRF for POST, PUT, PATCH, DELETE requests
        $method = $_SERVER['REQUEST_METHOD'] ?? 'GET';
        
        if (in_array($method, ['POST', 'PUT', 'PATCH', 'DELETE'])) {
            return self::validateRequest();
        }
        
        return true; // GET requests don't need CSRF protection
    }

    /**
     * Generate meta tag for CSRF token (for use in HTML head)
     * 
     * @return string
     */
    public static function getMetaTag(): string
    {
        $token = self::getToken();
        return '<meta name="csrf-token" content="' . htmlspecialchars($token) . '">';
    }

    /**
     * Get JavaScript code to set up CSRF token for AJAX requests
     * 
     * @return string
     */
    public static function getJavaScriptSetup(): string
    {
        $token = self::getToken();
        
        return "
        <script>
        // Setup CSRF token for AJAX requests
        window.csrfToken = '" . addslashes($token) . "';
        
        // Setup jQuery AJAX to include CSRF token
        if (typeof $ !== 'undefined') {
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': window.csrfToken
                }
            });
        }
        
        // Setup fetch to include CSRF token
        const originalFetch = window.fetch;
        window.fetch = function(url, options = {}) {
            if (!options.headers) {
                options.headers = {};
            }
            
            // Add CSRF token for POST, PUT, PATCH, DELETE requests
            const method = (options.method || 'GET').toUpperCase();
            if (['POST', 'PUT', 'PATCH', 'DELETE'].includes(method)) {
                options.headers['X-CSRF-TOKEN'] = window.csrfToken;
            }
            
            return originalFetch(url, options);
        };
        </script>
        ";
    }
}
