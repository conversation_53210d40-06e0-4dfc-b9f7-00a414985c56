/**
 * 🎵 ГЕНЕРАТОР ЗВУКА ПАДАЮЩИХ МОНЕТ
 * Создает звук падающих монет с помощью Web Audio API если нет аудио файла
 */

class CoinsSoundGenerator {
    constructor() {
        this.audioContext = null;
        this.init();
    }

    init() {
        try {
            // Создаем аудио контекст
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();

            // Тихая инициализация
        } catch (error) {
            // Тихо игнорируем ошибки
        }
    }

    generateCoinsSound(amount = 10) {
        if (!this.audioContext) return;

        try {
            // Количество монет зависит от суммы reward'а
            const numCoins = Math.min(Math.max(3, Math.floor(amount / 2)), 8);

            // Создаем реалистичные звуки падающих монет
            const coinSounds = [];

            for (let i = 0; i < numCoins; i++) {
                coinSounds.push({
                    freq: 800 + Math.random() * 600, // Случайная частота 800-1400Hz
                    delay: i * 0.08 + Math.random() * 0.05, // Небольшие случайные задержки
                    duration: 0.15 + Math.random() * 0.1, // Случайная длительность
                    volume: 0.3 + Math.random() * 0.2 // Случайная громкость
                });
            }

            // Воспроизводим каждую монетку
            coinSounds.forEach(coin => {
                setTimeout(() => {
                    this.createRealisticCoinSound(coin.freq, coin.duration, coin.volume);
                }, coin.delay * 1000);
            });

            // Добавляем финальный "звон" для больших наград
            if (amount >= 10) {
                setTimeout(() => {
                    this.createBellSound();
                }, (numCoins * 0.08 + 0.2) * 1000);
            }

        } catch (error) {
            // Тихо игнорируем ошибки
        }
    }

    createRealisticCoinSound(frequency, duration, volume) {
        if (!this.audioContext) return;

        const now = this.audioContext.currentTime;

        // Создаем основной тон (металлический звук)
        const oscillator1 = this.audioContext.createOscillator();
        const oscillator2 = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();
        const filter = this.audioContext.createBiquadFilter();

        // Основная частота
        oscillator1.type = 'triangle';
        oscillator1.frequency.setValueAtTime(frequency, now);
        oscillator1.frequency.exponentialRampToValueAtTime(frequency * 0.7, now + duration);

        // Гармоника для металлического звука
        oscillator2.type = 'sawtooth';
        oscillator2.frequency.setValueAtTime(frequency * 2.1, now);
        oscillator2.frequency.exponentialRampToValueAtTime(frequency * 1.4, now + duration);

        // Фильтр для металлического оттенка
        filter.type = 'bandpass';
        filter.frequency.setValueAtTime(frequency * 1.5, now);
        filter.Q.setValueAtTime(2, now);

        // Огибающая громкости (быстрая атака, медленное затухание)
        gainNode.gain.setValueAtTime(0, now);
        gainNode.gain.linearRampToValueAtTime(volume, now + 0.01);
        gainNode.gain.exponentialRampToValueAtTime(0.001, now + duration);

        // Соединяем цепочку
        oscillator1.connect(filter);
        oscillator2.connect(gainNode);
        filter.connect(gainNode);
        gainNode.connect(this.audioContext.destination);

        // Запускаем
        oscillator1.start(now);
        oscillator2.start(now);
        oscillator1.stop(now + duration);
        oscillator2.stop(now + duration);
    }

    createBellSound() {
        if (!this.audioContext) return;

        const now = this.audioContext.currentTime;
        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();

        // Звон колокольчика
        oscillator.type = 'sine';
        oscillator.frequency.setValueAtTime(1200, now);
        oscillator.frequency.exponentialRampToValueAtTime(800, now + 0.5);

        gainNode.gain.setValueAtTime(0.2, now);
        gainNode.gain.exponentialRampToValueAtTime(0.001, now + 0.5);

        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);

        oscillator.start(now);
        oscillator.stop(now + 0.5);
    }

    // Метод для воспроизведения звука
    play(amount = 10) {
        this.generateCoinsSound(amount);
    }
}

// Интегрируем с основным аудио менеджером
document.addEventListener('DOMContentLoaded', () => {
    // Создаем генератор звука как запасной вариант
    window.coinsSoundGenerator = new CoinsSoundGenerator();
    
    // Если основной аудио менеджер не может загрузить звук, используем генератор
    const originalPlayCoinsSound = window.playCoinsSound;
    
    window.playCoinsSound = (amount = 10) => {
        // Сначала пробуем основной звук
        if (window.coinsAudio && window.coinsAudio.sounds['coins']) {
            originalPlayCoinsSound(amount);
        } else {
            // Если нет файла, используем генератор
            window.coinsSoundGenerator.play(amount);
        }
    };


});
