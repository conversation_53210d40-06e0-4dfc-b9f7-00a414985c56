/**
 * app.js
 * Main JavaScript file for UniQPaid Telegram Mini App
 * 
 * This file handles all frontend functionality including:
 * - Telegram WebApp integration
 * - API communication
 * - User interface management
 * - Ad viewing and rewards
 */

class UniQPaidApp {
    constructor() {
        this.tg = window.Telegram?.WebApp;
        this.currentUser = null;
        this.appSettings = null;
        this.isInitialized = false;
        
        // API endpoints
        this.apiBase = '/api';
        
        // UI elements
        this.elements = {};
        
        // Ad counters
        this.adCounters = {
            native_banner: 0,
            interstitial: 0,
            rewarded_video: 0
        };
    }

    /**
     * Initialize the application
     */
    async init() {
        console.log('🚀 Initializing UniQPaid App...');
        
        try {
            // Initialize Telegram WebApp
            this.initTelegramWebApp();
            
            // Initialize UI elements
            this.initUIElements();
            
            // Load app settings
            await this.loadAppSettings();
            
            // Load user data
            await this.loadUserData();
            
            // Initialize event handlers
            this.initEventHandlers();
            
            // Initialize navigation
            this.initNavigation();
            
            // Initialize RichAds
            this.initRichAds();
            
            this.isInitialized = true;
            this.showStatus('Приложение готово к работе', 'success');
            
            console.log('✅ UniQPaid App initialized successfully!');
            
        } catch (error) {
            console.error('❌ Failed to initialize app:', error);
            this.showStatus('Ошибка инициализации: ' + error.message, 'error');
        }
    }

    /**
     * Initialize Telegram WebApp
     */
    initTelegramWebApp() {
        if (!this.tg) {
            console.warn('⚠️ Telegram WebApp not available, running in debug mode');
            return;
        }

        this.tg.ready();
        this.tg.expand();
        
        // Set theme
        document.documentElement.style.setProperty('--tg-theme-bg-color', this.tg.themeParams.bg_color || '#1a1a2e');
        document.documentElement.style.setProperty('--tg-theme-text-color', this.tg.themeParams.text_color || '#ffffff');
        
        console.log('📱 Telegram WebApp initialized');
    }

    /**
     * Initialize UI elements
     */
    initUIElements() {
        this.elements = {
            // Status and user info
            statusMessage: document.getElementById('status-message'),
            userName: document.getElementById('user-name'),
            balanceAmount: document.getElementById('balance-amount'),
            earnBalanceAmount: document.getElementById('earn-balance-amount'),
            
            // Ad buttons
            openLinkButton: document.getElementById('openLinkButton'),
            watchVideoButton: document.getElementById('watchVideoButton'),
            openAdButton: document.getElementById('openAdButton'),
            
            // Ad counters
            nativeBannerCounter: document.getElementById('native-banner-counter'),
            interstitialCounter: document.getElementById('interstitial-counter'),
            rewardedVideoCounter: document.getElementById('rewarded-video-counter'),
            
            // Withdrawal form
            withdrawalAmount: document.getElementById('withdrawal-amount'),
            withdrawalCurrency: document.getElementById('withdrawal-currency'),
            withdrawalAddress: document.getElementById('withdrawal-address'),
            submitWithdrawal: document.getElementById('submit-withdrawal'),
            withdrawalList: document.getElementById('withdrawal-list'),
            
            // Referral
            referralLink: document.getElementById('referral-link'),
            copyReferralLink: document.getElementById('copy-referral-link'),
            referralCount: document.getElementById('referral-count'),
            referralEarnings: document.getElementById('referral-earnings'),
            
            // Navigation
            navButtons: document.querySelectorAll('.nav-button'),
            sections: document.querySelectorAll('.app-section')
        };
    }

    /**
     * Load application settings
     */
    async loadAppSettings() {
        try {
            const response = await fetch(`${this.apiBase}/settings`);
            const result = await response.json();
            
            if (result.success) {
                this.appSettings = result.data;
                console.log('⚙️ App settings loaded:', this.appSettings);
            } else {
                throw new Error(result.data?.error || 'Failed to load settings');
            }
        } catch (error) {
            console.error('Failed to load app settings:', error);
            throw error;
        }
    }

    /**
     * Load user data
     */
    async loadUserData() {
        try {
            const initData = this.getInitData();
            
            const response = await fetch(`${this.apiBase}/user/data`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ initData })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.currentUser = result.data;
                this.updateUserInterface();
                console.log('👤 User data loaded:', this.currentUser);
            } else {
                throw new Error(result.data?.error || 'Failed to load user data');
            }
        } catch (error) {
            console.error('Failed to load user data:', error);
            throw error;
        }
    }

    /**
     * Get Telegram initData
     */
    getInitData() {
        if (this.tg && this.tg.initData) {
            return this.tg.initData;
        }
        
        // Fallback for development
        const testUser = JSON.stringify({
            id: 12345,
            first_name: "Test",
            username: "testuser",
            language_code: "en"
        });
        
        return `user=${encodeURIComponent(testUser)}&chat_instance=test&chat_type=private&auth_date=${Math.floor(Date.now() / 1000)}&hash=test`;
    }

    /**
     * Update user interface with loaded data
     */
    updateUserInterface() {
        if (!this.currentUser) return;
        
        // Update user info
        if (this.elements.userName) {
            this.elements.userName.textContent = this.currentUser.user.first_name || 'Пользователь';
        }
        
        // Update balance
        const balance = this.currentUser.user.balance || 0;
        if (this.elements.balanceAmount) {
            this.elements.balanceAmount.textContent = balance.toString();
        }
        if (this.elements.earnBalanceAmount) {
            this.elements.earnBalanceAmount.textContent = balance.toString();
        }
        
        // Update ad counters
        if (this.currentUser.ads) {
            Object.keys(this.currentUser.ads).forEach(adType => {
                const adData = this.currentUser.ads[adType];
                const counterElement = this.elements[`${adType.replace('_', '')}Counter`];
                
                if (counterElement && adData) {
                    const todayViews = adData.today_views || 0;
                    const maxViews = this.getAdLimit(adType);
                    counterElement.textContent = `${todayViews}/${maxViews}`;
                    
                    // Update button state
                    const button = this.getAdButton(adType);
                    if (button) {
                        button.disabled = !adData.can_view;
                        if (!adData.can_view) {
                            button.classList.add('disabled');
                        } else {
                            button.classList.remove('disabled');
                        }
                    }
                }
            });
        }
        
        // Update referral info
        if (this.currentUser.referral) {
            const referralData = this.currentUser.referral;
            if (this.elements.referralCount) {
                this.elements.referralCount.textContent = referralData.total_referrals || 0;
            }
            if (this.elements.referralEarnings) {
                this.elements.referralEarnings.textContent = referralData.total_bonus || 0;
            }
        }
        
        // Generate referral link
        this.generateReferralLink();
    }

    /**
     * Get ad limit for type
     */
    getAdLimit(adType) {
        if (!this.appSettings) return 0;
        
        const limits = this.appSettings.daily_limits || {};
        return limits[adType] || 0;
    }

    /**
     * Get ad button element
     */
    getAdButton(adType) {
        const buttonMap = {
            'native_banner': this.elements.openLinkButton,
            'interstitial': this.elements.openAdButton,
            'rewarded_video': this.elements.watchVideoButton
        };
        
        return buttonMap[adType];
    }

    /**
     * Initialize event handlers
     */
    initEventHandlers() {
        // Ad buttons
        if (this.elements.openLinkButton) {
            this.elements.openLinkButton.addEventListener('click', () => this.handleAdClick('native_banner'));
        }
        
        if (this.elements.watchVideoButton) {
            this.elements.watchVideoButton.addEventListener('click', () => this.handleAdClick('rewarded_video'));
        }
        
        if (this.elements.openAdButton) {
            this.elements.openAdButton.addEventListener('click', () => this.handleAdClick('interstitial'));
        }
        
        // Withdrawal form
        if (this.elements.submitWithdrawal) {
            this.elements.submitWithdrawal.addEventListener('click', () => this.handleWithdrawal());
        }
        
        // Copy referral link
        if (this.elements.copyReferralLink) {
            this.elements.copyReferralLink.addEventListener('click', () => this.copyReferralLink());
        }
    }

    /**
     * Initialize navigation
     */
    initNavigation() {
        this.elements.navButtons.forEach(button => {
            button.addEventListener('click', () => {
                const sectionId = button.dataset.section;
                this.showSection(sectionId);
                
                // Update active nav button
                this.elements.navButtons.forEach(btn => btn.classList.remove('active'));
                button.classList.add('active');
            });
        });
    }

    /**
     * Show specific section
     */
    showSection(sectionId) {
        this.elements.sections.forEach(section => {
            if (section.id === sectionId) {
                section.classList.add('active-section');
                section.classList.remove('page-hidden');
            } else {
                section.classList.remove('active-section');
                section.classList.add('page-hidden');
            }
        });
    }

    /**
     * Initialize RichAds
     */
    initRichAds() {
        // RichAds initialization will be added here
        console.log('🎯 RichAds initialized');
    }

    /**
     * Handle ad click
     */
    async handleAdClick(adType) {
        try {
            this.showStatus('Обработка просмотра рекламы...', 'info');
            
            // Here would be the actual ad display logic
            // For now, we'll simulate it
            await this.simulateAdView(adType);
            
            // Record ad view
            await this.recordAdView(adType);
            
        } catch (error) {
            console.error('Ad click error:', error);
            this.showStatus('Ошибка при просмотре рекламы: ' + error.message, 'error');
        }
    }

    /**
     * Simulate ad view (placeholder)
     */
    async simulateAdView(adType) {
        return new Promise(resolve => {
            setTimeout(() => {
                console.log(`Simulated ${adType} ad view`);
                resolve();
            }, 1000);
        });
    }

    /**
     * Record ad view via API
     */
    async recordAdView(adType, adToken = null) {
        try {
            const initData = this.getInitData();
            
            const response = await fetch(`${this.apiBase}/ad/view`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    initData,
                    ad_type: adType,
                    ad_token: adToken,
                    session_id: this.generateSessionId()
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                const reward = result.data.reward;
                this.showStatus(`Получено ${reward} монет за просмотр рекламы!`, 'success');
                
                // Reload user data to update balance and counters
                await this.loadUserData();
                
                // Play coin sound effect
                this.playCoinSound();
                
            } else {
                throw new Error(result.data?.error || 'Failed to record ad view');
            }
        } catch (error) {
            console.error('Failed to record ad view:', error);
            throw error;
        }
    }

    /**
     * Handle withdrawal request
     */
    async handleWithdrawal() {
        try {
            const amount = parseInt(this.elements.withdrawalAmount.value);
            const currency = this.elements.withdrawalCurrency.value;
            const address = this.elements.withdrawalAddress.value.trim();
            
            if (!amount || amount < 100) {
                this.showStatus('Минимальная сумма для вывода: 100 монет', 'error');
                return;
            }
            
            if (!address) {
                this.showStatus('Введите адрес кошелька', 'error');
                return;
            }
            
            this.showStatus('Обработка запроса на вывод...', 'info');
            
            const initData = this.getInitData();
            
            const response = await fetch(`${this.apiBase}/withdrawal/request`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    initData,
                    amount,
                    currency,
                    address
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showStatus('Запрос на вывод создан успешно!', 'success');
                
                // Clear form
                this.elements.withdrawalAmount.value = '';
                this.elements.withdrawalAddress.value = '';
                
                // Reload user data
                await this.loadUserData();
                
                // Load withdrawal history
                await this.loadWithdrawalHistory();
                
            } else {
                throw new Error(result.data?.error || 'Failed to create withdrawal request');
            }
        } catch (error) {
            console.error('Withdrawal error:', error);
            this.showStatus('Ошибка при создании запроса: ' + error.message, 'error');
        }
    }

    /**
     * Load withdrawal history
     */
    async loadWithdrawalHistory() {
        try {
            const initData = this.getInitData();
            
            const response = await fetch(`${this.apiBase}/withdrawal/history`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ initData, limit: 10 })
            });
            
            const result = await response.json();
            
            if (result.success && this.elements.withdrawalList) {
                const withdrawals = result.data.withdrawals || [];
                
                if (withdrawals.length === 0) {
                    this.elements.withdrawalList.innerHTML = '<p>История выводов пуста</p>';
                } else {
                    this.elements.withdrawalList.innerHTML = withdrawals.map(w => `
                        <div class="withdrawal-item">
                            <div class="withdrawal-info">
                                <span class="amount">${w.amount} монет → ${w.crypto_amount} ${w.currency}</span>
                                <span class="status status-${w.status}">${this.getStatusText(w.status)}</span>
                            </div>
                            <div class="withdrawal-date">${new Date(w.requested_at * 1000).toLocaleDateString()}</div>
                        </div>
                    `).join('');
                }
            }
        } catch (error) {
            console.error('Failed to load withdrawal history:', error);
        }
    }

    /**
     * Get status text in Russian
     */
    getStatusText(status) {
        const statusMap = {
            'pending': 'Ожидание',
            'processing': 'Обработка',
            'completed': 'Завершено',
            'failed': 'Ошибка',
            'cancelled': 'Отменено'
        };
        
        return statusMap[status] || status;
    }

    /**
     * Generate referral link
     */
    generateReferralLink() {
        if (!this.currentUser || !this.elements.referralLink) return;
        
        const userId = this.currentUser.user.id;
        const botUsername = this.appSettings?.app_info?.bot_username || 'UniQPaidBot';
        const referralLink = `https://t.me/${botUsername}?start=ref_${userId}`;
        
        this.elements.referralLink.value = referralLink;
    }

    /**
     * Copy referral link to clipboard
     */
    async copyReferralLink() {
        try {
            if (this.elements.referralLink) {
                await navigator.clipboard.writeText(this.elements.referralLink.value);
                this.showStatus('Реферальная ссылка скопирована!', 'success');
            }
        } catch (error) {
            console.error('Failed to copy referral link:', error);
            this.showStatus('Ошибка при копировании ссылки', 'error');
        }
    }

    /**
     * Generate session ID
     */
    generateSessionId() {
        return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * Play coin sound effect
     */
    playCoinSound() {
        try {
            // Create audio element for coin sound
            const audio = new Audio('../example/audio/zvuk-monety.mp3');
            audio.volume = 0.3;
            audio.play().catch(e => console.log('Could not play sound:', e));
        } catch (error) {
            console.log('Sound not available:', error);
        }
    }

    /**
     * Show status message
     */
    showStatus(message, type = 'info') {
        if (!this.elements.statusMessage) return;
        
        this.elements.statusMessage.textContent = message;
        this.elements.statusMessage.className = `status-message status-${type}`;
        
        // Auto-hide after 5 seconds for success/error messages
        if (type === 'success' || type === 'error') {
            setTimeout(() => {
                if (this.elements.statusMessage) {
                    this.elements.statusMessage.textContent = '';
                    this.elements.statusMessage.className = 'status-message';
                }
            }, 5000);
        }
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    const app = new UniQPaidApp();
    app.init();
    
    // Make app globally available for debugging
    window.UniQPaidApp = app;
});
