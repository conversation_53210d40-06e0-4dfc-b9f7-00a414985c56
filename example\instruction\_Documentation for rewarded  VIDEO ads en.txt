Telegram
Instructions for Custom Trigger Setup

Step 1: Verifying the Connection of the TG Script
Please, make sure your app’s website is connected to tg via the script below:
<script src="https://telegram.org/js/telegram-web-app.js?56"></script>

According to the following documentation: 
https://core.telegram.org/bots/webapps#initializing-mini-apps


Step 2: Library Connection
Add the script to load the TelegramAdsController library into your HTML file. Make sure that the specified path is correct:
<script src="https://richinfo.co/richpartners/telegram/js/tg-ob.js"></script>
This script loads the TelegramAdsController class, which will be used to manage advertising functionality.

Step 3: Initializing the Controller

window.TelegramAdsController = new TelegramAdsController(); 

window.TelegramAdsController.initialize({ 

 pubId: "12345", 

 appId: "54321", 

});
Parameters for initialize:
●pubId (required): The unique identifier of the publisher.
●appId (required): The application identifier.
●debug (optional): Enables debug mode to display additional information in the console.
●
Step 4: Setting Up an Event Handler
Add a click event handler for the button (the trigger can be any event). On click, the .triggerInterstitialVideo method is called to display the ad:
  const button = document.getElementById('ad-button');
    button?.addEventListener('click', () => {
        window.TelegramAdsController..triggerInterstitialVideo().then((result) => {
           console.log(result);
        }).catch((result) => {
            console.log(result);
        })
    })
- 

for processing rewarded variable response result:

resolve 
reject

5. Debug Mode

When debug: true is enabled, test ads will be returned for display.
To disable the automatic ad display, contact the manager. In the loaded settings, automatic ad display on click will be turned off.
Example of script integration:


<script src="https://richinfo.co/richpartners/telegram/js/tg-ob.js"></script>
<script>
    window.TelegramAdsController = new TelegramAdsController();
    window.TelegramAdsController.initialize({
        pubId: "792361",
        appId: "1396",
    });
    window.TelegramAdsController..triggerInterstitialVideo().then((result) => {
        console.log(result);
    }).catch((result) => {
        console.log(result);
    });
</script>
