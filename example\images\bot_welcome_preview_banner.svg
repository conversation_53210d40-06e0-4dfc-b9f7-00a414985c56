<svg width="800" height="400" viewBox="0 0 800 400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Светлый кремовый фон как в примере -->
    <linearGradient id="lightBg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFF8E1;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#FFFDE7;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FFF3C4;stop-opacity:1" />
    </linearGradient>

    <!-- Золотистый градиент для монет и акцентов -->
    <linearGradient id="goldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#FFC107;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FF8F00;stop-opacity:1" />
    </linearGradient>

    <!-- Темно-золотистый для контуров -->
    <linearGradient id="darkGoldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF8F00;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#F57C00;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#E65100;stop-opacity:1" />
    </linearGradient>

    <!-- Градиент для Bitcoin -->
    <linearGradient id="bitcoinGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F7931A;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FF8F00;stop-opacity:1" />
    </linearGradient>

    <!-- Градиент для Ethereum -->
    <linearGradient id="ethereumGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#627EEA;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#4A6CF7;stop-opacity:1" />
    </linearGradient>

    <!-- Градиент для USDT -->
    <linearGradient id="usdtGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#26A17B;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1E8E3E;stop-opacity:1" />
    </linearGradient>

    <!-- Градиент для TON -->
    <linearGradient id="tonGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0088CC;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#005F8C;stop-opacity:1" />
    </linearGradient>

    <!-- Тень для элементов -->
    <filter id="softShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="4" stdDeviation="3" flood-color="#000000" flood-opacity="0.15"/>
    </filter>
  </defs>

  <!-- Светлый кремовый фон как в примере -->
  <rect width="800" height="400" fill="url(#lightBg)"/>

  <!-- Декоративные элементы фона -->
  <circle cx="100" cy="80" r="40" fill="#FFD700" opacity="0.1"/>
  <circle cx="700" cy="320" r="60" fill="#FFC107" opacity="0.1"/>
  <circle cx="650" cy="100" r="25" fill="#FF8F00" opacity="0.15"/>

  <!-- Главный заголовок UniQPaid -->
  <text x="400" y="70" text-anchor="middle" font-family="Arial, sans-serif" font-size="48" font-weight="bold" fill="#2C2C2C">UniQPaid</text>

  <!-- Подзаголовок -->
  <text x="400" y="100" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="500" fill="#5D4037">Watch ads and earn crypto</text>

  <!-- Красивые криптомонеты в стиле примера -->

  <!-- Bitcoin - левая верхняя -->
  <g transform="translate(150, 180)">
    <circle cx="0" cy="0" r="35" fill="url(#bitcoinGradient)" filter="url(#softShadow)"/>
    <circle cx="0" cy="0" r="35" fill="none" stroke="#E65100" stroke-width="2"/>
    <!-- Bitcoin символ ₿ -->
    <text x="0" y="8" text-anchor="middle" font-family="Arial, sans-serif" font-size="32" font-weight="bold" fill="#FFFFFF">₿</text>
    <text x="0" y="55" text-anchor="middle" font-family="Arial, sans-serif" font-size="13" font-weight="600" fill="#5D4037">Bitcoin</text>
  </g>

  <!-- Ethereum - правая верхняя -->
  <g transform="translate(650, 180)">
    <circle cx="0" cy="0" r="35" fill="url(#ethereumGradient)" filter="url(#softShadow)"/>
    <circle cx="0" cy="0" r="35" fill="none" stroke="#2E3B8F" stroke-width="2"/>
    <!-- Ethereum символ -->
    <polygon points="0,-20 -12,2 0,8 12,2" fill="#FFFFFF"/>
    <polygon points="0,8 -12,2 0,20 12,2" fill="#FFFFFF" opacity="0.8"/>
    <text x="0" y="55" text-anchor="middle" font-family="Arial, sans-serif" font-size="13" font-weight="600" fill="#5D4037">Ethereum</text>
  </g>

  <!-- USDT - левая нижняя -->
  <g transform="translate(200, 280)">
    <circle cx="0" cy="0" r="30" fill="url(#usdtGradient)" filter="url(#softShadow)"/>
    <circle cx="0" cy="0" r="30" fill="none" stroke="#1B5E20" stroke-width="2"/>
    <!-- USDT символ -->
    <text x="0" y="8" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#FFFFFF">₮</text>
    <text x="0" y="50" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="600" fill="#5D4037">USDT</text>
  </g>

  <!-- TON - правая нижняя -->
  <g transform="translate(600, 280)">
    <circle cx="0" cy="0" r="30" fill="url(#tonGradient)" filter="url(#softShadow)"/>
    <circle cx="0" cy="0" r="30" fill="none" stroke="#004D6B" stroke-width="2"/>
    <!-- TON символ -->
    <polygon points="-12,-8 12,-8 8,8 -8,8" fill="#FFFFFF"/>
    <circle cx="0" cy="0" r="4" fill="#FFFFFF"/>
    <text x="0" y="50" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="600" fill="#5D4037">TON</text>
  </g>

  <!-- Центральная большая золотая монета -->
  <g transform="translate(400, 200)">
    <circle cx="0" cy="0" r="50" fill="url(#goldGradient)" filter="url(#softShadow)"/>
    <circle cx="0" cy="0" r="50" fill="none" stroke="#E65100" stroke-width="3"/>
    <circle cx="0" cy="0" r="35" fill="none" stroke="#FF8F00" stroke-width="1" opacity="0.6"/>
    <!-- Символ доллара -->
    <text x="0" y="12" text-anchor="middle" font-family="Arial, sans-serif" font-size="40" font-weight="bold" fill="#FFFFFF">$</text>
    <!-- Маленькие декоративные элементы -->
    <circle cx="-25" cy="-25" r="3" fill="#FFD700"/>
    <circle cx="25" cy="-25" r="3" fill="#FFD700"/>
    <circle cx="-25" cy="25" r="3" fill="#FFD700"/>
    <circle cx="25" cy="25" r="3" fill="#FFD700"/>
  </g>

  <!-- Декоративные соединительные линии -->
  <path d="M185 180 Q300 160 350 200" stroke="#FFD700" stroke-width="2" fill="none" opacity="0.6"/>
  <path d="M615 180 Q500 160 450 200" stroke="#FFD700" stroke-width="2" fill="none" opacity="0.6"/>
  <path d="M230 280 Q320 260 350 200" stroke="#FFD700" stroke-width="2" fill="none" opacity="0.6"/>
  <path d="M570 280 Q480 260 450 200" stroke="#FFD700" stroke-width="2" fill="none" opacity="0.6"/>

  <!-- Иконки интерфейса как в примере -->
  <!-- Play button -->
  <g transform="translate(120, 160)">
    <rect x="0" y="0" width="40" height="30" rx="5" fill="#FFD700" filter="url(#softShadow)"/>
    <polygon points="15,10 15,20 25,15" fill="#FFFFFF"/>
  </g>

  <!-- Hash/Number symbol -->
  <g transform="translate(680, 160)">
    <circle cx="20" cy="20" r="20" fill="#FFD700" filter="url(#softShadow)"/>
    <text x="20" y="26" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#FFFFFF">#</text>
  </g>

  <!-- Маленькие золотые монетки -->
  <g transform="translate(300, 160)">
    <circle cx="0" cy="0" r="12" fill="url(#goldGradient)" filter="url(#softShadow)"/>
    <text x="0" y="4" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#FFFFFF">$</text>
  </g>

  <g transform="translate(500, 160)">
    <circle cx="0" cy="0" r="12" fill="url(#goldGradient)" filter="url(#softShadow)"/>
    <text x="0" y="4" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#FFFFFF">$</text>
  </g>

  <!-- Призыв к действию -->
  <text x="400" y="350" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="600" fill="#5D4037">Start earning crypto rewards today!</text>
</svg>
