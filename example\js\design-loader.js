/**
 * Загрузчик настроек дизайна
 * Применяет настройки дизайна из админки к основному приложению
 */

class DesignLoader {
    constructor() {
        this.defaultSettings = {
            colors: {
                primary_dark: '#2a2a2a',
                accent_orange: '#ff6b35',
                bg_card: '#3a3a3a',
                text_primary: '#ffffff',
                bg_secondary: '#333333',
                border_color: '#4a4a4a'
            },
            effects: {
                glitch_speed: 3,
                glitch_count: 3,
                glitch_opacity: 1,
                bg_opacity: 0.8,
                geometric_size: 1,
                enable_glitch: true,
                enable_geometric: true
            },
            theme: 'geometric'
        };
        
        this.init();
    }
    
    async init() {
        try {
            await this.loadDesignSettings();
            this.applyDesignSettings();
            console.log('🎨 Geometric design loaded successfully!');
        } catch (error) {
            console.warn('Design settings not found, using defaults:', error);
            this.applyDesignSettings();
        }
    }
    
    async loadDesignSettings() {
        try {
            const response = await fetch('./api/admin/design_settings.json');
            if (response.ok) {
                const settings = await response.json();
                this.settings = this.mergeSettings(this.defaultSettings, settings);
            } else {
                throw new Error('Settings file not found');
            }
        } catch (error) {
            // Пробуем загрузить из localStorage (для тестирования)
            const localSettings = localStorage.getItem('designSettings');
            if (localSettings) {
                const settings = JSON.parse(localSettings);
                this.settings = this.mergeSettings(this.defaultSettings, settings);
            } else {
                this.settings = this.defaultSettings;
            }
        }
    }
    
    mergeSettings(defaults, custom) {
        const merged = JSON.parse(JSON.stringify(defaults));
        
        if (custom.colors) {
            Object.assign(merged.colors, custom.colors);
        }
        
        if (custom.effects) {
            Object.assign(merged.effects, custom.effects);
        }
        
        if (custom.theme) {
            merged.theme = custom.theme;
        }
        
        return merged;
    }
    
    applyDesignSettings() {
        this.applyColors();
        this.applyEffects();
        this.applyTheme();
    }
    
    applyColors() {
        const colors = this.settings.colors;
        const root = document.documentElement;
        
        // Применяем основные цвета
        root.style.setProperty('--primary-dark', colors.primary_dark);
        root.style.setProperty('--accent-orange', colors.accent_orange);
        root.style.setProperty('--bg-card', colors.bg_card);
        root.style.setProperty('--text-primary', colors.text_primary);
        root.style.setProperty('--bg-secondary', colors.bg_secondary);
        root.style.setProperty('--border-color', colors.border_color);
        
        // Вычисляем дополнительные цвета
        const accentLight = this.lightenColor(colors.accent_orange, 20);
        const accentDark = this.darkenColor(colors.accent_orange, 20);
        
        root.style.setProperty('--accent-orange-light', accentLight);
        root.style.setProperty('--accent-orange-dark', accentDark);
        
        // Обновляем градиенты
        root.style.setProperty('--gradient-orange', 
            `linear-gradient(135deg, ${colors.accent_orange} 0%, ${accentLight} 100%)`);
        root.style.setProperty('--gradient-geometric', 
            `linear-gradient(45deg, ${colors.accent_orange} 0%, ${accentDark} 50%, ${accentLight} 100%)`);
        root.style.setProperty('--gradient-dark', 
            `linear-gradient(135deg, ${colors.primary_dark} 0%, ${colors.bg_secondary} 100%)`);
        
        // Обновляем совместимые переменные
        root.style.setProperty('--app-primary-color', colors.primary_dark);
        root.style.setProperty('--accent-primary', colors.accent_orange);
        root.style.setProperty('--accent-neon', accentLight);
        root.style.setProperty('--shadow-orange', `rgba(255, 107, 53, 0.5)`);
        root.style.setProperty('--shadow-neon', `rgba(255, 140, 90, 0.5)`);
        
        // Обновляем фон
        root.style.setProperty('--bg-primary', colors.primary_dark);
        
        console.log('🎨 Colors applied:', colors);
    }
    
    applyEffects() {
        const effects = this.settings.effects;
        
        // Применяем глитч-эффекты
        if (effects.enable_glitch) {
            this.updateGlitchEffects(effects);
        } else {
            this.disableGlitchEffects();
        }
        
        // Применяем геометрические эффекты
        if (effects.enable_geometric) {
            this.updateGeometricEffects(effects);
        } else {
            this.disableGeometricEffects();
        }
        
        console.log('⚡ Effects applied:', effects);
    }
    
    updateGlitchEffects(effects) {
        // Удаляем существующие линии
        const existingLines = document.querySelectorAll('.glitch-line');
        existingLines.forEach(line => line.remove());
        
        // Создаем новые линии
        for (let i = 0; i < effects.glitch_count; i++) {
            const line = document.createElement('div');
            line.className = 'glitch-line';
            line.style.animationDuration = effects.glitch_speed + 's';
            line.style.animationDelay = (i * 1) + 's';
            line.style.top = (20 + (i * 60 / effects.glitch_count)) + '%';
            line.style.opacity = effects.glitch_opacity;
            document.body.appendChild(line);
        }
    }
    
    disableGlitchEffects() {
        const glitchLines = document.querySelectorAll('.glitch-line');
        glitchLines.forEach(line => line.style.display = 'none');
    }
    
    updateGeometricEffects(effects) {
        const style = document.createElement('style');
        style.id = 'geometric-effects-style';
        style.textContent = `
            body::before { 
                opacity: ${effects.bg_opacity} !important; 
                transform: scale(${effects.geometric_size}) !important; 
            }
            body::after { 
                opacity: ${effects.bg_opacity * 0.3} !important; 
                transform: rotate(15deg) scale(${effects.geometric_size}) !important; 
            }
        `;
        
        // Удаляем предыдущий стиль
        const oldStyle = document.getElementById('geometric-effects-style');
        if (oldStyle) oldStyle.remove();
        
        document.head.appendChild(style);
    }
    
    disableGeometricEffects() {
        const style = document.createElement('style');
        style.id = 'geometric-effects-style';
        style.textContent = `
            body::before, body::after { 
                display: none !important; 
            }
        `;
        
        const oldStyle = document.getElementById('geometric-effects-style');
        if (oldStyle) oldStyle.remove();
        
        document.head.appendChild(style);
    }
    
    applyTheme() {
        const theme = this.settings.theme;
        document.body.className = `theme-${theme}`;
        
        // Добавляем специфичные для темы стили
        const themeStyle = document.createElement('style');
        themeStyle.id = 'theme-style';
        
        switch (theme) {
            case 'geometric':
                themeStyle.textContent = `
                    .theme-geometric .action-button {
                        border-radius: 8px;
                        background: var(--gradient-orange);
                        margin-bottom: 20px !important;
                    }
                    .theme-geometric .action-button:last-child {
                        margin-bottom: 0 !important;
                    }
                    .theme-geometric .earn-block,
                    .theme-geometric .friends-block {
                        border-radius: 12px;
                        border-top: 3px solid var(--accent-orange);
                    }
                `;
                break;
            case 'cyberpunk':
                themeStyle.textContent = `
                    .theme-cyberpunk .action-button {
                        border-radius: 4px;
                        background: var(--gradient-orange);
                        box-shadow: 0 0 20px rgba(255, 107, 53, 0.5);
                    }
                    .theme-cyberpunk .earn-block,
                    .theme-cyberpunk .friends-block {
                        border-radius: 4px;
                        border: 1px solid var(--accent-orange);
                        box-shadow: 0 0 15px rgba(255, 107, 53, 0.3);
                    }
                `;
                break;
            case 'minimal':
                themeStyle.textContent = `
                    .theme-minimal .action-button {
                        border-radius: 24px;
                        background: var(--accent-orange);
                        box-shadow: none;
                    }
                    .theme-minimal .earn-block,
                    .theme-minimal .friends-block {
                        border-radius: 16px;
                        border: 1px solid var(--border-color);
                        box-shadow: none;
                    }
                `;
                break;
        }
        
        const oldThemeStyle = document.getElementById('theme-style');
        if (oldThemeStyle) oldThemeStyle.remove();
        
        document.head.appendChild(themeStyle);
        
        console.log('🎭 Theme applied:', theme);
    }
    
    // Вспомогательные функции для работы с цветами
    lightenColor(color, percent) {
        const num = parseInt(color.replace("#", ""), 16);
        const amt = Math.round(2.55 * percent);
        const R = (num >> 16) + amt;
        const G = (num >> 8 & 0x00FF) + amt;
        const B = (num & 0x0000FF) + amt;
        return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
            (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
            (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
    }
    
    darkenColor(color, percent) {
        const num = parseInt(color.replace("#", ""), 16);
        const amt = Math.round(2.55 * percent);
        const R = (num >> 16) - amt;
        const G = (num >> 8 & 0x00FF) - amt;
        const B = (num & 0x0000FF) - amt;
        return "#" + (0x1000000 + (R > 255 ? 255 : R < 0 ? 0 : R) * 0x10000 +
            (G > 255 ? 255 : G < 0 ? 0 : G) * 0x100 +
            (B > 255 ? 255 : B < 0 ? 0 : B)).toString(16).slice(1);
    }
}

// Инициализируем загрузчик дизайна
document.addEventListener('DOMContentLoaded', () => {
    window.designLoader = new DesignLoader();
});

// Экспортируем для использования в других скриптах
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DesignLoader;
}
