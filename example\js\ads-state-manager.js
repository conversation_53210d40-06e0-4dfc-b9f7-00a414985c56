/**
 * js/ads-state-manager.js
 * Централизованное управление состоянием рекламной системы
 * Реактивная система состояния с подписками на изменения
 */

class AdsStateManager {
  constructor() {
    console.log('[AdsStateManager] 🏗️ Инициализация менеджера состояния...');
    
    // Основное состояние
    this.state = {
      // Глобальное состояние системы
      system: {
        isInitialized: false,
        isAdShowing: false,
        currentLanguage: 'ru',
        lastAdShownTime: 0,
        sessionId: null
      },
      
      // Состояние кнопок рекламы
      buttons: new Map(),
      
      // Лимиты пользователя
      limits: new Map(),
      
      // Активные таймеры cooldown
      cooldowns: new Map(),
      
      // Конфигурация
      config: null,
      
      // Ошибки
      errors: []
    };
    
    // Подписчики на изменения состояния
    this.subscribers = new Map();
    
    // Middleware для обработки изменений
    this.middleware = [];
    
    // История изменений (для отладки)
    this.history = [];
    this.maxHistorySize = 100;
  }
  
  /**
   * Инициализация менеджера состояния
   */
  init(config = null) {
    console.log('[AdsStateManager] 🚀 Инициализация...');
    
    // Устанавливаем конфигурацию
    if (config) {
      this.setState('config', config);
    }
    
    // Генерируем ID сессии
    this.setState('system.sessionId', this.generateSessionId());
    
    // Определяем язык
    this.setState('system.currentLanguage', this.detectLanguage());
    
    // Инициализируем состояние кнопок
    this.initializeButtonStates();
    
    // Помечаем как инициализированный
    this.setState('system.isInitialized', true);
    
    console.log('[AdsStateManager] ✅ Инициализация завершена');
  }
  
  /**
   * Инициализация состояния кнопок
   */
  initializeButtonStates() {
    const adTypes = this.getAdTypes();
    
    adTypes.forEach(adType => {
      this.setState(`buttons.${adType.id}`, {
        id: adType.id,
        state: 'ready', // ready, loading, cooldown, disabled, limit_reached
        text: '',
        isEnabled: true,
        lastClickTime: 0,
        clickCount: 0
      });
      
      this.setState(`limits.${adType.id}`, {
        current: 0,
        limit: this.getConfig()?.LIMITS?.DAILY_LIMIT_PER_TYPE || 20,
        remaining: this.getConfig()?.LIMITS?.DAILY_LIMIT_PER_TYPE || 20,
        isLimitReached: false
      });
    });
  }
  
  /**
   * Получение состояния
   */
  getState(path = null) {
    if (!path) {
      return this.cloneState(this.state);
    }
    
    return this.getNestedValue(this.state, path);
  }
  
  /**
   * Установка состояния
   */
  setState(path, value) {
    const oldValue = this.getNestedValue(this.state, path);
    
    // Применяем middleware
    const processedValue = this.applyMiddleware(path, value, oldValue);
    
    // Устанавливаем новое значение
    this.setNestedValue(this.state, path, processedValue);
    
    // Записываем в историю
    this.addToHistory(path, oldValue, processedValue);
    
    // Уведомляем подписчиков
    this.notifySubscribers(path, processedValue, oldValue);
    
    console.log(`[AdsStateManager] 📝 Состояние обновлено: ${path} =`, processedValue);
  }
  
  /**
   * Подписка на изменения состояния
   */
  subscribe(path, callback) {
    if (!this.subscribers.has(path)) {
      this.subscribers.set(path, []);
    }
    
    this.subscribers.get(path).push(callback);
    
    // Возвращаем функцию отписки
    return () => {
      const callbacks = this.subscribers.get(path);
      if (callbacks) {
        const index = callbacks.indexOf(callback);
        if (index > -1) {
          callbacks.splice(index, 1);
        }
      }
    };
  }
  
  /**
   * Добавление middleware
   */
  addMiddleware(middleware) {
    this.middleware.push(middleware);
  }
  
  /**
   * Применение middleware
   */
  applyMiddleware(path, value, oldValue) {
    let processedValue = value;
    
    for (const middleware of this.middleware) {
      try {
        processedValue = middleware(path, processedValue, oldValue, this.state);
      } catch (error) {
        console.error('[AdsStateManager] ❌ Ошибка в middleware:', error);
      }
    }
    
    return processedValue;
  }
  
  /**
   * Уведомление подписчиков
   */
  notifySubscribers(path, newValue, oldValue) {
    // Уведомляем точных подписчиков
    const exactSubscribers = this.subscribers.get(path);
    if (exactSubscribers) {
      exactSubscribers.forEach(callback => {
        try {
          callback(newValue, oldValue, path);
        } catch (error) {
          console.error('[AdsStateManager] ❌ Ошибка в подписчике:', error);
        }
      });
    }
    
    // Уведомляем подписчиков на родительские пути
    const pathParts = path.split('.');
    for (let i = pathParts.length - 1; i > 0; i--) {
      const parentPath = pathParts.slice(0, i).join('.');
      const parentSubscribers = this.subscribers.get(parentPath);
      
      if (parentSubscribers) {
        const parentValue = this.getNestedValue(this.state, parentPath);
        parentSubscribers.forEach(callback => {
          try {
            callback(parentValue, null, parentPath);
          } catch (error) {
            console.error('[AdsStateManager] ❌ Ошибка в родительском подписчике:', error);
          }
        });
      }
    }
  }
  
  /**
   * Получение вложенного значения
   */
  getNestedValue(obj, path) {
    if (path.includes('.')) {
      return path.split('.').reduce((current, key) => {
        if (current instanceof Map) {
          return current.get(key);
        }
        return current && current[key];
      }, obj);
    }
    
    if (obj instanceof Map) {
      return obj.get(path);
    }
    
    return obj[path];
  }
  
  /**
   * Установка вложенного значения
   */
  setNestedValue(obj, path, value) {
    if (path.includes('.')) {
      const keys = path.split('.');
      const lastKey = keys.pop();
      
      const target = keys.reduce((current, key) => {
        if (current instanceof Map) {
          if (!current.has(key)) {
            current.set(key, {});
          }
          return current.get(key);
        } else {
          if (!current[key]) {
            current[key] = {};
          }
          return current[key];
        }
      }, obj);
      
      if (target instanceof Map) {
        target.set(lastKey, value);
      } else {
        target[lastKey] = value;
      }
    } else {
      if (obj instanceof Map) {
        obj.set(path, value);
      } else {
        obj[path] = value;
      }
    }
  }
  
  /**
   * Клонирование состояния
   */
  cloneState(state) {
    if (state instanceof Map) {
      const cloned = new Map();
      for (const [key, value] of state) {
        cloned.set(key, this.cloneState(value));
      }
      return cloned;
    }
    
    if (Array.isArray(state)) {
      return state.map(item => this.cloneState(item));
    }
    
    if (state && typeof state === 'object') {
      const cloned = {};
      for (const [key, value] of Object.entries(state)) {
        cloned[key] = this.cloneState(value);
      }
      return cloned;
    }
    
    return state;
  }
  
  /**
   * Добавление в историю
   */
  addToHistory(path, oldValue, newValue) {
    this.history.push({
      timestamp: Date.now(),
      path,
      oldValue: this.cloneState(oldValue),
      newValue: this.cloneState(newValue)
    });
    
    // Ограничиваем размер истории
    if (this.history.length > this.maxHistorySize) {
      this.history.shift();
    }
  }
  
  /**
   * Получение истории изменений
   */
  getHistory(limit = 10) {
    return this.history.slice(-limit);
  }
  
  /**
   * Вспомогательные методы
   */
  
  generateSessionId() {
    return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }
  
  detectLanguage() {
    const tg = window.Telegram?.WebApp;
    if (tg && tg.initDataUnsafe && tg.initDataUnsafe.user) {
      const langCode = tg.initDataUnsafe.user.language_code;
      if (langCode && langCode.startsWith('ru')) {
        return 'ru';
      }
    }
    
    const browserLang = navigator.language || navigator.userLanguage;
    return browserLang && browserLang.startsWith('ru') ? 'ru' : 'en';
  }
  
  getConfig() {
    return this.getState('config') || window.AdsConfig;
  }
  
  getAdTypes() {
    const config = this.getConfig();
    if (config && config.getAllAdTypes) {
      return config.getAllAdTypes();
    }
    
    // Fallback
    return [
      { id: 'native_banner', buttonId: 'openLinkButton', counterId: 'native-banner-counter' },
      { id: 'rewarded_video', buttonId: 'watchVideoButton', counterId: 'rewarded-video-counter' },
      { id: 'interstitial', buttonId: 'openAdButton', counterId: 'interstitial-counter' }
    ];
  }
  
  /**
   * Методы для работы с конкретными состояниями
   */
  
  // Состояние кнопок
  getButtonState(adTypeId) {
    return this.getState(`buttons.${adTypeId}`);
  }
  
  setButtonState(adTypeId, state) {
    this.setState(`buttons.${adTypeId}.state`, state);
  }
  
  setButtonText(adTypeId, text) {
    this.setState(`buttons.${adTypeId}.text`, text);
  }
  
  setButtonEnabled(adTypeId, enabled) {
    this.setState(`buttons.${adTypeId}.isEnabled`, enabled);
  }
  
  // Лимиты
  getLimitInfo(adTypeId) {
    return this.getState(`limits.${adTypeId}`);
  }
  
  updateLimitInfo(adTypeId, limitInfo) {
    this.setState(`limits.${adTypeId}`, limitInfo);
  }
  
  // Cooldowns
  setCooldown(adTypeId, endTime) {
    this.setState(`cooldowns.${adTypeId}`, endTime);
  }
  
  removeCooldown(adTypeId) {
    const cooldowns = this.getState('cooldowns');
    if (cooldowns instanceof Map) {
      cooldowns.delete(adTypeId);
    }
  }
  
  getCooldownEndTime(adTypeId) {
    return this.getState(`cooldowns.${adTypeId}`);
  }
  
  // Системное состояние
  setAdShowing(isShowing) {
    this.setState('system.isAdShowing', isShowing);
  }
  
  isAdShowing() {
    return this.getState('system.isAdShowing');
  }
  
  setLastAdShownTime(time) {
    this.setState('system.lastAdShownTime', time);
  }
  
  getLastAdShownTime() {
    return this.getState('system.lastAdShownTime');
  }
  
  // Ошибки
  addError(error) {
    const errors = this.getState('errors') || [];
    errors.push({
      timestamp: Date.now(),
      error: error.toString(),
      stack: error.stack
    });
    this.setState('errors', errors);
  }
  
  clearErrors() {
    this.setState('errors', []);
  }
  
  getErrors() {
    return this.getState('errors') || [];
  }
}

// Создаем глобальный экземпляр
window.adsStateManager = new AdsStateManager();

console.log('[AdsStateManager] ✅ Менеджер состояния загружен');

// Экспорт для совместимости (без ES6 modules)
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AdsStateManager;
}
