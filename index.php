<?php
/**
 * index.php
 * Root index file for UniQPaid application
 *
 * This file redirects all requests to the public/ directory
 * where the actual application entry point is located.
 */

// Security check - prevent direct access if accessed via public/
if (basename(__DIR__) === 'public') {
    http_response_code(403);
    exit('Direct access not allowed');
}

// Redirect to public directory
$requestUri = $_SERVER['REQUEST_URI'] ?? '/';
$publicPath = '/public' . $requestUri;

// Check if this is already a public path to prevent infinite redirect
if (strpos($requestUri, '/public/') !== 0) {
    header('Location: ' . $publicPath, true, 301);
    exit;
}

// If we get here, something went wrong
http_response_code(500);
exit('Application error');