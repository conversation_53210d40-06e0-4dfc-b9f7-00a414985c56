/**
 * === test-tabs.js ===
 * Тестирование табов калькулятора и вывода
 */

// Функция для тестирования табов
function testWithdrawalTabs() {
  console.log('🧪 Тестирование табов калькулятора и вывода...');
  
  // Проверяем наличие кнопок
  const calculatorTab = document.getElementById('calculator-tab');
  const withdrawalTab = document.getElementById('withdrawal-tab');
  
  console.log('🔘 Кнопки табов:');
  console.log(`  calculator-tab: ${calculatorTab ? '✅ найдена' : '❌ не найдена'}`);
  console.log(`  withdrawal-tab: ${withdrawalTab ? '✅ найдена' : '❌ не найдена'}`);
  
  // Проверяем наличие секций
  const calculatorSection = document.getElementById('calculator-section');
  const withdrawalSection = document.getElementById('withdrawal-section');
  
  console.log('📄 Секции:');
  console.log(`  calculator-section: ${calculatorSection ? '✅ найдена' : '❌ не найдена'}`);
  console.log(`  withdrawal-section: ${withdrawalSection ? '✅ найдена' : '❌ не найдена'}`);
  
  if (calculatorSection) {
    console.log(`    Классы: ${calculatorSection.className}`);
    console.log(`    Видимость: ${getComputedStyle(calculatorSection).display}`);
  }
  
  if (withdrawalSection) {
    console.log(`    Классы: ${withdrawalSection.className}`);
    console.log(`    Видимость: ${getComputedStyle(withdrawalSection).display}`);
  }
  
  // Проверяем WithdrawalTabs
  if (window.withdrawalTabs) {
    console.log('🎯 WithdrawalTabs доступен');
    console.log('  Текущий таб:', window.withdrawalTabs.activeTab);
  } else {
    console.log('❌ WithdrawalTabs не найден');
  }
}

// Функция для принудительного показа калькулятора
function forceShowCalculator() {
  console.log('🔧 Принудительный показ калькулятора...');
  
  const calculatorSection = document.getElementById('calculator-section');
  const withdrawalSection = document.getElementById('withdrawal-section');
  const calculatorTab = document.getElementById('calculator-tab');
  const withdrawalTab = document.getElementById('withdrawal-tab');
  
  // Скрываем секцию вывода
  if (withdrawalSection) {
    withdrawalSection.classList.add('hidden');
    withdrawalSection.style.display = 'none';
  }
  
  // Показываем секцию калькулятора
  if (calculatorSection) {
    calculatorSection.classList.remove('hidden');
    calculatorSection.style.display = 'block';
  }
  
  // Обновляем табы
  if (calculatorTab) calculatorTab.classList.add('active');
  if (withdrawalTab) withdrawalTab.classList.remove('active');
  
  console.log('✅ Калькулятор показан принудительно');
}

// Функция для принудительного показа вывода
function forceShowWithdrawal() {
  console.log('🔧 Принудительный показ вывода...');
  
  const calculatorSection = document.getElementById('calculator-section');
  const withdrawalSection = document.getElementById('withdrawal-section');
  const calculatorTab = document.getElementById('calculator-tab');
  const withdrawalTab = document.getElementById('withdrawal-tab');
  
  // Скрываем секцию калькулятора
  if (calculatorSection) {
    calculatorSection.classList.add('hidden');
    calculatorSection.style.display = 'none';
  }
  
  // Показываем секцию вывода
  if (withdrawalSection) {
    withdrawalSection.classList.remove('hidden');
    withdrawalSection.style.display = 'block';
  }
  
  // Обновляем табы
  if (withdrawalTab) withdrawalTab.classList.add('active');
  if (calculatorTab) calculatorTab.classList.remove('active');
  
  console.log('✅ Вывод показан принудительно');
}

// Функция для добавления обработчиков кликов к табам
function addTabClickHandlers() {
  console.log('🖱️ Добавление обработчиков кликов к табам...');
  
  const calculatorTab = document.getElementById('calculator-tab');
  const withdrawalTab = document.getElementById('withdrawal-tab');
  
  if (calculatorTab) {
    // Удаляем старые обработчики
    calculatorTab.replaceWith(calculatorTab.cloneNode(true));
    const newCalculatorTab = document.getElementById('calculator-tab');
    
    newCalculatorTab.addEventListener('click', (e) => {
      e.preventDefault();
      e.stopPropagation();
      console.log('🖱️ Клик по calculator-tab');
      forceShowCalculator();
    });
    
    console.log('✅ Обработчик добавлен для calculator-tab');
  }
  
  if (withdrawalTab) {
    // Удаляем старые обработчики
    withdrawalTab.replaceWith(withdrawalTab.cloneNode(true));
    const newWithdrawalTab = document.getElementById('withdrawal-tab');
    
    newWithdrawalTab.addEventListener('click', (e) => {
      e.preventDefault();
      e.stopPropagation();
      console.log('🖱️ Клик по withdrawal-tab');
      forceShowWithdrawal();
    });
    
    console.log('✅ Обработчик добавлен для withdrawal-tab');
  }
}

// Функция для полной диагностики табов
function fullTabsDiagnostic() {
  console.log('🔍 ПОЛНАЯ ДИАГНОСТИКА ТАБОВ');
  console.log('============================');
  
  testWithdrawalTabs();
  console.log('');
  addTabClickHandlers();
  console.log('');
  
  console.log('🎮 Доступные команды:');
  console.log('  testWithdrawalTabs() - проверить табы');
  console.log('  forceShowCalculator() - показать калькулятор');
  console.log('  forceShowWithdrawal() - показать вывод');
  console.log('  addTabClickHandlers() - добавить обработчики');
  console.log('  fullTabsDiagnostic() - полная диагностика');
}

// Экспорт функций
window.testWithdrawalTabs = testWithdrawalTabs;
window.forceShowCalculator = forceShowCalculator;
window.forceShowWithdrawal = forceShowWithdrawal;
window.addTabClickHandlers = addTabClickHandlers;
window.fullTabsDiagnostic = fullTabsDiagnostic;

// Автоматический запуск диагностики
setTimeout(() => {
  console.log('🚀 Автоматическая диагностика табов...');
  fullTabsDiagnostic();
}, 3000);

console.log('🔧 [TestTabs] Тестирование табов загружено');
