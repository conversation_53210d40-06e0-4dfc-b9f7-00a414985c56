// === navigation.js ===
// Навигация и переключение страниц

// Получаем элементы DOM напрямую
const mainContentEl = document.getElementById('main-content');
const earnSectionEl = document.getElementById('earn-page');
const friendsSectionEl = document.getElementById('friends-page');
const navHomeButton = document.querySelector('[data-page="main-content"]');
const navEarnButton = document.querySelector('[data-page="earn-page"]');
const navFriendsButton = document.querySelector('[data-page="friends-page"]');

// Функции для работы с классами
function removeClass(element, className) {
  if (element) element.classList.remove(className);
}

function addClass(element, className) {
  if (element) element.classList.add(className);
}

// Отслеживаем текущую страницу
let currentPageElement = mainContentEl;

/**
 * Получает текущую активную страницу
 * @returns {HTMLElement} Текущая страница
 */
function getCurrentPage() {
  return currentPageElement;
}

/**
 * Переключает видимую страницу с CSS-анимацией
 * @param {HTMLElement} nextPageElement - Элемент новой страницы
 * @param {HTMLElement} [activeNavButton] - Кнопка навигации для активации
 */
function switchPageAnimated(nextPageElement, activeNavButton) {
  if (
    !nextPageElement ||
    nextPageElement === currentPageElement ||
    isTransitioning
  ) {
    console.log(
      `Переключение отменено: next=${nextPageElement?.id}, current=${currentPageElement?.id}, transitioning=${isTransitioning}`
    );
    return;
  }
  
  console.log(`Анимация v6: ${currentPageElement?.id} -> ${nextPageElement.id}`);
  if (window.setIsTransitioning) window.setIsTransitioning(true);
  const pageOutElement = currentPageElement;

  // 1. Обновляем кнопку навигации
  updateActiveNavButton(activeNavButton);

  // --- Подготовка новой страницы ---
  // СНАЧАЛА УБИРАЕМ КЛАСС СКРЫТИЯ И СБРАСЫВАЕМ ИНЛАЙНОВЫЙ DISPLAY
  removeClass(nextPageElement, "page-hidden");
  nextPageElement.style.display = ""; // ВАЖНО: Сбрасываем инлайновый стиль!
  
  // Сбрасываем классы анимации на всякий случай
  removeClass(nextPageElement, "page-leave-active");
  removeClass(nextPageElement, "page-enter-active");
  removeClass(nextPageElement, "page-enter");
  
  // Ставим начальное состояние анимации входа
  addClass(nextPageElement, "page-enter");
  console.log(
    `Новая страница ${nextPageElement.id}: убран hidden, сброшен inline display, добавлен enter`
  );

  // --- Анимация ухода старой страницы ---
  if (pageOutElement) {
    removeClass(pageOutElement, "page-enter-active");
    addClass(pageOutElement, "page-leave-active"); // Запускаем анимацию ухода
    console.log(`Старая страница ${pageOutElement.id}: добавлен leave-active`);
  }

  // --- Запуск анимации входа новой страницы (после reflow) ---
  requestAnimationFrame(() => {
    removeClass(nextPageElement, "page-enter");
    addClass(nextPageElement, "page-enter-active");
    console.log(
      `Новая страница ${nextPageElement.id}: убран enter, добавлен enter-active`
    );
  });

  // 5. Обновляем текущую страницу СРАЗУ
  currentPageElement = nextPageElement;

  // 6. Завершение анимации и очистка классов
  setTimeout(() => {
    // Убираем классы анимации с НОВОЙ (теперь текущей) страницы
    removeClass(currentPageElement, "page-enter-active");
    console.log(`Новая страница ${currentPageElement.id}: убран enter-active`);

    // Прячем СТАРУЮ страницу (добавляем класс) и убираем класс анимации
    if (pageOutElement) {
      addClass(pageOutElement, "page-hidden"); // Добавляем класс для скрытия
      removeClass(pageOutElement, "page-leave-active");
      // Дополнительно можно сбросить инлайн стиль и у старой страницы
      pageOutElement.style.display = "";
      console.log(
        `Старая страница ${pageOutElement.id}: добавлен hidden, убран leave-active, сброшен inline display`
      );
    }

    if (window.setIsTransitioning) window.setIsTransitioning(false); // Разрешаем следующее переключение
    console.log(`Анимация v6 завершена. Активна: ${currentPageElement.id}`);

    // Действия после переключения
    handlePageTransitionComplete();
  }, window.PAGE_TRANSITION_DURATION || 300); // Ждем время анимации
}

/**
 * Обрабатывает завершение перехода между страницами
 */
function handlePageTransitionComplete() {
  if (currentPageElement === friendsSectionEl) {
    // Инициализация реферальной системы
    if (window.generateReferralLink) window.generateReferralLink();
    if (window.loadReferralStats) window.loadReferralStats();
  }
  
  if (currentPageElement === earnSectionEl) {
    // Инициализация системы вывода
    if (window.updateWithdrawalSection) window.updateWithdrawalSection();
    if (window.updateCryptoAmountField) window.updateCryptoAmountField();
    if (window.validateWithdrawalForm) window.validateWithdrawalForm();

    // Применяем данные калькулятора если есть
    if (window.calculatorData && window.calculatorData.amount > 0 && window.calculatorData.currency) {
      const withdrawalAmountInput = document.getElementById('withdrawal-amount');
      const cryptoCurrencySelect = document.getElementById('crypto-currency');

      if (withdrawalAmountInput) {
        withdrawalAmountInput.value = window.calculatorData.amount;
      }

      if (cryptoCurrencySelect) {
        cryptoCurrencySelect.value = window.calculatorData.currency;
      }

      console.log(`[Sync] Форма вывода синхронизирована с калькулятором`);
    }
  }
}

/**
 * Показывает главную секцию
 */
function showMainContent() {
  switchPageAnimated(mainContentEl, navHomeButton);
}

/**
 * Показывает секцию заработка
 */
function showEarnSection() {
  switchPageAnimated(earnSectionEl, navEarnButton);
}

/**
 * Показывает секцию друзей
 */
function showFriendsSection() {
  switchPageAnimated(friendsSectionEl, navFriendsButton);
}

/**
 * Обновляет активное состояние кнопок навигации
 * @param {HTMLElement} activeButton - Активная кнопка
 */
function updateActiveNavButton(activeButton) {
  [navHomeButton, navEarnButton, navFriendsButton].forEach((button) => {
    if (button) removeClass(button, "active");
  });
  if (activeButton) {
    addClass(activeButton, "active");
  }
}

/**
 * Инициализирует обработчики навигации
 */
function initNavigation() {
  if (navHomeButton) {
    navHomeButton.addEventListener('click', showMainContent);
  }
  
  if (navEarnButton) {
    navEarnButton.addEventListener('click', showEarnSection);
  }
  
  if (navFriendsButton) {
    navFriendsButton.addEventListener('click', showFriendsSection);
  }
  
  console.log('🧭 Навигация инициализирована');
}

// Делаем функции доступными глобально
window.getCurrentPage = getCurrentPage;
window.switchPageAnimated = switchPageAnimated;
window.showMainContent = showMainContent;
window.showEarnSection = showEarnSection;
window.showFriendsSection = showFriendsSection;
window.updateActiveNavButton = updateActiveNavButton;
window.initNavigation = initNavigation;
