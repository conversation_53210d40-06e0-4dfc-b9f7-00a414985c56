<?php
declare(strict_types=1);

namespace Models;

require_once __DIR__ . '/Model.php';

/**
 * User.php
 * User model for managing user data and operations
 *
 * This model handles all user-related database operations including
 * registration, balance management, referrals, and security features.
 */
class User extends Model
{
    /** @var string Table name */
    protected string $table = 'users';
    
    /** @var string Primary key field */
    protected string $primaryKey = 'telegram_id';
    
    /** @var array<string> Mass assignable fields */
    protected array $fillable = [
        'telegram_id',
        'username',
        'first_name',
        'last_name',
        'language',
        'balance',
        'total_earned',
        'referrer_id',
        'referral_earnings',
        'registered_at',
        'last_activity',
        'suspicious_activity_count',
        'blocked',
        'blocked_at',
        'device_fingerprint'
    ];

    /**
     * Find user by Telegram ID
     * 
     * @param int $telegramId
     * @return User|null
     */
    public static function findByTelegramId(int $telegramId): ?User
    {
        return self::find($telegramId);
    }

    /**
     * Create new user from Telegram data
     * 
     * @param array<string, mixed> $telegramData
     * @param int|null $referrerId
     * @return User
     */
    public static function createFromTelegram(array $telegramData, ?int $referrerId = null): User
    {
        $now = time();
        
        $userData = [
            'telegram_id' => (int)$telegramData['id'],
            'username' => $telegramData['username'] ?? null,
            'first_name' => $telegramData['first_name'] ?? '',
            'last_name' => $telegramData['last_name'] ?? '',
            'language' => $telegramData['language_code'] ?? 'ru',
            'balance' => 0,
            'total_earned' => 0,
            'referrer_id' => $referrerId,
            'referral_earnings' => 0,
            'registered_at' => $now,
            'last_activity' => $now,
            'suspicious_activity_count' => 0,
            'blocked' => false,
            'blocked_at' => null,
            'device_fingerprint' => null
        ];
        
        $user = new self($userData);
        $user->save();
        
        // If user has a referrer, create referral record
        if ($referrerId) {
            Referral::create($referrerId, (int)$telegramData['id']);
        }
        
        return $user;
    }

    /**
     * Update user's last activity
     * 
     * @return bool
     */
    public function updateActivity(): bool
    {
        $this->last_activity = time();
        return $this->save();
    }

    /**
     * Add coins to user balance
     * 
     * @param int $amount
     * @param string $reason
     * @return bool
     */
    public function addBalance(int $amount, string $reason = 'ad_view'): bool
    {
        if ($amount <= 0) {
            return false;
        }
        
        $this->balance = ($this->balance ?? 0) + $amount;
        $this->total_earned = ($this->total_earned ?? 0) + $amount;
        
        $result = $this->save();
        
        if ($result) {
            // Log the transaction
            error_log("User {$this->telegram_id} earned {$amount} coins for {$reason}. New balance: {$this->balance}");
        }
        
        return $result;
    }

    /**
     * Subtract coins from user balance
     * 
     * @param int $amount
     * @param string $reason
     * @return bool
     */
    public function subtractBalance(int $amount, string $reason = 'withdrawal'): bool
    {
        if ($amount <= 0 || $this->balance < $amount) {
            return false;
        }
        
        $this->balance -= $amount;
        $result = $this->save();
        
        if ($result) {
            error_log("User {$this->telegram_id} spent {$amount} coins for {$reason}. New balance: {$this->balance}");
        }
        
        return $result;
    }

    /**
     * Check if user can withdraw specified amount
     * 
     * @param int $amount
     * @return bool
     */
    public function canWithdraw(int $amount): bool
    {
        return !$this->blocked && 
               $this->balance >= $amount && 
               $amount >= MIN_WITHDRAWAL_AMOUNT &&
               $this->balance >= MIN_BALANCE_FOR_WITHDRAWAL;
    }

    /**
     * Block user
     * 
     * @param string $reason
     * @return bool
     */
    public function block(string $reason = 'suspicious_activity'): bool
    {
        $this->blocked = true;
        $this->blocked_at = time();
        
        $result = $this->save();
        
        if ($result) {
            // Log fraud event
            FraudLog::create($this->telegram_id, 'user_blocked', $reason, 'high');
            error_log("User {$this->telegram_id} blocked for: {$reason}");
        }
        
        return $result;
    }

    /**
     * Unblock user
     * 
     * @return bool
     */
    public function unblock(): bool
    {
        $this->blocked = false;
        $this->blocked_at = null;
        
        return $this->save();
    }

    /**
     * Increment suspicious activity counter
     * 
     * @param string $reason
     * @return bool
     */
    public function incrementSuspiciousActivity(string $reason): bool
    {
        $this->suspicious_activity_count = ($this->suspicious_activity_count ?? 0) + 1;
        
        // Auto-block if too many suspicious activities
        if ($this->suspicious_activity_count >= 5) {
            $this->block('multiple_suspicious_activities');
        }
        
        $result = $this->save();
        
        if ($result) {
            FraudLog::create($this->telegram_id, 'suspicious_activity', $reason, 'medium');
        }
        
        return $result;
    }

    /**
     * Get user's referrals
     * 
     * @return array<User>
     */
    public function getReferrals(): array
    {
        return self::where(['referrer_id' => $this->telegram_id]);
    }

    /**
     * Get user's referral count
     * 
     * @return int
     */
    public function getReferralCount(): int
    {
        return count($this->getReferrals());
    }

    /**
     * Check if user is admin
     * 
     * @return bool
     */
    public function isAdmin(): bool
    {
        return in_array($this->telegram_id, ADMIN_USER_IDS);
    }

    /**
     * Get user's ad views for today
     * 
     * @param string $adType
     * @return int
     */
    public function getTodayAdViews(string $adType): int
    {
        $today = strtotime('today');
        $tomorrow = strtotime('tomorrow');
        
        $adViews = AdView::where([
            'user_id' => $this->telegram_id,
            'ad_type' => $adType
        ]);
        
        $todayViews = array_filter($adViews, function($view) use ($today, $tomorrow) {
            return $view->viewed_at >= $today && $view->viewed_at < $tomorrow;
        });
        
        return count($todayViews);
    }

    /**
     * Check if user can view more ads of specified type today
     * 
     * @param string $adType
     * @return bool
     */
    public function canViewAd(string $adType): bool
    {
        if ($this->blocked) {
            return false;
        }
        
        $todayViews = $this->getTodayAdViews($adType);
        
        $limits = [
            'native_banner' => USER_AD_LIMIT_NATIVE_BANNER,
            'interstitial' => USER_AD_LIMIT_INTERSTITIAL,
            'rewarded_video' => USER_AD_LIMIT_REWARDED_VIDEO
        ];
        
        $limit = $limits[$adType] ?? 0;
        
        return $todayViews < $limit;
    }

    /**
     * Get user statistics
     * 
     * @return array<string, mixed>
     */
    public function getStats(): array
    {
        return [
            'balance' => $this->balance ?? 0,
            'total_earned' => $this->total_earned ?? 0,
            'referral_earnings' => $this->referral_earnings ?? 0,
            'referral_count' => $this->getReferralCount(),
            'registered_at' => $this->registered_at,
            'last_activity' => $this->last_activity,
            'blocked' => $this->blocked ?? false,
            'suspicious_activity_count' => $this->suspicious_activity_count ?? 0
        ];
    }
}
