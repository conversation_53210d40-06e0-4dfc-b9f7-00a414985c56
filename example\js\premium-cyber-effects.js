/* ======================================== */
/* PREMIUM CYBER EFFECTS - Премиум киберпанк эффекты */
/* ======================================== */

// Инициализация премиум киберпанк эффектов
function initPremiumCyberEffects() {
  console.log('💎 Инициализация премиум киберпанк эффектов...');
  
  // Добавляем премиум визуальные эффекты
  addPremiumVisualEffects();
  
  // Создаем премиум интерактивные эффекты
  addPremiumInteractions();
  
  // Добавляем премиум анимации
  addPremiumAnimations();
  
  // Создаем премиум частицы
  createPremiumParticles();
  
  // НЕ ТРОГАЕМ навигацию - она работает в main.js
  
  console.log('✨ Премиум киберпанк эффекты активированы!');
}

// Премиум визуальные эффекты
function addPremiumVisualEffects() {
  // Добавляем премиум свечение к важным элементам
  addPremiumGlow();
  
  // Создаем эффект премиум голограммы
  createHologramEffect();
  
  // Добавляем премиум мерцание
  addPremiumShimmer();
  
  // Создаем эффект энергетических волн
  createEnergyWaves();
}

// Создание премиум плавающих частиц
function createPremiumParticles() {
  const particlesContainer = document.createElement('div');
  particlesContainer.className = 'premium-particles';
  particlesContainer.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
    overflow: hidden;
  `;
  document.body.appendChild(particlesContainer);
  
  // Создаем 20 премиум частиц
  const colors = ['#00ffff', '#ff00ff', '#00ff00', '#ffff00', '#ff4500'];
  
  for (let i = 0; i < 20; i++) {
    const particle = document.createElement('div');
    const color = colors[i % colors.length];
    const size = 2 + Math.random() * 4;
    
    particle.style.cssText = `
      position: absolute;
      width: ${size}px;
      height: ${size}px;
      background: ${color};
      border-radius: 50%;
      box-shadow: 
        0 0 10px ${color}, 
        0 0 20px ${color}, 
        0 0 30px ${color};
      animation: premium-float ${4 + Math.random() * 6}s ease-in-out infinite;
      left: ${Math.random() * 100}%;
      top: ${Math.random() * 100}%;
      animation-delay: ${Math.random() * 5}s;
      opacity: 0.7;
    `;
    
    particlesContainer.appendChild(particle);
    
    // Добавляем случайное движение
    setInterval(() => {
      particle.style.left = Math.random() * 100 + '%';
      particle.style.top = Math.random() * 100 + '%';
    }, 8000 + Math.random() * 4000);
  }
}

// Добавление премиум свечения
function addPremiumGlow() {
  // Добавляем динамическое свечение к балансу
  const balanceElements = document.querySelectorAll('.balance-amount');
  balanceElements.forEach(element => {
    const observer = new MutationObserver(() => {
      // Создаем эффект премиум вспышки при изменении баланса
      createPremiumFlash(element);
    });
    
    observer.observe(element, { childList: true, characterData: true, subtree: true });
  });
  
  // Добавляем премиум пульсацию к статус сообщениям
  const statusMessages = document.querySelectorAll('.status-message');
  statusMessages.forEach(message => {
    message.classList.add('premium-glow');
  });
}

// Создание эффекта голограммы
function createHologramEffect() {
  const hologramContainer = document.createElement('div');
  hologramContainer.className = 'hologram-effect';
  hologramContainer.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
    background: 
      repeating-linear-gradient(
        0deg,
        transparent,
        transparent 2px,
        rgba(0, 255, 255, 0.03) 2px,
        rgba(0, 255, 255, 0.03) 4px
      );
    animation: premium-scan 8s linear infinite;
  `;
  document.body.appendChild(hologramContainer);
}

// Добавление премиум мерцания
function addPremiumShimmer() {
  const shimmerElements = document.querySelectorAll('.premium-text, .premium-shimmer');
  shimmerElements.forEach(element => {
    element.style.background = `
      linear-gradient(
        45deg,
        #00ffff,
        #ff00ff,
        #00ff00,
        #ffff00,
        #00ffff
      )
    `;
    element.style.backgroundSize = '400% 400%';
    element.style.webkitBackgroundClip = 'text';
    element.style.backgroundClip = 'text';
    element.style.webkitTextFillColor = 'transparent';
    element.style.animation = 'premium-shimmer 3s ease-in-out infinite';
  });
}

// Создание энергетических волн
function createEnergyWaves() {
  const wavesContainer = document.createElement('div');
  wavesContainer.className = 'energy-waves';
  wavesContainer.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
    overflow: hidden;
  `;
  document.body.appendChild(wavesContainer);
  
  // Создаем случайные энергетические волны
  setInterval(() => {
    if (Math.random() < 0.4) { // 40% шанс каждые 4 секунды
      createEnergyWave(wavesContainer);
    }
  }, 4000);
}

// Создание одной энергетической волны
function createEnergyWave(container) {
  const wave = document.createElement('div');
  const colors = ['#00ffff', '#ff00ff', '#00ff00', '#ffff00'];
  const color = colors[Math.floor(Math.random() * colors.length)];
  const isHorizontal = Math.random() > 0.5;
  
  if (isHorizontal) {
    wave.style.cssText = `
      position: absolute;
      width: 100%;
      height: 2px;
      background: linear-gradient(90deg, transparent, ${color}, transparent);
      left: 0;
      top: ${Math.random() * 100}%;
      animation: premium-wave 3s linear;
      box-shadow: 0 0 20px ${color};
      opacity: 0.8;
    `;
  } else {
    wave.style.cssText = `
      position: absolute;
      width: 2px;
      height: 100%;
      background: linear-gradient(0deg, transparent, ${color}, transparent);
      left: ${Math.random() * 100}%;
      top: 0;
      animation: premium-wave 3s linear;
      box-shadow: 0 0 20px ${color};
      opacity: 0.8;
    `;
  }
  
  container.appendChild(wave);
  
  setTimeout(() => {
    wave.remove();
  }, 3000);
}

// Премиум интерактивные эффекты
function addPremiumInteractions() {
  // Премиум эффекты для кнопок
  const buttons = document.querySelectorAll('.premium-button, .action-button');
  buttons.forEach(button => {
    button.addEventListener('mouseenter', () => {
      createPremiumAura(button);
      button.style.animation = 'premium-breathe 2s ease-in-out infinite';
    });
    
    button.addEventListener('mouseleave', () => {
      button.style.animation = '';
    });
    
    button.addEventListener('click', () => {
      createPremiumExplosion(button);
      createPremiumShockwave(button);
    });
  });
  
  // Премиум эффекты для карточек
  const cards = document.querySelectorAll('.premium-card, .app-section');
  cards.forEach(card => {
    card.addEventListener('mouseenter', () => {
      createPremiumHalo(card);
    });
    
    card.addEventListener('mouseleave', () => {
      removePremiumHalo(card);
    });
  });
  
  // Премиум эффекты для иконок
  const icons = document.querySelectorAll('.premium-icon');
  icons.forEach(icon => {
    icon.addEventListener('mouseenter', () => {
      icon.style.animation = 'premium-rotate 2s ease-in-out infinite';
      icon.style.filter = 'drop-shadow(0 0 20px currentColor) brightness(1.5)';
    });
    
    icon.addEventListener('mouseleave', () => {
      icon.style.animation = '';
      icon.style.filter = 'drop-shadow(0 0 10px currentColor)';
    });
  });
}

// Создание премиум ауры
function createPremiumAura(element) {
  const aura = document.createElement('div');
  aura.className = 'premium-aura';
  aura.style.cssText = `
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: radial-gradient(circle, rgba(0, 255, 255, 0.2), transparent);
    border-radius: 20px;
    animation: premium-pulse 2s ease-in-out infinite;
    pointer-events: none;
    z-index: -1;
  `;
  
  element.style.position = 'relative';
  element.appendChild(aura);
  
  setTimeout(() => {
    aura.remove();
  }, 2000);
}

// Создание премиум взрыва
function createPremiumExplosion(element) {
  const explosion = document.createElement('div');
  explosion.style.cssText = `
    position: absolute;
    top: 50%;
    left: 50%;
    width: 150px;
    height: 150px;
    margin-left: -75px;
    margin-top: -75px;
    background: radial-gradient(
      circle,
      rgba(255, 255, 255, 0.8),
      rgba(0, 255, 255, 0.6),
      rgba(255, 0, 255, 0.4),
      transparent
    );
    border-radius: 50%;
    animation: premium-explosion 0.8s ease-out;
    pointer-events: none;
    z-index: 10;
  `;
  
  element.style.position = 'relative';
  element.appendChild(explosion);
  
  setTimeout(() => {
    explosion.remove();
  }, 800);
}

// Создание премиум ударной волны
function createPremiumShockwave(element) {
  const shockwave = document.createElement('div');
  shockwave.style.cssText = `
    position: absolute;
    top: 50%;
    left: 50%;
    width: 50px;
    height: 50px;
    margin-left: -25px;
    margin-top: -25px;
    border: 3px solid #00ffff;
    border-radius: 50%;
    animation: premium-shockwave 1s ease-out;
    pointer-events: none;
    z-index: 9;
  `;
  
  element.style.position = 'relative';
  element.appendChild(shockwave);
  
  setTimeout(() => {
    shockwave.remove();
  }, 1000);
}

// Создание премиум гало
function createPremiumHalo(element) {
  const halo = document.createElement('div');
  halo.className = 'premium-halo';
  halo.style.cssText = `
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    border: 2px solid transparent;
    border-radius: 25px;
    background: linear-gradient(45deg, #00ffff, #ff00ff, #00ff00, #ffff00) border-box;
    mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
    animation: premium-rotate 3s linear infinite;
    pointer-events: none;
    z-index: -1;
  `;
  
  element.style.position = 'relative';
  element.appendChild(halo);
}

// Удаление премиум гало
function removePremiumHalo(element) {
  const halo = element.querySelector('.premium-halo');
  if (halo) {
    halo.remove();
  }
}

// Создание премиум вспышки
function createPremiumFlash(element) {
  const flash = document.createElement('div');
  flash.style.cssText = `
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.8), transparent);
    border-radius: inherit;
    animation: premium-flash 0.5s ease-out;
    pointer-events: none;
    z-index: 10;
  `;
  
  element.style.position = 'relative';
  element.appendChild(flash);
  
  setTimeout(() => {
    flash.remove();
  }, 500);
}

// Премиум анимации
function addPremiumAnimations() {
  // Плавное появление элементов с премиум эффектами
  const elements = document.querySelectorAll('.premium-entrance');
  
  const observer = new IntersectionObserver((entries) => {
    entries.forEach((entry, index) => {
      if (entry.isIntersecting) {
        setTimeout(() => {
          entry.target.style.animation = 'premium-fade-in 1s ease forwards';
          entry.target.style.opacity = '1';
          
          // Добавляем премиум эффект появления
          createPremiumAppearance(entry.target);
        }, index * 200);
        observer.unobserve(entry.target);
      }
    });
  }, { threshold: 0.1 });
  
  elements.forEach(element => {
    element.style.opacity = '0';
    observer.observe(element);
  });
}

// Создание премиум эффекта появления
function createPremiumAppearance(element) {
  const appearance = document.createElement('div');
  appearance.style.cssText = `
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      45deg,
      transparent,
      rgba(0, 255, 255, 0.3),
      transparent
    );
    animation: premium-scan 1s ease-out;
    pointer-events: none;
    z-index: 10;
  `;
  
  element.style.position = 'relative';
  element.appendChild(appearance);
  
  setTimeout(() => {
    appearance.remove();
  }, 1000);
}

// Премиум уведомления
function showPremiumNotification(message, type = 'info') {
  const notification = document.createElement('div');
  notification.className = `premium-notification premium-notification-${type}`;
  
  let borderColor = '#00ffff';
  let glowColor = '#00ffff';
  
  if (type === 'success') {
    borderColor = '#00ff00';
    glowColor = '#00ff00';
  } else if (type === 'error') {
    borderColor = '#ff0040';
    glowColor = '#ff0040';
  }
  
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(10, 10, 10, 0.95);
    backdrop-filter: blur(20px);
    border: 2px solid ${borderColor};
    border-radius: 20px;
    padding: 20px 24px;
    color: ${borderColor};
    font-weight: 600;
    font-size: 16px;
    z-index: 10000;
    box-shadow: 
      0 0 30px ${glowColor}, 
      0 0 60px ${glowColor},
      0 20px 40px rgba(0, 0, 0, 0.3);
    transform: translateX(100%) scale(0.8);
    opacity: 0;
    transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    max-width: 350px;
    text-shadow: 0 0 15px ${glowColor};
    animation: premium-glow-wave 3s ease-in-out infinite;
  `;
  
  notification.textContent = message;
  document.body.appendChild(notification);
  
  // Анимация появления
  setTimeout(() => {
    notification.style.transform = 'translateX(0) scale(1)';
    notification.style.opacity = '1';
  }, 100);
  
  // Автоматическое скрытие
  setTimeout(() => {
    notification.style.transform = 'translateX(100%) scale(0.8)';
    notification.style.opacity = '0';
    setTimeout(() => {
      notification.remove();
    }, 500);
  }, 5000);
}

// Добавление премиум CSS анимаций
function addPremiumCSS() {
  const style = document.createElement('style');
  style.textContent = `
    @keyframes premium-explosion {
      0% { transform: translate(-50%, -50%) scale(0); opacity: 1; }
      100% { transform: translate(-50%, -50%) scale(3); opacity: 0; }
    }
    
    @keyframes premium-shockwave {
      0% { transform: translate(-50%, -50%) scale(1); opacity: 1; }
      100% { transform: translate(-50%, -50%) scale(6); opacity: 0; }
    }
    
    @keyframes premium-flash {
      0% { opacity: 0; }
      50% { opacity: 1; }
      100% { opacity: 0; }
    }
    
    /* Скрытие частиц на слабых устройствах */
    @media (max-width: 768px) {
      .premium-particles,
      .energy-waves,
      .hologram-effect {
        display: none;
      }
    }
    
    /* Уменьшение анимаций на мобильных */
    @media (prefers-reduced-motion: reduce) {
      * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
      }
    }
  `;
  document.head.appendChild(style);
}

// Премиум курсор (только для десктопа)
function initPremiumCursor() {
  if (window.innerWidth <= 768) return;
  
  const cursor = document.createElement('div');
  cursor.style.cssText = `
    position: fixed;
    width: 12px;
    height: 12px;
    background: radial-gradient(circle, #00ffff, #ff00ff);
    border-radius: 50%;
    pointer-events: none;
    z-index: 9999;
    transition: all 0.1s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    mix-blend-mode: difference;
    box-shadow: 
      0 0 20px #00ffff, 
      0 0 40px #ff00ff,
      0 0 60px #00ff00;
    animation: premium-rotate 4s linear infinite;
  `;
  document.body.appendChild(cursor);
  
  document.addEventListener('mousemove', (e) => {
    cursor.style.left = e.clientX - 6 + 'px';
    cursor.style.top = e.clientY - 6 + 'px';
  });
  
  // Премиум эффекты при наведении
  const interactiveElements = document.querySelectorAll('button, a, input, [role="button"]');
  interactiveElements.forEach(element => {
    element.addEventListener('mouseenter', () => {
      cursor.style.transform = 'scale(2.5)';
      cursor.style.background = 'radial-gradient(circle, #ff00ff, #ffff00)';
      cursor.style.boxShadow = '0 0 30px #ff00ff, 0 0 60px #ffff00';
    });
    
    element.addEventListener('mouseleave', () => {
      cursor.style.transform = 'scale(1)';
      cursor.style.background = 'radial-gradient(circle, #00ffff, #ff00ff)';
      cursor.style.boxShadow = '0 0 20px #00ffff, 0 0 40px #ff00ff, 0 0 60px #00ff00';
    });
  });
}

// Инициализация всех премиум эффектов
document.addEventListener('DOMContentLoaded', () => {
  // Небольшая задержка чтобы не конфликтовать с main.js
  setTimeout(() => {
    addPremiumCSS();
    initPremiumCyberEffects();
    
    // Премиум курсор только для десктопа
    if (window.innerWidth > 768) {
      initPremiumCursor();
    }
    
    // Показываем премиум приветственное уведомление
    setTimeout(() => {
      showPremiumNotification('💎 Премиум киберпанк активирован!', 'success');
    }, 1500);
  }, 400);
});

// Экспорт функций для использования в других скриптах
window.PremiumCyberEffects = {
  showPremiumNotification,
  createPremiumExplosion,
  createPremiumShockwave,
  createPremiumAura,
  initPremiumCursor
};
