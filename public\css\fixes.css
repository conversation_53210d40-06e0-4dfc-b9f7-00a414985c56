/* fixes.css - Исправления для UniQPaid */

/* === ИСПРАВЛЕНИЕ ИКОНОК В КНОПКАХ РЕКЛАМЫ === */
/* Белые иконки в кнопках рекламы */
.action-button svg {
    color: #ffffff !important;
    fill: none !important;
    stroke: #ffffff !important;
    stroke-width: 2 !important;
    width: 20px !important;
    height: 20px !important;
    margin-right: 8px !important;
    flex-shrink: 0 !important;
}

.action-button .button-icon {
    color: #ffffff !important;
    fill: none !important;
    stroke: #ffffff !important;
    stroke-width: 2 !important;
    width: 20px !important;
    height: 20px !important;
    margin-right: 8px !important;
    flex-shrink: 0 !important;
}

/* Исправление позиционирования иконок */
.action-button .button-icon {
    position: relative !important;
    left: auto !important;
    top: auto !important;
    transform: none !important;
}

/* === ИСПРАВЛЕНИЕ НАВИГАЦИИ === */
/* Правильные стили для нижней навигации */
.app-navigation {
    position: fixed !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    width: 100% !important;
    max-width: 480px !important;
    margin: 0 auto !important;
    background: var(--secondary-bg) !important;
    display: flex !important;
    justify-content: space-around !important;
    align-items: center !important;
    padding: 12px 16px max(12px, env(safe-area-inset-bottom)) 16px !important;
    border-radius: 24px 24px 0 0 !important;
    border-top: 1px solid var(--border-light) !important;
    z-index: 1000 !important;
    height: 80px !important;
    box-shadow: 0 -4px 20px var(--shadow-medium) !important;
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
}

/* Кнопки навигации */
.nav-button {
    background: none !important;
    border: none !important;
    color: var(--text-muted) !important;
    cursor: pointer !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 8px 12px !important;
    font-size: 12px !important;
    flex-grow: 1 !important;
    transition: all 0.3s ease !important;
    border-radius: 12px !important;
    font-family: "Inter", sans-serif !important;
    font-weight: 500 !important;
    min-height: 56px !important;
}

.nav-button:hover {
    color: var(--text-secondary) !important;
    background: rgba(255, 215, 0, 0.1) !important;
}

.nav-button.active {
    color: #1A1A1A !important;
    background: linear-gradient(145deg, #FFD54F, #FFC107) !important;
    border-radius: 18px !important;
    box-shadow: 
        0 8px 16px rgba(255, 193, 7, 0.4),
        0 4px 8px rgba(255, 193, 7, 0.3),
        inset 0 2px 0 rgba(255, 255, 255, 0.4),
        inset 0 -2px 0 rgba(255, 165, 0, 0.3) !important;
    border: none !important;
    transform: translateY(-2px) !important;
}

/* SVG иконки в навигации */
.nav-button svg {
    width: 20px !important;
    height: 20px !important;
    margin-bottom: 4px !important;
    fill: none !important;
    stroke: currentColor !important;
    stroke-width: 2 !important;
    transition: all 0.3s ease !important;
    flex-shrink: 0 !important;
}

.nav-button.active svg {
    stroke: #1A1A1A !important;
    filter: drop-shadow(0 0 8px rgba(26, 26, 26, 0.8)) !important;
}

.nav-button:hover svg {
    stroke: var(--text-secondary) !important;
    filter: drop-shadow(0 0 4px rgba(255, 193, 7, 0.5)) !important;
}

/* Текст в навигации */
.nav-button span {
    font-size: 12px !important;
    font-weight: 500 !important;
    margin-top: 2px !important;
}

/* === ИСПРАВЛЕНИЕ ОТСТУПОВ ДЛЯ НАВИГАЦИИ === */
/* Добавляем отступ снизу для контента, чтобы навигация не перекрывала */
.app-container {
    padding-bottom: 100px !important;
}

/* === ИСПРАВЛЕНИЕ ИКОНОК КНОПОК === */
/* SVG внутри круглых иконок */
.action-button .button-icon svg {
    width: 20px !important;
    height: 20px !important;
}

/* === ИСПРАВЛЕНИЕ КНОПОК РЕКЛАМЫ === */
/* Правильное выравнивание содержимого кнопок */
.action-button {
    display: flex !important;
    align-items: center !important;
    justify-content: flex-start !important;
    position: relative !important;
    padding: 18px 80px 18px 75px !important;
}

.action-button .button-content {
    flex: 1 !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: flex-start !important;
    justify-content: center !important;
    margin-left: 8px !important;
}

.action-button .button-text {
    font-size: 17px !important;
    font-weight: 700 !important;
    line-height: 1.2 !important;
    margin: 0 0 4px 0 !important;
    color: rgba(0, 0, 0, 0.9) !important;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.3) !important;
}

.action-button .ad-counter {
    font-size: 14px !important;
    font-weight: 500 !important;
    line-height: 1.1 !important;
    color: rgba(0, 0, 0, 0.75) !important;
    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.2) !important;
    margin: 0 !important;
}

/* Награда справа */
.reward-badge {
    position: absolute !important;
    right: 20px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    background: rgba(255, 107, 53, 0.9) !important;
    color: white !important;
    padding: 8px 12px !important;
    border-radius: 50px !important;
    font-size: 14px !important;
    font-weight: 600 !important;
    box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3) !important;
    min-width: 36px !important;
    height: 36px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border: 2px solid rgba(255, 255, 255, 0.2) !important;
    backdrop-filter: blur(10px) !important;
}

/* === ИСПРАВЛЕНИЕ СЕКЦИЙ === */
/* Правильное скрытие/показ секций */
.app-section {
    display: block !important;
    opacity: 1 !important;
    transform: translateX(0) !important;
    transition: all 0.3s ease !important;
}

.app-section.page-hidden {
    display: none !important;
}

.app-section.active-section {
    display: block !important;
    opacity: 1 !important;
    transform: translateX(0) !important;
}

/* === ИСПРАВЛЕНИЕ СТАТУС СООБЩЕНИЙ === */
.status-message {
    padding: 12px 16px !important;
    margin: 16px 0 !important;
    border-radius: 12px !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    text-align: center !important;
    transition: all 0.3s ease !important;
}

.status-message.status-success {
    background: rgba(76, 175, 80, 0.1) !important;
    color: #4CAF50 !important;
    border: 1px solid rgba(76, 175, 80, 0.3) !important;
}

.status-message.status-error {
    background: rgba(244, 67, 54, 0.1) !important;
    color: #F44336 !important;
    border: 1px solid rgba(244, 67, 54, 0.3) !important;
}

.status-message.status-info {
    background: rgba(33, 150, 243, 0.1) !important;
    color: #2196F3 !important;
    border: 1px solid rgba(33, 150, 243, 0.3) !important;
}

/* === АДАПТИВНОСТЬ === */
@media (max-width: 480px) {
    .app-navigation {
        border-radius: 0 !important;
        left: 0 !important;
        right: 0 !important;
        width: 100% !important;
        max-width: none !important;
    }
    
    .action-button {
        padding: 16px 50px 16px 24px !important;
    }
    
    .action-button .button-text {
        font-size: 16px !important;
    }
    
    .nav-button {
        padding: 6px 8px !important;
        font-size: 11px !important;
    }
    
    .nav-button svg {
        width: 18px !important;
        height: 18px !important;
    }
}
