<?php
declare(strict_types=1);

namespace Controllers;

require_once __DIR__ . '/Controller.php';
require_once __DIR__ . '/../Models/User.php';
require_once __DIR__ . '/../Models/Translation.php';
require_once __DIR__ . '/../Localization.php';

use Models\User;
use Models\Translation;

/**
 * TestController - handles all testing functionality
 * This controller is used for testing various features during development
 */
class TestController extends Controller
{
    /**
     * Main test page - displays all available tests
     * 
     * @return void
     */
    public function index(): void
    {
        $this->render('test/index', [
            'title' => 'UniQPaid - Test Suite',
            'tests' => $this->getAvailableTests()
        ]);
    }

    /**
     * Test database connection
     * 
     * @return void
     */
    public function testDatabase(): void
    {
        $results = [];
        
        try {
            // Test database connection
            $db = \Database::getConnection();
            $results['database_connection'] = [
                'status' => 'success',
                'message' => 'Database connection successful',
                'type' => get_class($db)
            ];
            
            // Test tables existence
            $tables = ['users', 'ad_views', 'withdrawals', 'referrals', 'fraud_logs', 'settings', 'translations'];
            foreach ($tables as $table) {
                try {
                    $stmt = $db->query("SELECT COUNT(*) FROM {$table}");
                    $count = $stmt->fetchColumn();
                    $results["table_{$table}"] = [
                        'status' => 'success',
                        'message' => "Table {$table} exists with {$count} records"
                    ];
                } catch (Exception $e) {
                    $results["table_{$table}"] = [
                        'status' => 'error',
                        'message' => "Table {$table} error: " . $e->getMessage()
                    ];
                }
            }
            
        } catch (Exception $e) {
            $results['database_connection'] = [
                'status' => 'error',
                'message' => 'Database connection failed: ' . $e->getMessage()
            ];
        }
        
        $this->renderJson($results);
    }

    /**
     * Test translations system
     * 
     * @return void
     */
    public function testTranslations(): void
    {
        $results = [];
        
        try {
            // Test translation import
            $testTranslations = [
                'test.hello' => 'Hello World',
                'test.goodbye' => 'Goodbye'
            ];
            
            $imported = Translation::importTranslations($testTranslations, 'en', 'test');
            $results['import'] = [
                'status' => 'success',
                'message' => "Imported {$imported} test translations"
            ];
            
            // Test translation retrieval
            $hello = Translation::getTranslation('test.hello', 'en');
            $results['retrieval'] = [
                'status' => $hello === 'Hello World' ? 'success' : 'error',
                'message' => "Retrieved: {$hello}"
            ];
            
            // Test localization class
            \Localization::init('en');
            $localized = \Localization::get('test.hello');
            $results['localization'] = [
                'status' => !empty($localized) ? 'success' : 'error',
                'message' => "Localized: {$localized}"
            ];
            
        } catch (Exception $e) {
            $results['error'] = [
                'status' => 'error',
                'message' => 'Translation test failed: ' . $e->getMessage()
            ];
        }
        
        $this->renderJson($results);
    }

    /**
     * Test user model
     * 
     * @return void
     */
    public function testUser(): void
    {
        $results = [];
        
        try {
            // Test user creation
            $testUser = new User([
                'telegram_id' => 999999999,
                'first_name' => 'Test User',
                'username' => 'testuser',
                'language_code' => 'en',
                'balance' => 100
            ]);
            
            $saved = $testUser->save();
            $results['user_creation'] = [
                'status' => $saved ? 'success' : 'error',
                'message' => $saved ? 'Test user created successfully' : 'Failed to create test user'
            ];
            
            // Test user retrieval
            $foundUser = User::findByTelegramId(999999999);
            $results['user_retrieval'] = [
                'status' => $foundUser ? 'success' : 'error',
                'message' => $foundUser ? 'Test user found' : 'Test user not found'
            ];
            
            // Test referral code generation
            if ($foundUser) {
                $refCode = $foundUser->generateReferralCode();
                $results['referral_code'] = [
                    'status' => !empty($refCode) ? 'success' : 'error',
                    'message' => "Generated referral code: {$refCode}"
                ];
            }
            
            // Clean up test user
            if ($foundUser) {
                $foundUser->delete();
                $results['cleanup'] = [
                    'status' => 'success',
                    'message' => 'Test user cleaned up'
                ];
            }
            
        } catch (Exception $e) {
            $results['error'] = [
                'status' => 'error',
                'message' => 'User test failed: ' . $e->getMessage()
            ];
        }
        
        $this->renderJson($results);
    }

    /**
     * Test configuration
     *
     * @return void
     */
    public function testConfig(): void
    {
        $results = [];

        // Test constants
        $constants = [
            'APP_VERSION', 'DB_PATH', 'MIN_WITHDRAWAL_AMOUNT',
            'AD_REWARD_NATIVE_BANNER', 'AD_REWARD_INTERSTITIAL', 'AD_REWARD_REWARDED_VIDEO'
        ];

        foreach ($constants as $constant) {
            $results["constant_{$constant}"] = [
                'status' => defined($constant) ? 'success' : 'error',
                'message' => defined($constant) ?
                    "✅ {$constant} = " . constant($constant) :
                    "❌ {$constant} not defined"
            ];
        }

        // Test file permissions
        $paths = [
            'database' => APP_ROOT . '/database',
            'app' => APP_ROOT . '/app',
            'views' => APP_ROOT . '/views',
            'public' => APP_ROOT . '/public'
        ];

        foreach ($paths as $name => $path) {
            $results["path_{$name}"] = [
                'status' => is_dir($path) && is_readable($path) ? 'success' : 'error',
                'message' => is_dir($path) ?
                    "✅ {$name} directory accessible" :
                    "❌ {$name} directory not found"
            ];
        }

        $this->renderJson($results);
    }

    /**
     * Import all translations into database
     *
     * @return void
     */
    public function importTranslations(): void
    {
        $results = [];

        try {
            // English translations
            $enTranslations = [
                'app.name' => 'UniQPaid',
                'app.title' => 'UniQPaid - Crypto Wallet',
                'app.loading' => 'Loading...',
                'app.initializing' => 'Initializing application...',
                'app.error' => 'Error',
                'app.success' => 'Success',

                'navigation.home' => 'Home',
                'navigation.earnings' => 'Earnings',
                'navigation.friends' => 'Friends',

                'tasks.title' => 'Tasks',
                'tasks.open_link' => 'Open Link',
                'tasks.watch_video' => 'Watch Video',
                'tasks.tap_banner' => 'Tap and Earn',

                'buttons.open_link' => 'Open Link',
                'buttons.watch_video' => 'Watch Video',
                'buttons.tap_banner' => 'Tap and Earn',
                'buttons.claim_reward' => 'Claim Reward',
                'buttons.invite_friends' => 'Invite Friends',
                'buttons.withdraw' => 'Withdraw',
                'buttons.cancel' => 'Cancel',
                'buttons.confirm' => 'Confirm',
                'buttons.close' => 'Close',
                'buttons.back' => 'Back',
                'buttons.save' => 'Save',
                'buttons.copy' => 'Copy',
                'buttons.share' => 'Share',

                'rewards.earned' => 'You earned {amount} coins!',
                'rewards.daily_limit' => 'Daily limit reached',
                'rewards.no_ads' => 'No ads available',
                'rewards.try_again' => 'Try again later',
                'rewards.coins' => 'coins',

                'balance.current' => 'Current Balance',
                'balance.total_earned' => 'Total Earned',
                'balance.available' => 'Available for withdrawal',
                'balance.coins' => 'coins',

                'withdrawal.title' => 'Withdraw Funds',
                'withdrawal.minimum' => 'Minimum withdrawal: {amount} coins',
                'withdrawal.select_currency' => 'Select cryptocurrency',
                'withdrawal.enter_address' => 'Enter wallet address',
                'withdrawal.amount' => 'Amount',
                'withdrawal.fee' => 'Network fee',
                'withdrawal.total' => 'Total to receive',
                'withdrawal.request' => 'Request Withdrawal',
                'withdrawal.success' => 'Withdrawal request submitted successfully!',

                'referrals.title' => 'Invite Friends',
                'referrals.your_code' => 'Your referral code',
                'referrals.invite_link' => 'Invitation link',
                'referrals.friends_count' => 'Friends invited',
                'referrals.total_earned' => 'Earned from referrals',
                'referrals.bonus_percent' => 'You get {percent}% from friends\' earnings',
                'referrals.copy_link' => 'Copy invitation link',
                'referrals.share_link' => 'Share invitation link',
                'referrals.invite_text' => 'Join UniQPaid and earn crypto! Use my referral link:',

                'admin.title' => 'Admin Panel',
                'admin.users' => 'Users',
                'admin.withdrawals' => 'Withdrawals',
                'admin.statistics' => 'Statistics',
                'admin.settings' => 'Settings',
                'admin.total_users' => 'Total Users',
                'admin.active_users' => 'Active Users',
                'admin.approve' => 'Approve',
                'admin.reject' => 'Reject',
                'admin.block_user' => 'Block User',
                'admin.unblock_user' => 'Unblock User',

                'errors.network' => 'Network error. Please try again.',
                'errors.server' => 'Server error. Please try again later.',
                'errors.invalid_data' => 'Invalid data provided.',
                'errors.unauthorized' => 'Unauthorized access.',
                'errors.rate_limit' => 'Too many requests. Please wait.',
                'errors.blocked' => 'Your account has been blocked.',

                'status.online' => 'Online',
                'status.offline' => 'Offline',
                'status.loading' => 'Loading',
                'status.pending' => 'Pending',
                'status.completed' => 'Completed',
                'status.failed' => 'Failed',
                'status.cancelled' => 'Cancelled',

                'limits.daily_ads' => 'Daily ad limit: {current}/{max}',
                'limits.views_left' => '{count} views left',
                'limits.limit_reached' => 'Daily limit reached',

                'currencies.TON' => 'TON',
                'currencies.USDT' => 'USDT (TRC20)',
                'currencies.BTC' => 'Bitcoin',
                'currencies.ETH' => 'Ethereum'
            ];

            // Import English translations
            $enCount = Translation::importTranslations($enTranslations, 'en', 'general');
            $results['import_english'] = [
                'status' => $enCount > 0 ? 'success' : 'error',
                'message' => "✅ Imported {$enCount} English translations"
            ];

            // Russian translations
            $ruTranslations = [
                'app.name' => 'UniQPaid',
                'app.title' => 'UniQPaid - Криптокошелёк',
                'app.loading' => 'Загрузка...',
                'app.initializing' => 'Инициализация приложения...',
                'app.error' => 'Ошибка',
                'app.success' => 'Успешно',

                'navigation.home' => 'Главная',
                'navigation.earnings' => 'Заработок',
                'navigation.friends' => 'Друзья',

                'tasks.title' => 'Задания',
                'tasks.open_link' => 'Открыть ссылку',
                'tasks.watch_video' => 'Смотреть видео',
                'tasks.tap_banner' => 'Кликнуть по баннеру',

                'buttons.open_link' => 'Открыть ссылку',
                'buttons.watch_video' => 'Смотреть видео',
                'buttons.tap_banner' => 'Кликнуть по баннеру',
                'buttons.claim_reward' => 'Получить награду',
                'buttons.invite_friends' => 'Пригласить друзей',
                'buttons.withdraw' => 'Вывести',
                'buttons.cancel' => 'Отмена',
                'buttons.confirm' => 'Подтвердить',
                'buttons.close' => 'Закрыть',
                'buttons.back' => 'Назад',
                'buttons.save' => 'Сохранить',
                'buttons.copy' => 'Копировать',
                'buttons.share' => 'Поделиться',

                'rewards.earned' => 'Вы заработали {amount} монет!',
                'rewards.daily_limit' => 'Дневной лимит достигнут',
                'rewards.no_ads' => 'Нет доступной рекламы',
                'rewards.try_again' => 'Попробуйте позже',
                'rewards.coins' => 'монет',

                'balance.current' => 'Текущий баланс',
                'balance.total_earned' => 'Всего заработано',
                'balance.available' => 'Доступно для вывода',
                'balance.coins' => 'монет',

                'withdrawal.title' => 'Вывод средств',
                'withdrawal.minimum' => 'Минимальный вывод: {amount} монет',
                'withdrawal.select_currency' => 'Выберите криптовалюту',
                'withdrawal.enter_address' => 'Введите адрес кошелька',
                'withdrawal.amount' => 'Сумма',
                'withdrawal.fee' => 'Комиссия сети',
                'withdrawal.total' => 'К получению',
                'withdrawal.request' => 'Запросить вывод',
                'withdrawal.success' => 'Запрос на вывод успешно отправлен!',

                'referrals.title' => 'Пригласить друзей',
                'referrals.your_code' => 'Ваш реферальный код',
                'referrals.invite_link' => 'Ссылка-приглашение',
                'referrals.friends_count' => 'Приглашено друзей',
                'referrals.total_earned' => 'Заработано с рефералов',
                'referrals.bonus_percent' => 'Вы получаете {percent}% с заработка друзей',
                'referrals.copy_link' => 'Копировать ссылку-приглашение',
                'referrals.share_link' => 'Поделиться ссылкой-приглашением',
                'referrals.invite_text' => 'Присоединяйтесь к UniQPaid и зарабатывайте криптовалюту! Используйте мою реферальную ссылку:',

                'admin.title' => 'Панель администратора',
                'admin.users' => 'Пользователи',
                'admin.withdrawals' => 'Выводы',
                'admin.statistics' => 'Статистика',
                'admin.settings' => 'Настройки',
                'admin.total_users' => 'Всего пользователей',
                'admin.active_users' => 'Активных пользователей',
                'admin.approve' => 'Одобрить',
                'admin.reject' => 'Отклонить',
                'admin.block_user' => 'Заблокировать пользователя',
                'admin.unblock_user' => 'Разблокировать пользователя',

                'errors.network' => 'Ошибка сети. Попробуйте ещё раз.',
                'errors.server' => 'Ошибка сервера. Попробуйте позже.',
                'errors.invalid_data' => 'Предоставлены неверные данные.',
                'errors.unauthorized' => 'Неавторизованный доступ.',
                'errors.rate_limit' => 'Слишком много запросов. Подождите.',
                'errors.blocked' => 'Ваш аккаунт заблокирован.',

                'status.online' => 'Онлайн',
                'status.offline' => 'Оффлайн',
                'status.loading' => 'Загрузка',
                'status.pending' => 'Ожидание',
                'status.completed' => 'Завершено',
                'status.failed' => 'Неудачно',
                'status.cancelled' => 'Отменено',

                'limits.daily_ads' => 'Дневной лимит рекламы: {current}/{max}',
                'limits.views_left' => 'Осталось просмотров: {count}',
                'limits.limit_reached' => 'Дневной лимит достигнут',

                'currencies.TON' => 'TON',
                'currencies.USDT' => 'USDT (TRC20)',
                'currencies.BTC' => 'Биткоин',
                'currencies.ETH' => 'Эфириум'
            ];

            // Import Russian translations
            $ruCount = Translation::importTranslations($ruTranslations, 'ru', 'general');
            $results['import_russian'] = [
                'status' => $ruCount > 0 ? 'success' : 'error',
                'message' => "✅ Imported {$ruCount} Russian translations"
            ];

            // Summary
            $total = $enCount + $ruCount;
            $results['summary'] = [
                'status' => 'success',
                'message' => "🎉 Total imported: {$total} translations ({$enCount} EN + {$ruCount} RU)"
            ];

        } catch (Exception $e) {
            $results['import_error'] = [
                'status' => 'error',
                'message' => "❌ Import failed: " . $e->getMessage()
            ];
        }

        $this->renderJson($results);
    }

    /**
     * Get list of available tests
     *
     * @return array<string, array<string, string>>
     */
    private function getAvailableTests(): array
    {
        return [
            'database' => [
                'name' => 'Database Connection',
                'description' => 'Test database connection and table structure',
                'url' => '/test/database'
            ],
            'translations' => [
                'name' => 'Translations System',
                'description' => 'Test translation import and localization',
                'url' => '/test/translations'
            ],
            'import' => [
                'name' => 'Import Translations',
                'description' => 'Import all English and Russian translations into database',
                'url' => '/test/import'
            ],
            'user' => [
                'name' => 'User Model',
                'description' => 'Test user creation, retrieval, and methods',
                'url' => '/test/user'
            ],
            'config' => [
                'name' => 'Configuration',
                'description' => 'Test application configuration and constants',
                'url' => '/test/config'
            ]
        ];
    }
}
