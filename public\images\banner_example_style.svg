<svg width="600" height="400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Градиенты в стиле примера -->
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2a2a2a;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#1a1a1a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0a0a0a;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="titleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#FFA500;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FFD700;stop-opacity:1" />
    </linearGradient>

    <radialGradient id="coinGradient" cx="50%" cy="30%" r="70%">
      <stop offset="0%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="70%" style="stop-color:#FFA500;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FF8C00;stop-opacity:1" />
    </radialGradient>

    <linearGradient id="buttonGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FF8C00;stop-opacity:1" />
    </linearGradient>

    <!-- Эффекты свечения -->
    <filter id="softGlow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <filter id="buttonGlow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>

  <!-- Тёмный фон как в примере -->
  <rect width="600" height="400" fill="url(#bgGradient)"/>

  <!-- Главный заголовок -->
  <text x="300" y="70" text-anchor="middle" font-family="Arial, sans-serif" font-size="42" font-weight="bold" fill="url(#titleGradient)" filter="url(#softGlow)">UniQPaid</text>

  <!-- Подзаголовок -->
  <text x="300" y="105" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="600" fill="#FFD700" opacity="0.9">Watch Ads • Earn Crypto • Instant Payouts</text>

  <!-- Центральная композиция с монетами -->
  <!-- Большая центральная монета -->
  <circle cx="300" cy="200" r="65" fill="url(#coinGradient)" stroke="#FFD700" stroke-width="3" filter="url(#buttonGlow)"/>
  <circle cx="300" cy="200" r="50" fill="none" stroke="#2d2d2d" stroke-width="2" opacity="0.8"/>
  <text x="300" y="215" text-anchor="middle" font-family="Arial, sans-serif" font-size="44" font-weight="bold" fill="#2d2d2d">#</text>

  <!-- Орбитальные монеты -->
  <!-- TON -->
  <circle cx="180" cy="140" r="28" fill="url(#coinGradient)" stroke="#FFD700" stroke-width="2" opacity="0.95"/>
  <text x="180" y="150" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#2d2d2d">TON</text>

  <!-- Bitcoin -->
  <circle cx="420" cy="140" r="28" fill="url(#coinGradient)" stroke="#FFD700" stroke-width="2" opacity="0.95"/>
  <text x="420" y="150" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#2d2d2d">₿</text>

  <!-- Ethereum -->
  <circle cx="150" cy="260" r="28" fill="url(#coinGradient)" stroke="#FFD700" stroke-width="2" opacity="0.95"/>
  <text x="150" y="270" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#2d2d2d">Ξ</text>

  <!-- USDT -->
  <circle cx="450" cy="260" r="28" fill="url(#coinGradient)" stroke="#FFD700" stroke-width="2" opacity="0.95"/>
  <text x="450" y="270" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#2d2d2d">USDT</text>

  <!-- Стильные кнопки действий -->
  <!-- Кнопка "Смотреть" -->
  <rect x="80" y="320" width="120" height="50" rx="25" fill="url(#buttonGradient)" stroke="#FFD700" stroke-width="2" filter="url(#buttonGlow)"/>
  <polygon points="125,335 125,355 145,345" fill="#2d2d2d"/>

  <!-- Кнопка "Заработать" -->
  <rect x="240" y="320" width="120" height="50" rx="25" fill="url(#buttonGradient)" stroke="#FFD700" stroke-width="2" filter="url(#buttonGlow)"/>
  <text x="300" y="350" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#2d2d2d">$</text>

  <!-- Кнопка "Вывести" -->
  <rect x="400" y="320" width="120" height="50" rx="25" fill="url(#buttonGradient)" stroke="#FFD700" stroke-width="2" filter="url(#buttonGlow)"/>
  <polygon points="445,335 465,335 465,345 445,345" fill="#2d2d2d"/>
  <polygon points="450,350 460,350 455,360 450,360" fill="#2d2d2d"/>
</svg>
