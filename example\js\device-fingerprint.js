/**
 * js/device-fingerprint.js
 * Система создания уникальных отпечатков устройств для антифрод защиты
 * 
 * Использует множественные методы fingerprinting:
 * - Canvas fingerprinting
 * - WebGL fingerprinting  
 * - Audio context fingerprinting
 * - Screen & hardware fingerprinting
 * - System fingerprinting
 */

class DeviceFingerprint {
    constructor() {
        this.fingerprint = null;
        this.components = {};
        
        console.log('[DeviceFingerprint] 🔍 Инициализация системы детекции устройств');
    }
    
    /**
     * Генерирует полный отпечаток устройства
     */
    async generateFingerprint() {
        try {
            console.log('[DeviceFingerprint] 📊 Начинаем сбор данных устройства...');
            
            // Собираем все компоненты отпечатка
            this.components = {
                canvas: await this.getCanvasFingerprint(),
                webgl: await this.getWebGLFingerprint(),
                audio: await this.getAudioFingerprint(),
                screen: this.getScreenFingerprint(),
                system: this.getSystemFingerprint(),
                navigator: this.getNavigatorFingerprint(),
                timezone: this.getTimezoneFingerprint(),
                fonts: await this.getFontsFingerprint()
            };
            
            // Создаем хеш из всех компонентов
            this.fingerprint = await this.createFingerprintHash();
            
            console.log('[DeviceFingerprint] ✅ Отпечаток устройства создан:', this.fingerprint.substring(0, 16) + '...');
            
            return {
                fingerprint: this.fingerprint,
                components: this.components,
                timestamp: Date.now()
            };
            
        } catch (error) {
            console.error('[DeviceFingerprint] ❌ Ошибка создания отпечатка:', error);
            
            // Fallback - создаем базовый отпечаток
            return this.createFallbackFingerprint();
        }
    }
    
    /**
     * Canvas fingerprinting - уникальный отпечаток на основе рендеринга
     */
    async getCanvasFingerprint() {
        try {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            canvas.width = 280;
            canvas.height = 60;
            
            // Рендерим сложную графику с текстом
            ctx.textBaseline = 'top';
            ctx.font = '14px Arial, sans-serif';
            ctx.fillStyle = '#f60';
            ctx.fillRect(125, 1, 62, 20);
            
            ctx.fillStyle = '#069';
            ctx.fillText('UniQPaid Security 🔒 Антифрод', 2, 15);
            
            ctx.fillStyle = 'rgba(102, 204, 0, 0.7)';
            ctx.fillText('Canvas fingerprint test 123', 4, 35);
            
            // Добавляем геометрические фигуры
            ctx.globalCompositeOperation = 'multiply';
            ctx.fillStyle = 'rgb(255,0,255)';
            ctx.beginPath();
            ctx.arc(50, 50, 50, 0, Math.PI * 2, true);
            ctx.closePath();
            ctx.fill();
            
            return canvas.toDataURL();
            
        } catch (error) {
            console.warn('[DeviceFingerprint] Canvas fingerprint failed:', error);
            return 'canvas_unavailable';
        }
    }
    
    /**
     * WebGL fingerprinting - отпечаток графического процессора
     */
    async getWebGLFingerprint() {
        try {
            const canvas = document.createElement('canvas');
            const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
            
            if (!gl) {
                return 'webgl_unavailable';
            }
            
            const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
            
            return {
                vendor: gl.getParameter(gl.VENDOR),
                renderer: gl.getParameter(gl.RENDERER),
                version: gl.getParameter(gl.VERSION),
                shadingLanguageVersion: gl.getParameter(gl.SHADING_LANGUAGE_VERSION),
                unmaskedVendor: debugInfo ? gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL) : 'unknown',
                unmaskedRenderer: debugInfo ? gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL) : 'unknown',
                extensions: gl.getSupportedExtensions()?.join(',') || '',
                maxTextureSize: gl.getParameter(gl.MAX_TEXTURE_SIZE),
                maxViewportDims: gl.getParameter(gl.MAX_VIEWPORT_DIMS)?.join(',') || ''
            };
            
        } catch (error) {
            console.warn('[DeviceFingerprint] WebGL fingerprint failed:', error);
            return 'webgl_error';
        }
    }
    
    /**
     * Audio context fingerprinting
     */
    async getAudioFingerprint() {
        return new Promise((resolve) => {
            try {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                
                const oscillator = audioContext.createOscillator();
                const analyser = audioContext.createAnalyser();
                const gainNode = audioContext.createGain();
                const scriptProcessor = audioContext.createScriptProcessor(4096, 1, 1);
                
                oscillator.type = 'triangle';
                oscillator.frequency.value = 1000;
                
                gainNode.gain.value = 0;
                
                oscillator.connect(analyser);
                analyser.connect(scriptProcessor);
                scriptProcessor.connect(gainNode);
                gainNode.connect(audioContext.destination);
                
                oscillator.start();
                
                let audioData = [];
                
                scriptProcessor.onaudioprocess = function(event) {
                    const inputBuffer = event.inputBuffer.getChannelData(0);
                    
                    for (let i = 0; i < inputBuffer.length; i++) {
                        audioData.push(inputBuffer[i]);
                    }
                    
                    if (audioData.length >= 1000) {
                        oscillator.stop();
                        audioContext.close();
                        
                        // Создаем хеш из аудио данных
                        const audioHash = audioData.slice(0, 100).join(',');
                        resolve(audioHash);
                    }
                };
                
                // Fallback через 500ms
                setTimeout(() => {
                    try {
                        oscillator.stop();
                        audioContext.close();
                    } catch (e) {}
                    resolve('audio_timeout');
                }, 500);
                
            } catch (error) {
                console.warn('[DeviceFingerprint] Audio fingerprint failed:', error);
                resolve('audio_unavailable');
            }
        });
    }
    
    /**
     * Screen fingerprinting
     */
    getScreenFingerprint() {
        return {
            width: screen.width,
            height: screen.height,
            availWidth: screen.availWidth,
            availHeight: screen.availHeight,
            colorDepth: screen.colorDepth,
            pixelDepth: screen.pixelDepth,
            orientation: screen.orientation?.type || 'unknown',
            devicePixelRatio: window.devicePixelRatio || 1,
            innerWidth: window.innerWidth,
            innerHeight: window.innerHeight,
            outerWidth: window.outerWidth,
            outerHeight: window.outerHeight
        };
    }
    
    /**
     * System fingerprinting
     */
    getSystemFingerprint() {
        return {
            platform: navigator.platform,
            userAgent: navigator.userAgent,
            language: navigator.language,
            languages: navigator.languages?.join(',') || '',
            hardwareConcurrency: navigator.hardwareConcurrency || 'unknown',
            deviceMemory: navigator.deviceMemory || 'unknown',
            maxTouchPoints: navigator.maxTouchPoints || 0,
            cookieEnabled: navigator.cookieEnabled,
            doNotTrack: navigator.doNotTrack || 'unknown',
            onLine: navigator.onLine
        };
    }
    
    /**
     * Navigator fingerprinting
     */
    getNavigatorFingerprint() {
        const plugins = [];
        for (let i = 0; i < navigator.plugins.length; i++) {
            plugins.push(navigator.plugins[i].name);
        }
        
        return {
            plugins: plugins.join(','),
            mimeTypes: Array.from(navigator.mimeTypes).map(mt => mt.type).join(','),
            vendor: navigator.vendor || '',
            product: navigator.product || '',
            productSub: navigator.productSub || '',
            appName: navigator.appName || '',
            appVersion: navigator.appVersion || ''
        };
    }
    
    /**
     * Timezone fingerprinting
     */
    getTimezoneFingerprint() {
        return {
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
            timezoneOffset: new Date().getTimezoneOffset(),
            locale: Intl.DateTimeFormat().resolvedOptions().locale,
            calendar: Intl.DateTimeFormat().resolvedOptions().calendar || 'unknown',
            numberingSystem: Intl.DateTimeFormat().resolvedOptions().numberingSystem || 'unknown'
        };
    }
    
    /**
     * Fonts fingerprinting
     */
    async getFontsFingerprint() {
        const testFonts = [
            'Arial', 'Helvetica', 'Times New Roman', 'Courier New', 'Verdana',
            'Georgia', 'Palatino', 'Garamond', 'Bookman', 'Comic Sans MS',
            'Trebuchet MS', 'Arial Black', 'Impact', 'Tahoma', 'Calibri'
        ];
        
        const availableFonts = [];
        
        for (const font of testFonts) {
            if (await this.isFontAvailable(font)) {
                availableFonts.push(font);
            }
        }
        
        return availableFonts.join(',');
    }
    
    /**
     * Проверяет доступность шрифта
     */
    async isFontAvailable(fontName) {
        try {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            const testText = 'mmmmmmmmmmlli';
            const testSize = '72px';
            const fallbackFont = 'monospace';
            
            ctx.font = `${testSize} ${fallbackFont}`;
            const fallbackWidth = ctx.measureText(testText).width;
            
            ctx.font = `${testSize} ${fontName}, ${fallbackFont}`;
            const testWidth = ctx.measureText(testText).width;
            
            return testWidth !== fallbackWidth;
        } catch (error) {
            return false;
        }
    }
    
    /**
     * Создает итоговый хеш отпечатка
     */
    async createFingerprintHash() {
        const dataString = JSON.stringify(this.components);
        
        if (window.crypto && window.crypto.subtle) {
            try {
                const encoder = new TextEncoder();
                const data = encoder.encode(dataString);
                const hashBuffer = await crypto.subtle.digest('SHA-256', data);
                const hashArray = Array.from(new Uint8Array(hashBuffer));
                return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
            } catch (error) {
                console.warn('[DeviceFingerprint] Crypto API failed, using fallback hash');
            }
        }
        
        // Fallback hash function
        return this.simpleHash(dataString);
    }
    
    /**
     * Простая hash функция для fallback
     */
    simpleHash(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32bit integer
        }
        return Math.abs(hash).toString(16);
    }
    
    /**
     * Fallback отпечаток для случаев ошибок
     */
    createFallbackFingerprint() {
        const basicData = {
            userAgent: navigator.userAgent,
            screen: `${screen.width}x${screen.height}`,
            timezone: new Date().getTimezoneOffset(),
            language: navigator.language,
            platform: navigator.platform,
            timestamp: Date.now()
        };
        
        return {
            fingerprint: this.simpleHash(JSON.stringify(basicData)),
            components: basicData,
            timestamp: Date.now(),
            fallback: true
        };
    }
}

// Экспортируем класс
window.DeviceFingerprint = DeviceFingerprint;

console.log('✅ [DeviceFingerprint] Модуль детекции устройств загружен');
