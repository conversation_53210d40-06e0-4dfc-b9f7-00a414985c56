<?php
declare(strict_types=1);

namespace Controllers;

/**
 * Controller.php
 * Base controller class for all application controllers
 * 
 * This abstract class provides common functionality for all controllers,
 * including request handling, response formatting, and security features.
 */
abstract class Controller
{
    /** @var array<string, mixed> Request data */
    protected array $requestData = [];
    
    /** @var array<string, mixed> Response data */
    protected array $responseData = [];
    
    /** @var int HTTP response code */
    protected int $responseCode = 200;

    /**
     * Constructor
     * Initializes request data and performs basic security checks
     */
    public function __construct()
    {
        $this->initializeRequest();
        $this->performSecurityChecks();
    }

    /**
     * Initialize request data
     */
    protected function initializeRequest(): void
    {
        // Get request method
        $method = $_SERVER['REQUEST_METHOD'] ?? 'GET';
        
        // Parse request data based on method
        if ($method === 'POST' || $method === 'PUT' || $method === 'PATCH') {
            $input = file_get_contents('php://input');
            $this->requestData = json_decode($input, true) ?? [];
            
            // Also include POST data if available
            if (!empty($_POST)) {
                $this->requestData = array_merge($this->requestData, $_POST);
            }
        } else {
            $this->requestData = $_GET;
        }
        
        // Add server data
        $this->requestData['_server'] = [
            'method' => $method,
            'uri' => $_SERVER['REQUEST_URI'] ?? '/',
            'ip' => $_SERVER['REMOTE_ADDR'] ?? null,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null,
            'timestamp' => time()
        ];
    }

    /**
     * Perform basic security checks
     */
    protected function performSecurityChecks(): void
    {
        // Rate limiting check
        if (RATE_LIMIT_ENABLED) {
            $this->checkRateLimit();
        }
        
        // CSRF protection for state-changing requests
        $method = $this->requestData['_server']['method'];
        if (in_array($method, ['POST', 'PUT', 'PATCH', 'DELETE'])) {
            $this->validateCSRF();
        }
    }

    /**
     * Check rate limiting
     */
    protected function checkRateLimit(): void
    {
        $ip = $this->requestData['_server']['ip'];
        if (!$ip) {
            return;
        }

        // Simple file-based rate limiting implementation
        $cacheDir = APP_ROOT . '/database/cache';
        if (!is_dir($cacheDir)) {
            mkdir($cacheDir, 0755, true);
        }

        $cacheFile = $cacheDir . '/rate_limit_' . md5($ip) . '.txt';
        $currentTime = time();

        // Read current request count
        $requests = 0;
        $lastReset = $currentTime;

        if (file_exists($cacheFile)) {
            $data = file_get_contents($cacheFile);
            if ($data) {
                list($requests, $lastReset) = explode('|', $data . '|0');
                $requests = (int)$requests;
                $lastReset = (int)$lastReset;
            }
        }

        // Reset counter if more than 1 minute has passed
        if ($currentTime - $lastReset >= 60) {
            $requests = 0;
            $lastReset = $currentTime;
        }

        // Check limit
        if ($requests >= RATE_LIMIT_REQUESTS_PER_MINUTE) {
            $this->respondWithError('Rate limit exceeded', 429);
            return;
        }

        // Increment and save
        $requests++;
        file_put_contents($cacheFile, $requests . '|' . $lastReset);
    }

    /**
     * Validate CSRF token (simplified implementation)
     */
    protected function validateCSRF(): void
    {
        // For Telegram Mini Apps, we rely on initData validation instead of CSRF tokens
        // This is a placeholder for future CSRF implementation if needed
    }

    /**
     * Get request parameter
     * 
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    protected function getParam(string $key, mixed $default = null): mixed
    {
        return $this->requestData[$key] ?? $default;
    }

    /**
     * Get required request parameter
     * 
     * @param string $key
     * @throws \InvalidArgumentException
     * @return mixed
     */
    protected function getRequiredParam(string $key): mixed
    {
        if (!isset($this->requestData[$key])) {
            throw new \InvalidArgumentException("Required parameter '{$key}' is missing");
        }
        
        return $this->requestData[$key];
    }

    /**
     * Validate Telegram initData
     * 
     * @return array<string, mixed> Validated user data
     * @throws \Exception If validation fails
     */
    protected function validateTelegramAuth(): array
    {
        $initData = $this->getRequiredParam('initData');
        
        if (!TELEGRAM_VALIDATION_ENABLED) {
            // For development/testing - extract user data without validation
            return $this->extractUserDataFromInitData($initData);
        }
        
        // Use TelegramValidator to validate initData
        $validator = new \TelegramValidator();
        $userData = $validator->validate($initData);
        
        if (!$userData) {
            throw new \Exception('Invalid Telegram authentication data');
        }
        
        return $userData;
    }

    /**
     * Extract user data from initData (for development)
     * 
     * @param string $initData
     * @return array<string, mixed>
     */
    private function extractUserDataFromInitData(string $initData): array
    {
        parse_str($initData, $params);
        
        if (isset($params['user'])) {
            $userData = json_decode($params['user'], true);
            if ($userData) {
                return $userData;
            }
        }
        
        // Fallback for testing
        return [
            'id' => 12345,
            'first_name' => 'Test',
            'username' => 'testuser',
            'language_code' => 'en'
        ];
    }

    /**
     * Set response data
     * 
     * @param array<string, mixed> $data
     * @param int $code
     */
    protected function setResponse(array $data, int $code = 200): void
    {
        $this->responseData = $data;
        $this->responseCode = $code;
    }

    /**
     * Send JSON response
     * 
     * @param array<string, mixed>|null $data
     * @param int|null $code
     */
    protected function respond(?array $data = null, ?int $code = null): void
    {
        $responseData = $data ?? $this->responseData;
        $responseCode = $code ?? $this->responseCode;
        
        http_response_code($responseCode);
        header('Content-Type: application/json');
        
        // Add metadata to response
        $response = [
            'success' => $responseCode < 400,
            'timestamp' => time(),
            'data' => $responseData
        ];
        
        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        exit;
    }

    /**
     * Send error response
     * 
     * @param string $message
     * @param int $code
     * @param array<string, mixed> $details
     */
    protected function respondWithError(string $message, int $code = 400, array $details = []): void
    {
        $errorData = [
            'error' => $message,
            'code' => $code
        ];
        
        if (!empty($details)) {
            $errorData['details'] = $details;
        }
        
        if (DEBUG_MODE) {
            $errorData['debug'] = [
                'request_data' => $this->requestData,
                'timestamp' => time()
            ];
        }
        
        $this->respond($errorData, $code);
    }

    /**
     * Send success response
     * 
     * @param array<string, mixed> $data
     * @param string|null $message
     */
    protected function respondWithSuccess(array $data = [], ?string $message = null): void
    {
        $responseData = $data;
        
        if ($message) {
            $responseData['message'] = $message;
        }
        
        $this->respond($responseData, 200);
    }

    /**
     * Log controller action
     * 
     * @param string $action
     * @param array<string, mixed> $context
     */
    protected function logAction(string $action, array $context = []): void
    {
        $logData = [
            'controller' => static::class,
            'action' => $action,
            'user_ip' => $this->requestData['_server']['ip'],
            'timestamp' => time(),
            'context' => $context
        ];
        
        error_log("Controller Action: " . json_encode($logData));
    }

    /**
     * Check if user is admin
     * 
     * @param int $userId
     * @return bool
     */
    protected function isAdmin(int $userId): bool
    {
        return in_array($userId, ADMIN_USER_IDS);
    }

    /**
     * Require admin access
     *
     * @param int $userId
     * @throws \Exception
     */
    protected function requireAdmin(int $userId): void
    {
        if (!$this->isAdmin($userId)) {
            throw new \Exception('Admin access required');
        }
    }

    /**
     * Render a view template
     *
     * @param string $viewName Name of the view file (without .php extension)
     * @param array<string, mixed> $data Data to pass to the view
     * @return void
     */
    protected function render(string $viewName, array $data = []): void
    {
        // Extract data variables for use in the view
        extract($data);

        // Construct the view file path
        $viewPath = __DIR__ . '/../../views/' . $viewName . '.php';

        // Check if view file exists
        if (!file_exists($viewPath)) {
            throw new \Exception("View file not found: {$viewPath}");
        }

        // Include the view file
        include $viewPath;
    }
}
