<?php
declare(strict_types=1);

/**
 * test.php - Main testing file for UniQPaid
 * 
 * This file handles all testing functionality and routes.
 * All tests should be conducted through this file and accessible via test routes.
 */

// Define APP_ROOT if not already defined
if (!defined('APP_ROOT')) {
    define('APP_ROOT', __DIR__);
}

// Include necessary files
require_once APP_ROOT . '/app/config.php';
require_once APP_ROOT . '/app/Router.php';
require_once APP_ROOT . '/app/Controllers/TestController.php';

// Initialize router
$router = new Router();

// Test routes
$router->addRoute('GET', '/test', 'TestController@index');
$router->addRoute('GET', '/test/', 'TestController@index');
$router->addRoute('GET', '/test/database', 'TestController@testDatabase');
$router->addRoute('GET', '/test/translations', 'TestController@testTranslations');
$router->addRoute('GET', '/test/import', 'TestController@importTranslations');
$router->addRoute('GET', '/test/user', 'TestController@testUser');
$router->addRoute('GET', '/test/config', 'TestController@testConfig');

// Handle the request
try {
    $method = $_SERVER['REQUEST_METHOD'] ?? 'GET';
    $uri = $_SERVER['REQUEST_URI'] ?? '/test';
    
    // Remove script name from URI if present
    $scriptName = $_SERVER['SCRIPT_NAME'] ?? '';
    if (!empty($scriptName) && str_starts_with($uri, $scriptName)) {
        $uri = substr($uri, strlen($scriptName));
    }
    
    // Default to /test if empty
    if (empty($uri) || $uri === '/') {
        $uri = '/test';
    }
    
    // Handle the request
    $router->handleRequest($method, $uri);
    
} catch (Exception $e) {
    // Error handling
    http_response_code(500);
    
    // Check if this is an AJAX request
    $isAjax = !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
              strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    
    if ($isAjax || str_contains($_SERVER['HTTP_ACCEPT'] ?? '', 'application/json')) {
        // Return JSON error for AJAX requests
        header('Content-Type: application/json');
        echo json_encode([
            'error' => 'Test execution failed',
            'message' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]);
    } else {
        // Return HTML error page
        ?>
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Test Error - UniQPaid</title>
            <style>
                body { 
                    font-family: Arial, sans-serif; 
                    background: #f8f9fa; 
                    margin: 0; 
                    padding: 20px; 
                }
                .error-container { 
                    max-width: 800px; 
                    margin: 0 auto; 
                    background: white; 
                    border-radius: 10px; 
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
                    overflow: hidden;
                }
                .error-header { 
                    background: #dc3545; 
                    color: white; 
                    padding: 20px; 
                    text-align: center; 
                }
                .error-content { 
                    padding: 30px; 
                }
                .error-details { 
                    background: #f8f9fa; 
                    border-left: 4px solid #dc3545; 
                    padding: 15px; 
                    margin: 20px 0; 
                    border-radius: 0 5px 5px 0;
                }
                .back-btn { 
                    background: #007bff; 
                    color: white; 
                    padding: 10px 20px; 
                    text-decoration: none; 
                    border-radius: 5px; 
                    display: inline-block; 
                    margin-top: 20px;
                }
                .back-btn:hover { 
                    background: #0056b3; 
                    text-decoration: none; 
                    color: white; 
                }
                pre { 
                    background: #f1f3f4; 
                    padding: 15px; 
                    border-radius: 5px; 
                    overflow-x: auto; 
                    font-size: 0.9em;
                }
            </style>
        </head>
        <body>
            <div class="error-container">
                <div class="error-header">
                    <h1>🚨 Test Execution Error</h1>
                    <p>An error occurred while running the test</p>
                </div>
                <div class="error-content">
                    <div class="error-details">
                        <h3>Error Details:</h3>
                        <p><strong>Message:</strong> <?= htmlspecialchars($e->getMessage()) ?></p>
                        <p><strong>File:</strong> <?= htmlspecialchars($e->getFile()) ?></p>
                        <p><strong>Line:</strong> <?= $e->getLine() ?></p>
                    </div>
                    
                    <h3>Stack Trace:</h3>
                    <pre><?= htmlspecialchars($e->getTraceAsString()) ?></pre>
                    
                    <h3>Request Information:</h3>
                    <div class="error-details">
                        <p><strong>Method:</strong> <?= htmlspecialchars($method) ?></p>
                        <p><strong>URI:</strong> <?= htmlspecialchars($uri) ?></p>
                        <p><strong>Script:</strong> <?= htmlspecialchars($_SERVER['SCRIPT_NAME'] ?? 'N/A') ?></p>
                    </div>
                    
                    <a href="/test" class="back-btn">← Back to Test Suite</a>
                    <a href="/" class="back-btn">← Back to Application</a>
                </div>
            </div>
        </body>
        </html>
        <?php
    }
}
?>
