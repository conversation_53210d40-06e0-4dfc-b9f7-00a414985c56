# Защита locales директории
# Настройки для корректной работы с Telegram мини-приложением

<IfModule mod_headers.c>
    # РАЗРЕШАЕМ отображение в iframe для Telegram
    Header always unset X-Frame-Options
    Header always set X-Frame-Options "ALLOWALL"

    # Разрешаем CORS для JSON файлов
    Header always set Access-Control-Allow-Origin "*"
    Header always set Access-Control-Allow-Methods "GET, OPTIONS"
    Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With"
</IfModule>

# Запрещаем доступ ко всем файлам по умолчанию
<Files "*">
    Order Deny,Allow
    Deny from all
</Files>

# Разрешаем доступ к JSON файлам локализации для AJAX запросов
<Files "*.json">
    Order Allow,Deny
    Allow from all
    
    # Устанавливаем правильный Content-Type для JSON
    <IfModule mod_headers.c>
        Header set Content-Type "application/json; charset=utf-8"
    </IfModule>
</Files>

# Запрещаем просмотр директории
Options -Indexes
