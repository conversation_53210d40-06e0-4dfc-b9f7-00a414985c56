/**
 * js/vpn-detector.js
 * Модуль детекции VPN/Proxy соединений
 * 
 * Использует множественные методы для обнаружения:
 * - WebRTC IP утечки
 * - Анализ таймингов соединений
 * - DNS резолюция
 * - Геолокационные несоответствия
 * - Анализ заголовков
 */

class VPNDetector {
    constructor() {
        this.detectionResults = {
            webrtc_leak: null,
            timing_analysis: null,
            dns_analysis: null,
            geolocation_mismatch: null,
            header_analysis: null,
            risk_score: 0,
            is_vpn_detected: false
        };
        
        this.vpnIndicators = [];
        this.detectionComplete = false;
    }

    /**
     * Запускает полную детекцию VPN/Proxy
     */
    async detectVPN() {
        console.log('VPN Detector: Начинаем детекцию VPN/Proxy...');
        
        try {
            // Запускаем все методы детекции параллельно
            const detectionPromises = [
                this.detectWebRTCLeak(),
                this.analyzeConnectionTiming(),
                this.analyzeDNS(),
                this.checkGeolocationMismatch(),
                this.analyzeHeaders()
            ];
            
            // Ждем завершения всех проверок (максимум 10 секунд)
            await Promise.allSettled(detectionPromises);
            
            // Вычисляем итоговый риск-скор
            this.calculateRiskScore();
            
            this.detectionComplete = true;
            console.log('VPN Detector: Детекция завершена', this.detectionResults);
            
            return this.detectionResults;
            
        } catch (error) {
            console.error('VPN Detector ERROR:', error);
            this.detectionResults.risk_score = 10; // Минимальный риск при ошибке
            return this.detectionResults;
        }
    }

    /**
     * Детекция утечек IP через WebRTC
     */
    async detectWebRTCLeak() {
        return new Promise((resolve) => {
            try {
                const timeout = setTimeout(() => {
                    this.detectionResults.webrtc_leak = { detected: false, reason: 'timeout' };
                    resolve();
                }, 3000);

                const rtc = new RTCPeerConnection({
                    iceServers: [{ urls: 'stun:stun.l.google.com:19302' }]
                });

                const ips = new Set();
                
                rtc.onicecandidate = (event) => {
                    if (event.candidate) {
                        const candidate = event.candidate.candidate;
                        const ipMatch = candidate.match(/(\d+\.\d+\.\d+\.\d+)/);
                        if (ipMatch) {
                            ips.add(ipMatch[1]);
                        }
                    }
                };

                rtc.createDataChannel('test');
                rtc.createOffer().then(offer => rtc.setLocalDescription(offer));

                setTimeout(() => {
                    clearTimeout(timeout);
                    const ipArray = Array.from(ips);
                    const hasMultipleIPs = ipArray.length > 1;
                    const hasPrivateIP = ipArray.some(ip => this.isPrivateIP(ip));
                    
                    this.detectionResults.webrtc_leak = {
                        detected: hasMultipleIPs || hasPrivateIP,
                        ips: ipArray,
                        multiple_ips: hasMultipleIPs,
                        private_ip: hasPrivateIP
                    };
                    
                    if (hasMultipleIPs) this.vpnIndicators.push('multiple_webrtc_ips');
                    if (hasPrivateIP) this.vpnIndicators.push('private_ip_leak');
                    
                    rtc.close();
                    resolve();
                }, 2000);

            } catch (error) {
                console.error('WebRTC detection error:', error);
                this.detectionResults.webrtc_leak = { detected: false, error: error.message };
                resolve();
            }
        });
    }

    /**
     * Анализ таймингов соединений
     */
    async analyzeConnectionTiming() {
        try {
            const startTime = performance.now();
            
            // Тестируем соединения к разным серверам
            const testUrls = [
                'https://www.google.com/favicon.ico',
                'https://www.cloudflare.com/favicon.ico',
                'https://httpbin.org/ip'
            ];
            
            const timings = [];
            
            for (const url of testUrls) {
                try {
                    const requestStart = performance.now();
                    await fetch(url, { 
                        method: 'HEAD', 
                        mode: 'no-cors',
                        cache: 'no-cache'
                    });
                    const requestEnd = performance.now();
                    timings.push(requestEnd - requestStart);
                } catch (error) {
                    timings.push(5000); // Высокий тайминг при ошибке
                }
            }
            
            const avgTiming = timings.reduce((a, b) => a + b, 0) / timings.length;
            const maxTiming = Math.max(...timings);
            const isSlowConnection = avgTiming > 2000 || maxTiming > 5000;
            
            this.detectionResults.timing_analysis = {
                average_timing: avgTiming,
                max_timing: maxTiming,
                slow_connection: isSlowConnection,
                timings: timings
            };
            
            if (isSlowConnection) this.vpnIndicators.push('slow_connection');
            
        } catch (error) {
            console.error('Timing analysis error:', error);
            this.detectionResults.timing_analysis = { error: error.message };
        }
    }

    /**
     * DNS анализ
     */
    async analyzeDNS() {
        try {
            // Проверяем DNS резолюцию через разные методы
            const dnsTests = [];
            
            // Тест 1: Проверка DNS over HTTPS
            try {
                const response = await fetch('https://cloudflare-dns.com/dns-query?name=google.com&type=A', {
                    headers: { 'Accept': 'application/dns-json' }
                });
                const dnsData = await response.json();
                dnsTests.push({ method: 'doh', success: true, data: dnsData });
            } catch (error) {
                dnsTests.push({ method: 'doh', success: false, error: error.message });
            }
            
            // Тест 2: Анализ времени DNS резолюции
            const dnsStart = performance.now();
            try {
                await fetch('https://www.google.com/favicon.ico', { method: 'HEAD', mode: 'no-cors' });
                const dnsEnd = performance.now();
                const dnsTime = dnsEnd - dnsStart;
                dnsTests.push({ method: 'timing', dns_time: dnsTime, slow_dns: dnsTime > 1000 });
                
                if (dnsTime > 1000) this.vpnIndicators.push('slow_dns');
            } catch (error) {
                dnsTests.push({ method: 'timing', error: error.message });
            }
            
            this.detectionResults.dns_analysis = {
                tests: dnsTests,
                suspicious: dnsTests.some(test => test.slow_dns || !test.success)
            };
            
        } catch (error) {
            console.error('DNS analysis error:', error);
            this.detectionResults.dns_analysis = { error: error.message };
        }
    }

    /**
     * Проверка несоответствий геолокации
     */
    async checkGeolocationMismatch() {
        try {
            // Получаем геолокацию через разные API
            const geoSources = [];
            
            // Источник 1: IP геолокация
            try {
                const ipResponse = await fetch('https://httpbin.org/ip');
                const ipData = await ipResponse.json();
                geoSources.push({ source: 'httpbin', ip: ipData.origin });
            } catch (error) {
                console.error('IP detection error:', error);
            }
            
            // Источник 2: Браузерная геолокация (ОТКЛЮЧЕНО - не запрашиваем разрешение)
            // Комментируем запрос геолокации, чтобы не беспокоить пользователя
            geoSources.push({ source: 'browser', status: 'disabled_to_avoid_permission_request' });
            
            // Источник 3: Timezone анализ
            const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
            geoSources.push({ source: 'timezone', timezone: timezone });
            
            this.detectionResults.geolocation_mismatch = {
                sources: geoSources,
                timezone: timezone,
                suspicious: false // Будет обновлено на сервере при сравнении с IP геолокацией
            };
            
        } catch (error) {
            console.error('Geolocation analysis error:', error);
            this.detectionResults.geolocation_mismatch = { error: error.message };
        }
    }

    /**
     * Анализ заголовков и характеристик соединения
     */
    async analyzeHeaders() {
        try {
            const headerAnalysis = {
                user_agent: navigator.userAgent,
                language: navigator.language,
                languages: navigator.languages,
                platform: navigator.platform,
                cookie_enabled: navigator.cookieEnabled,
                do_not_track: navigator.doNotTrack,
                connection: navigator.connection ? {
                    effective_type: navigator.connection.effectiveType,
                    downlink: navigator.connection.downlink,
                    rtt: navigator.connection.rtt
                } : null
            };
            
            // Проверяем подозрительные паттерны
            const suspiciousPatterns = [
                /headless/i,
                /phantom/i,
                /selenium/i,
                /webdriver/i,
                /bot/i
            ];
            
            const hasSuspiciousUA = suspiciousPatterns.some(pattern => 
                pattern.test(headerAnalysis.user_agent)
            );
            
            if (hasSuspiciousUA) this.vpnIndicators.push('suspicious_user_agent');
            
            this.detectionResults.header_analysis = {
                ...headerAnalysis,
                suspicious_user_agent: hasSuspiciousUA
            };
            
        } catch (error) {
            console.error('Header analysis error:', error);
            this.detectionResults.header_analysis = { error: error.message };
        }
    }

    /**
     * Вычисляет итоговый риск-скор
     */
    calculateRiskScore() {
        let score = 0;
        
        // WebRTC утечки
        if (this.detectionResults.webrtc_leak?.detected) {
            score += this.detectionResults.webrtc_leak.multiple_ips ? 30 : 20;
        }
        
        // Медленное соединение
        if (this.detectionResults.timing_analysis?.slow_connection) {
            score += 15;
        }
        
        // DNS проблемы
        if (this.detectionResults.dns_analysis?.suspicious) {
            score += 10;
        }
        
        // Подозрительный User Agent
        if (this.detectionResults.header_analysis?.suspicious_user_agent) {
            score += 25;
        }
        
        // Дополнительные индикаторы
        score += this.vpnIndicators.length * 5;
        
        this.detectionResults.risk_score = Math.min(score, 100);
        this.detectionResults.is_vpn_detected = score >= 30;
        this.detectionResults.indicators = this.vpnIndicators;
    }

    /**
     * Проверяет, является ли IP приватным
     */
    isPrivateIP(ip) {
        const parts = ip.split('.').map(Number);
        return (
            (parts[0] === 10) ||
            (parts[0] === 172 && parts[1] >= 16 && parts[1] <= 31) ||
            (parts[0] === 192 && parts[1] === 168) ||
            (parts[0] === 127)
        );
    }

    /**
     * Получает результаты детекции
     */
    getResults() {
        return {
            ...this.detectionResults,
            detection_complete: this.detectionComplete
        };
    }

    /**
     * Проверка IP через IPAPI сервис
     */
    async checkIPAPI(ip = null) {
        try {
            console.log('[VPNDetector] 🌐 Проверка через IPAPI...');

            const targetIP = ip || await this.getCurrentIP();
            if (!targetIP) {
                throw new Error('Не удалось получить IP адрес');
            }

            // Симуляция API вызова (в реальности здесь был бы fetch к IPAPI)
            const mockResult = {
                ip: targetIP,
                country: 'Unknown',
                isp: 'Unknown ISP',
                isVPN: false,
                isProxy: false,
                riskScore: 0
            };

            // Простая эвристика для определения VPN
            if (targetIP.startsWith('10.') || targetIP.startsWith('192.168.') || targetIP === '127.0.0.1') {
                mockResult.isVPN = true;
                mockResult.riskScore = 80;
                mockResult.isp = 'Private Network';
            }

            console.log('[VPNDetector] 📊 IPAPI результат:', mockResult);
            return mockResult;

        } catch (error) {
            console.error('[VPNDetector] ❌ Ошибка IPAPI:', error);
            return { error: error.message, isVPN: false, riskScore: 0 };
        }
    }

    /**
     * Проверка IP через IPInfo сервис
     */
    async checkIPInfo(ip = null) {
        try {
            console.log('[VPNDetector] 🌐 Проверка через IPInfo...');

            const targetIP = ip || await this.getCurrentIP();
            if (!targetIP) {
                throw new Error('Не удалось получить IP адрес');
            }

            // Симуляция API вызова
            const mockResult = {
                ip: targetIP,
                city: 'Unknown',
                region: 'Unknown',
                country: 'Unknown',
                org: 'Unknown Organization',
                isVPN: false,
                riskScore: 0
            };

            // Проверка на известные VPN провайдеры
            const vpnKeywords = ['vpn', 'proxy', 'tor', 'anonymous', 'private'];
            const orgLower = mockResult.org.toLowerCase();

            for (const keyword of vpnKeywords) {
                if (orgLower.includes(keyword)) {
                    mockResult.isVPN = true;
                    mockResult.riskScore = 70;
                    break;
                }
            }

            console.log('[VPNDetector] 📊 IPInfo результат:', mockResult);
            return mockResult;

        } catch (error) {
            console.error('[VPNDetector] ❌ Ошибка IPInfo:', error);
            return { error: error.message, isVPN: false, riskScore: 0 };
        }
    }

    /**
     * Проверка IP через IPGeolocation сервис
     */
    async checkIPGeolocation(ip = null) {
        try {
            console.log('[VPNDetector] 🌐 Проверка через IPGeolocation...');

            const targetIP = ip || await this.getCurrentIP();
            if (!targetIP) {
                throw new Error('Не удалось получить IP адрес');
            }

            // Симуляция API вызова
            const mockResult = {
                ip: targetIP,
                country_name: 'Unknown',
                isp: 'Unknown ISP',
                threat: {
                    is_tor: false,
                    is_proxy: false,
                    is_anonymous: false,
                    is_known_attacker: false,
                    is_known_abuser: false,
                    is_threat: false,
                    is_bogon: false
                },
                isVPN: false,
                riskScore: 0
            };

            // Анализ угроз
            if (mockResult.threat.is_tor || mockResult.threat.is_proxy || mockResult.threat.is_anonymous) {
                mockResult.isVPN = true;
                mockResult.riskScore = 90;
            }

            console.log('[VPNDetector] 📊 IPGeolocation результат:', mockResult);
            return mockResult;

        } catch (error) {
            console.error('[VPNDetector] ❌ Ошибка IPGeolocation:', error);
            return { error: error.message, isVPN: false, riskScore: 0 };
        }
    }

    /**
     * Проверка IP через ProxyCheck сервис
     */
    async checkProxyCheck(ip = null) {
        try {
            console.log('[VPNDetector] 🌐 Проверка через ProxyCheck...');

            const targetIP = ip || await this.getCurrentIP();
            if (!targetIP) {
                throw new Error('Не удалось получить IP адрес');
            }

            // Симуляция API вызова
            const mockResult = {
                ip: targetIP,
                proxy: 'no',
                type: 'residential',
                provider: 'Unknown',
                country: 'Unknown',
                isVPN: false,
                riskScore: 0
            };

            // Проверка типа соединения
            if (mockResult.proxy === 'yes' || mockResult.type === 'vpn') {
                mockResult.isVPN = true;
                mockResult.riskScore = 85;
            }

            console.log('[VPNDetector] 📊 ProxyCheck результат:', mockResult);
            return mockResult;

        } catch (error) {
            console.error('[VPNDetector] ❌ Ошибка ProxyCheck:', error);
            return { error: error.message, isVPN: false, riskScore: 0 };
        }
    }

    /**
     * Проверка IP через VPNAPI сервис
     */
    async checkVPNAPI(ip = null) {
        try {
            console.log('[VPNDetector] 🌐 Проверка через VPNAPI...');

            const targetIP = ip || await this.getCurrentIP();
            if (!targetIP) {
                throw new Error('Не удалось получить IP адрес');
            }

            // Симуляция API вызова
            const mockResult = {
                ip: targetIP,
                security: {
                    vpn: false,
                    proxy: false,
                    tor: false,
                    relay: false
                },
                location: {
                    country: 'Unknown',
                    city: 'Unknown'
                },
                network: {
                    network: 'Unknown',
                    autonomous_system_number: null,
                    autonomous_system_organization: 'Unknown'
                },
                isVPN: false,
                riskScore: 0
            };

            // Анализ безопасности
            if (mockResult.security.vpn || mockResult.security.proxy || mockResult.security.tor) {
                mockResult.isVPN = true;
                mockResult.riskScore = 95;
            }

            console.log('[VPNDetector] 📊 VPNAPI результат:', mockResult);
            return mockResult;

        } catch (error) {
            console.error('[VPNDetector] ❌ Ошибка VPNAPI:', error);
            return { error: error.message, isVPN: false, riskScore: 0 };
        }
    }

    /**
     * Получение текущего IP адреса
     */
    async getCurrentIP() {
        try {
            // В реальности здесь был бы вызов к внешнему сервису
            // Для тестирования возвращаем mock IP
            return '**************'; // Пример IP
        } catch (error) {
            console.error('[VPNDetector] ❌ Ошибка получения IP:', error);
            return null;
        }
    }
}

// Экспорт для использования в других модулях
window.VPNDetector = VPNDetector;
