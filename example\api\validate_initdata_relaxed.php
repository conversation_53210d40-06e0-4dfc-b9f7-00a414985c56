<?php
/**
 * Упрощенная валидация initData для тестирования
 */

function validateTelegramInitData(string $initData): array|false {
    if (empty($initData)) {
        error_log("validateTelegramInitData: initData пустая.");
        return false;
    }
    
    $initDataParts = [];
    parse_str($initData, $initDataParts);

    if (!isset($initDataParts['user'])) {
        error_log("validateTelegramInitData: Отсутствует user.");
        return false;
    }

    $userArray = json_decode($initDataParts['user'], true);
    if ($userArray === null || !isset($userArray['id'])) {
        error_log("validateTelegramInitData: Невалидный JSON user или отсутствует user ID.");
        return false;
    }

    // Для тестирования - пропускаем проверку хеша
    error_log("validateTelegramInitData: RELAXED MODE - пропускаем проверку хеша для пользователя " . $userArray['id']);
    
    // Возвращаем данные с декодированным user
    $initDataParts['user'] = $userArray;
    return $initDataParts;
}
?>
