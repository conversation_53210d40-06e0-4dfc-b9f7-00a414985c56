/**
 * 🎯 СИСТЕМА ТАБОВ ДЛЯ КАЛЬКУЛЯТОРА И ЗАЯВКИ НА ВЫВОД
 * Управляет переключением между разделами и синхронизацией данных
 */

class WithdrawalTabs {
    constructor() {
        this.activeTab = null; // Нет активного таба по умолчанию
        this.calculatorData = {
            amount: 0,
            currency: 'eth',
            cryptoAmount: 0
        };

        this.init();
    }

    init() {
        this.bindEvents();
        this.hideAllSections(); // Скрываем все разделы по умолчанию
        console.log('🎯 Withdrawal Tabs initialized - both sections hidden');
    }

    bindEvents() {
        // Обработчики кликов по табам
        document.getElementById('calculator-tab')?.addEventListener('click', () => {
            this.switchTab('calculator');
        });

        document.getElementById('withdrawal-tab')?.addEventListener('click', () => {
            this.switchTab('withdrawal');
        });

        // Слушаем изменения в калькуляторе для синхронизации
        document.getElementById('calc-amount')?.addEventListener('input', (e) => {
            this.updateCalculatorData();
        });

        // Слушаем изменения валюты в калькуляторе
        document.querySelectorAll('.currency-tab').forEach(tab => {
            tab.addEventListener('click', () => {
                setTimeout(() => {
                    this.updateCalculatorData();
                    this.syncCurrencyToWithdrawal();
                }, 100);
            });
        });

        // Добавляем обработчик для "умной ссылки" в калькуляторе
        this.addSmartLinkHandler();
    }

    switchTab(tabName) {
        if (this.activeTab === tabName) return;

        console.log(`🔄 Switching to ${tabName} tab`);
        
        // Обновляем активный таб
        this.activeTab = tabName;
        
        // Обновляем визуальное состояние табов
        this.updateTabsVisual();
        
        // Показываем нужный раздел
        this.showSection(tabName);
        
        // Синхронизируем данные при переходе на вывод
        if (tabName === 'withdrawal') {
            this.syncDataToWithdrawal();
        }
    }

    updateTabsVisual() {
        // Убираем активный класс со всех табов
        document.querySelectorAll('.withdrawal-tab').forEach(tab => {
            tab.classList.remove('active');
        });

        // Добавляем активный класс к текущему табу (если есть)
        if (this.activeTab) {
            const activeTabElement = document.getElementById(`${this.activeTab}-tab`);
            if (activeTabElement) {
                activeTabElement.classList.add('active');
            }
        }
    }

    hideAllSections() {
        // Скрываем все разделы по умолчанию
        const calculatorSection = document.getElementById('calculator-section');
        const withdrawalSection = document.getElementById('withdrawal-section');

        if (calculatorSection) {
            calculatorSection.classList.add('hidden');
        }

        if (withdrawalSection) {
            withdrawalSection.classList.add('hidden');
        }

        console.log('👁️ All sections hidden');
    }

    showSection(sectionName) {
        // Скрываем все разделы
        const calculatorSection = document.getElementById('calculator-section');
        const withdrawalSection = document.getElementById('withdrawal-section');

        if (calculatorSection) {
            calculatorSection.classList.toggle('hidden', sectionName !== 'calculator');
        }

        if (withdrawalSection) {
            withdrawalSection.classList.toggle('hidden', sectionName !== 'withdrawal');
        }

        console.log(`👁️ Showing ${sectionName} section`);
    }

    updateCalculatorData() {
        const amountInput = document.getElementById('calc-amount');
        const activeCurrencyTab = document.querySelector('.currency-tab.active');
        
        if (amountInput && activeCurrencyTab) {
            this.calculatorData.amount = parseFloat(amountInput.value) || 0;
            this.calculatorData.currency = activeCurrencyTab.dataset.currency;
            
            // Получаем рассчитанную сумму криптовалюты
            const finalAmountDisplay = document.getElementById('final-amount-display');
            if (finalAmountDisplay && finalAmountDisplay.textContent !== '-') {
                // Извлекаем числовое значение из текста
                const match = finalAmountDisplay.textContent.match(/[\d.]+/);
                this.calculatorData.cryptoAmount = match ? parseFloat(match[0]) : 0;
            }
        }
    }

    syncCurrencyToWithdrawal() {
        const cryptoSelect = document.getElementById('crypto-currency');
        if (cryptoSelect && this.calculatorData.currency) {
            cryptoSelect.value = this.calculatorData.currency;
            
            // Триггерим событие change для обновления формы
            cryptoSelect.dispatchEvent(new Event('change'));
        }
    }

    syncDataToWithdrawal() {
        console.log('🔄 Syncing data to withdrawal form:', this.calculatorData);
        
        // Синхронизируем валюту
        this.syncCurrencyToWithdrawal();
        
        // Синхронизируем сумму
        const withdrawalAmountInput = document.getElementById('withdrawal-amount');
        if (withdrawalAmountInput && this.calculatorData.amount > 0) {
            withdrawalAmountInput.value = this.calculatorData.amount;
            
            // Триггерим событие input для обновления расчетов
            withdrawalAmountInput.dispatchEvent(new Event('input'));
        }
    }

    addSmartLinkHandler() {
        // Создаем наблюдатель за изменениями в статусе калькулятора
        const observer = new MutationObserver(() => {
            this.checkForSmartLink();
        });

        const statusCard = document.getElementById('action-status');
        if (statusCard) {
            observer.observe(statusCard, {
                childList: true,
                subtree: true,
                characterData: true
            });
        }

        // Также проверяем при загрузке
        setTimeout(() => this.checkForSmartLink(), 1000);
    }

    checkForSmartLink() {
        const statusCard = document.getElementById('action-status');
        const statusText = statusCard?.querySelector('.status-text');

        if (!statusCard || !statusText) return;

        // Проверяем, что статус показывает готовность к выводу
        const isReadyForWithdrawal = statusCard.classList.contains('available') &&
                                   (statusText.textContent.includes('Готово к выводу') ||
                                    statusText.textContent.includes('ready_for_withdrawal'));

        if (isReadyForWithdrawal) {
            // Делаем текст кликабельным
            statusText.style.cursor = 'pointer';
            statusText.style.color = '#4CAF50';
            statusText.style.textDecoration = 'underline';
            statusText.title = 'Нажмите, чтобы перейти к оформлению заявки';
            statusText.classList.add('smart-link');

            // Добавляем обработчик клика
            statusText.onclick = () => {
                this.handleSmartLinkClick();
            };
        } else {
            // Убираем умную ссылку если условия не выполнены
            statusText.style.cursor = 'default';
            statusText.style.color = '';
            statusText.style.textDecoration = 'none';
            statusText.title = '';
            statusText.classList.remove('smart-link');
            statusText.onclick = null;
        }
    }

    handleSmartLinkClick() {
        console.log('🎯 Smart link clicked - switching to withdrawal');
        
        // Обновляем данные калькулятора
        this.updateCalculatorData();
        
        // Переключаемся на таб вывода
        this.switchTab('withdrawal');
        
        // Добавляем визуальный эффект
        setTimeout(() => {
            const withdrawalSection = document.getElementById('withdrawal-section');
            if (withdrawalSection) {
                withdrawalSection.style.animation = 'pulse 0.6s ease-in-out';
                setTimeout(() => {
                    withdrawalSection.style.animation = '';
                }, 600);
            }
        }, 100);
    }

    // Публичный метод для переключения табов из внешнего кода
    switchToTab(tabName) {
        this.switchTab(tabName);
    }

    // Публичный метод для получения текущих данных
    getCurrentData() {
        return {
            activeTab: this.activeTab,
            calculatorData: { ...this.calculatorData }
        };
    }

    // Публичный метод для проверки умной ссылки (вызывается из main.js)
    checkSmartLink() {
        this.checkForSmartLink();
    }
}

// Инициализируем систему табов
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.withdrawalTabs = new WithdrawalTabs();
    });
} else {
    // DOM уже готов
    window.withdrawalTabs = new WithdrawalTabs();
}

// Экспорт в глобальную область видимости
console.log('🎯 [WithdrawalTabs] Система табов загружена');
