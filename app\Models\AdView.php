<?php
declare(strict_types=1);

namespace Models;

/**
 * AdView.php
 * Ad view model for tracking advertisement views and rewards
 * 
 * This model handles recording ad views, calculating rewards,
 * and enforcing daily limits and fraud prevention.
 */
class AdView extends Model
{
    /** @var string Table name */
    protected string $table = 'ad_views';
    
    /** @var array<string> Mass assignable fields */
    protected array $fillable = [
        'user_id',
        'ad_type',
        'ad_token',
        'reward_amount',
        'viewed_at',
        'ip_address',
        'user_agent',
        'session_id'
    ];

    /** @var array<string> Valid ad types */
    public const VALID_AD_TYPES = [
        'native_banner',
        'interstitial', 
        'rewarded_video'
    ];

    /**
     * Record a new ad view
     * 
     * @param int $userId
     * @param string $adType
     * @param string|null $adToken
     * @param array<string, mixed> $metadata
     * @return AdView|null
     */
    public static function recordView(
        int $userId, 
        string $adType, 
        ?string $adToken = null,
        array $metadata = []
    ): ?AdView {
        // Validate ad type
        if (!in_array($adType, self::VALID_AD_TYPES)) {
            error_log("Invalid ad type: {$adType}");
            return null;
        }

        // Get user
        $user = User::findByTelegramId($userId);
        if (!$user) {
            error_log("User not found: {$userId}");
            return null;
        }

        // Check if user can view this ad type
        if (!$user->canViewAd($adType)) {
            error_log("User {$userId} cannot view {$adType} - blocked or limit reached");
            return null;
        }

        // Calculate reward
        $rewardAmount = self::getRewardAmount($adType);
        
        // Create ad view record
        $adView = new self([
            'user_id' => $userId,
            'ad_type' => $adType,
            'ad_token' => $adToken,
            'reward_amount' => $rewardAmount,
            'viewed_at' => time(),
            'ip_address' => $metadata['ip_address'] ?? $_SERVER['REMOTE_ADDR'] ?? null,
            'user_agent' => $metadata['user_agent'] ?? $_SERVER['HTTP_USER_AGENT'] ?? null,
            'session_id' => $metadata['session_id'] ?? session_id()
        ]);

        if ($adView->save()) {
            // Add reward to user balance
            $user->addBalance($rewardAmount, "ad_view_{$adType}");
            
            // Check for referral bonus
            if ($user->referrer_id) {
                self::processReferralBonus($user->referrer_id, $rewardAmount);
            }
            
            error_log("Ad view recorded: User {$userId}, Type {$adType}, Reward {$rewardAmount}");
            return $adView;
        }

        return null;
    }

    /**
     * Get reward amount for ad type
     * 
     * @param string $adType
     * @return int
     */
    public static function getRewardAmount(string $adType): int
    {
        $rewards = [
            'native_banner' => AD_REWARD_NATIVE_BANNER,
            'interstitial' => AD_REWARD_INTERSTITIAL,
            'rewarded_video' => AD_REWARD_REWARDED_VIDEO
        ];

        return $rewards[$adType] ?? 0;
    }

    /**
     * Process referral bonus
     * 
     * @param int $referrerId
     * @param int $baseReward
     */
    private static function processReferralBonus(int $referrerId, int $baseReward): void
    {
        $referrer = User::findByTelegramId($referrerId);
        if (!$referrer || $referrer->blocked) {
            return;
        }

        $bonusAmount = (int)($baseReward * REFERRAL_BONUS_PERCENT);
        if ($bonusAmount > 0) {
            $referrer->addBalance($bonusAmount, 'referral_bonus');
            $referrer->referral_earnings = ($referrer->referral_earnings ?? 0) + $bonusAmount;
            $referrer->save();
            
            error_log("Referral bonus: {$bonusAmount} coins to user {$referrerId}");
        }
    }

    /**
     * Get ad views for user in date range
     * 
     * @param int $userId
     * @param int $startTime
     * @param int $endTime
     * @return array<AdView>
     */
    public static function getUserViewsInRange(int $userId, int $startTime, int $endTime): array
    {
        $instance = new self();
        $sql = "SELECT * FROM {$instance->table} 
                WHERE user_id = ? AND viewed_at >= ? AND viewed_at < ? 
                ORDER BY viewed_at DESC";
        
        try {
            $stmt = $instance->getConnection()->prepare($sql);
            $stmt->execute([$userId, $startTime, $endTime]);
            $results = [];
            
            while ($data = $stmt->fetch()) {
                $model = new self($data);
                $model->exists = true;
                $results[] = $model;
            }
            
            return $results;
        } catch (\PDOException $e) {
            error_log("Error getting user ad views: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get today's ad views for user by type
     * 
     * @param int $userId
     * @param string $adType
     * @return array<AdView>
     */
    public static function getTodayViewsByType(int $userId, string $adType): array
    {
        $today = strtotime('today');
        $tomorrow = strtotime('tomorrow');
        
        $instance = new self();
        $sql = "SELECT * FROM {$instance->table} 
                WHERE user_id = ? AND ad_type = ? AND viewed_at >= ? AND viewed_at < ?
                ORDER BY viewed_at DESC";
        
        try {
            $stmt = $instance->getConnection()->prepare($sql);
            $stmt->execute([$userId, $adType, $today, $tomorrow]);
            $results = [];
            
            while ($data = $stmt->fetch()) {
                $model = new self($data);
                $model->exists = true;
                $results[] = $model;
            }
            
            return $results;
        } catch (\PDOException $e) {
            error_log("Error getting today's ad views: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Check for suspicious ad viewing patterns
     * 
     * @param int $userId
     * @return bool True if suspicious activity detected
     */
    public static function checkSuspiciousActivity(int $userId): bool
    {
        $lastHour = time() - 3600;
        $recentViews = self::getUserViewsInRange($userId, $lastHour, time());
        
        // Check for too many views in short time
        if (count($recentViews) > RATE_LIMIT_AD_VIEWS_PER_HOUR) {
            $user = User::findByTelegramId($userId);
            if ($user) {
                $user->incrementSuspiciousActivity('too_many_ad_views_per_hour');
            }
            return true;
        }
        
        // Check for duplicate tokens (if provided)
        $tokens = array_filter(array_map(fn($view) => $view->ad_token, $recentViews));
        if (count($tokens) !== count(array_unique($tokens))) {
            $user = User::findByTelegramId($userId);
            if ($user) {
                $user->incrementSuspiciousActivity('duplicate_ad_tokens');
            }
            return true;
        }
        
        return false;
    }

    /**
     * Get ad view statistics
     * 
     * @param int|null $userId Optional user filter
     * @param int|null $days Number of days to look back
     * @return array<string, mixed>
     */
    public static function getStats(?int $userId = null, ?int $days = 7): array
    {
        $startTime = time() - ($days * 24 * 3600);
        $instance = new self();
        
        $whereClause = "WHERE viewed_at >= ?";
        $params = [$startTime];
        
        if ($userId) {
            $whereClause .= " AND user_id = ?";
            $params[] = $userId;
        }
        
        $sql = "SELECT 
                    ad_type,
                    COUNT(*) as view_count,
                    SUM(reward_amount) as total_rewards,
                    COUNT(DISTINCT user_id) as unique_users
                FROM {$instance->table} 
                {$whereClause}
                GROUP BY ad_type";
        
        try {
            $stmt = $instance->getConnection()->prepare($sql);
            $stmt->execute($params);
            
            $stats = [];
            while ($row = $stmt->fetch()) {
                $stats[$row['ad_type']] = [
                    'views' => (int)$row['view_count'],
                    'rewards' => (int)$row['total_rewards'],
                    'unique_users' => (int)$row['unique_users']
                ];
            }
            
            return $stats;
        } catch (\PDOException $e) {
            error_log("Error getting ad view stats: " . $e->getMessage());
            return [];
        }
    }
}
