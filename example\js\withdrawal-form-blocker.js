/**
 * 🔒 МЕНЕДЖЕР БЛОКИРОВКИ ФОРМЫ ВЫПЛАТ
 * Управляет блокировкой/разблокировкой формы выплат на основе активных заявок
 */

class WithdrawalFormBlocker {
  constructor() {
    this.isBlocked = false;
    this.activeWithdrawals = [];
    this.blockReason = '';
    this.checkInterval = null;
    
    // Элементы формы для блокировки
    this.formElements = {
      form: document.querySelector('.withdrawal-form'),
      amountInput: document.getElementById('withdrawal-amount'),
      addressInput: document.getElementById('withdrawal-address'),
      currencySelect: document.getElementById('crypto-currency'),
      submitButton: document.getElementById('request-withdrawal-button'),
      errorElement: document.getElementById('withdrawal-error') || document.getElementById('action-status')
    };
    
    console.log('[WithdrawalFormBlocker] Инициализирован');
  }

  /**
   * 🚀 Запуск мониторинга активных выплат
   */
  async init() {
    console.log('[WithdrawalFormBlocker] Запуск мониторинга...');
    
    // Проверяем сразу при инициализации
    await this.checkActiveWithdrawals();
    
    // Затем проверяем каждые 30 секунд
    this.checkInterval = setInterval(() => {
      this.checkActiveWithdrawals();
    }, 30000);
    
    console.log('[WithdrawalFormBlocker] Мониторинг запущен (проверка каждые 30 секунд)');
  }

  /**
   * 🔍 Проверяет активные выплаты пользователя
   */
  async checkActiveWithdrawals() {
    try {
      const initData = window.Telegram?.WebApp?.initData;
      if (!initData) {
        console.warn('[WithdrawalFormBlocker] Нет initData, пропускаем проверку');
        return;
      }

      const response = await fetch(`${window.API_BASE_URL}/checkActiveWithdrawals.php`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ initData: initData })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.error || 'Ошибка проверки активных выплат');
      }

      // Обновляем состояние блокировки
      const wasBlocked = this.isBlocked;
      this.isBlocked = data.is_blocked;
      this.activeWithdrawals = data.active_withdrawals || [];
      this.blockReason = data.block_reason || '';

      // Если состояние изменилось, обновляем форму
      if (wasBlocked !== this.isBlocked) {
        if (this.isBlocked) {
          this.blockForm();
        } else {
          this.unblockForm();
        }
      }

      console.log(`[WithdrawalFormBlocker] Проверка завершена: блокировка=${this.isBlocked}, активных выплат=${this.activeWithdrawals.length}`);

    } catch (error) {
      console.error('[WithdrawalFormBlocker] Ошибка проверки активных выплат:', error);
    }
  }

  /**
   * 🔒 Блокирует форму выплат
   */
  blockForm() {
    console.log('[WithdrawalFormBlocker] Блокируем форму выплат');
    
    // Блокируем все элементы формы
    Object.values(this.formElements).forEach(element => {
      if (element) {
        element.disabled = true;
        element.style.opacity = '0.5';
        element.style.pointerEvents = 'none';
        element.style.cursor = 'not-allowed';
      }
    });

    // Добавляем класс блокировки к форме
    if (this.formElements.form) {
      this.formElements.form.classList.add('blocked');
      this.formElements.form.setAttribute('data-blocked', 'true');
    }

    // Показываем сообщение о блокировке
    this.showBlockMessage();
    
    // Уведомление пользователю
    if (window.appUtils) {
      window.appUtils.showBlockedFormNotification(this.blockReason, this.activeWithdrawals);
    }
  }

  /**
   * 🔓 Разблокирует форму выплат
   */
  unblockForm() {
    console.log('[WithdrawalFormBlocker] Разблокируем форму выплат');
    
    // Разблокируем все элементы формы
    Object.values(this.formElements).forEach(element => {
      if (element) {
        element.disabled = false;
        element.style.opacity = '1';
        element.style.pointerEvents = 'auto';
        element.style.cursor = 'auto';
      }
    });

    // Убираем класс блокировки
    if (this.formElements.form) {
      this.formElements.form.classList.remove('blocked');
      this.formElements.form.removeAttribute('data-blocked');
    }

    // Скрываем сообщение о блокировке
    this.hideBlockMessage();
    
    // Уведомление о разблокировке
    if (window.appUtils) {
      window.appUtils.showStatus('✅ Форма выплат разблокирована!', 'success', 2000);
    }
  }

  /**
   * 📢 Показывает сообщение о блокировке в форме
   */
  showBlockMessage() {
    if (!this.formElements.errorElement) return;

    let message = `🔒 <strong>Форма заблокирована</strong><br>${this.blockReason}`;
    
    if (this.activeWithdrawals.length > 0) {
      message += '<br><br><strong>Активные выплаты:</strong>';
      this.activeWithdrawals.forEach(withdrawal => {
        message += `<br>• ${withdrawal.coins_amount} монет → ${withdrawal.currency.toUpperCase()} (${withdrawal.status_text})`;
      });
    }

    this.formElements.errorElement.innerHTML = message;
    this.formElements.errorElement.className = 'withdrawal-error blocked-message';
    this.formElements.errorElement.style.display = 'block';
    this.formElements.errorElement.style.background = '#fff3cd';
    this.formElements.errorElement.style.color = '#856404';
    this.formElements.errorElement.style.border = '1px solid #ffeaa7';
    this.formElements.errorElement.style.borderRadius = '8px';
    this.formElements.errorElement.style.padding = '12px';
    this.formElements.errorElement.style.marginTop = '10px';
  }

  /**
   * 🚫 Скрывает сообщение о блокировке
   */
  hideBlockMessage() {
    if (!this.formElements.errorElement) return;

    this.formElements.errorElement.innerHTML = '';
    this.formElements.errorElement.style.display = 'none';
    this.formElements.errorElement.className = 'withdrawal-error';
  }

  /**
   * 🔍 Проверяет заблокирована ли форма (для внешнего использования)
   */
  isFormBlocked() {
    return this.isBlocked;
  }

  /**
   * 📊 Получает информацию о блокировке
   */
  getBlockInfo() {
    return {
      isBlocked: this.isBlocked,
      reason: this.blockReason,
      activeWithdrawals: this.activeWithdrawals,
      activeCount: this.activeWithdrawals.length
    };
  }

  /**
   * 🛑 Останавливает мониторинг
   */
  destroy() {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
    }
    console.log('[WithdrawalFormBlocker] Мониторинг остановлен');
  }

  /**
   * 🔄 Принудительная проверка (для внешнего вызова)
   */
  async forceCheck() {
    console.log('[WithdrawalFormBlocker] Принудительная проверка активных выплат');
    await this.checkActiveWithdrawals();
  }
}

// Создаем глобальный экземпляр
window.withdrawalFormBlocker = new WithdrawalFormBlocker();

// Автоматически запускаем при загрузке страницы
document.addEventListener('DOMContentLoaded', () => {
  if (window.withdrawalFormBlocker) {
    window.withdrawalFormBlocker.init();
  }
});

// Экспорт функций для обратной совместимости
window.checkWithdrawalFormBlock = () => window.withdrawalFormBlocker?.forceCheck();
window.isWithdrawalFormBlocked = () => window.withdrawalFormBlocker?.isFormBlocked() || false;
window.getWithdrawalBlockInfo = () => window.withdrawalFormBlocker?.getBlockInfo() || {};

console.log('🔒 [WithdrawalFormBlocker] Менеджер блокировки формы выплат загружен');
