<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Import Translations - UniQPaid</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .status { padding: 15px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .btn { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        .btn:hover { background: #0056b3; }
        .btn-danger { background: #dc3545; }
        .btn-danger:hover { background: #c82333; }
        .progress { width: 100%; height: 20px; background: #f0f0f0; border-radius: 10px; overflow: hidden; margin: 10px 0; }
        .progress-bar { height: 100%; background: #007bff; transition: width 0.3s; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌐 Translation Import Tool</h1>
            <p>Import translations into UniQPaid database</p>
        </div>

        <?php
        require_once __DIR__ . '/../app/config.php';
        require_once __DIR__ . '/../app/Database.php';
        require_once __DIR__ . '/../app/FileDatabase.php';
        require_once __DIR__ . '/../app/Models/Translation.php';

        use Models\Translation;

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $action = $_POST['action'] ?? '';
            
            if ($action === 'import') {
                echo '<div class="status info">Starting translation import...</div>';
                
                try {
                    // English translations
                    $enTranslations = [
                        'app.name' => 'UniQPaid',
                        'app.title' => 'UniQPaid - Crypto Wallet',
                        'app.loading' => 'Loading...',
                        'app.initializing' => 'Initializing application...',
                        'navigation.home' => 'Home',
                        'navigation.earnings' => 'Earnings',
                        'navigation.friends' => 'Friends',
                        'tasks.title' => 'Tasks',
                        'tasks.open_link' => 'Open Link',
                        'tasks.watch_video' => 'Watch Video',
                        'tasks.tap_banner' => 'Tap and Earn',
                        'buttons.open_link' => 'Open Link',
                        'buttons.watch_video' => 'Watch Video',
                        'buttons.tap_banner' => 'Tap and Earn',
                        'buttons.withdraw' => 'Withdraw',
                        'buttons.cancel' => 'Cancel',
                        'buttons.confirm' => 'Confirm',
                        'rewards.earned' => 'You earned {amount} coins!',
                        'rewards.daily_limit' => 'Daily limit reached',
                        'rewards.coins' => 'coins',
                        'balance.current' => 'Current Balance',
                        'balance.coins' => 'coins',
                        'withdrawal.title' => 'Withdraw Funds',
                        'withdrawal.minimum' => 'Minimum withdrawal: {amount} coins',
                        'referrals.title' => 'Invite Friends',
                        'referrals.your_code' => 'Your referral code',
                        'admin.title' => 'Admin Panel',
                        'admin.users' => 'Users',
                        'admin.withdrawals' => 'Withdrawals',
                        'errors.network' => 'Network error. Please try again.',
                        'errors.server' => 'Server error. Please try again later.',
                        'status.loading' => 'Loading',
                        'status.pending' => 'Pending',
                        'status.completed' => 'Completed',
                        'currencies.TON' => 'TON',
                        'currencies.USDT' => 'USDT (TRC20)',
                        'currencies.BTC' => 'Bitcoin'
                    ];

                    // Russian translations
                    $ruTranslations = [
                        'app.name' => 'UniQPaid',
                        'app.title' => 'UniQPaid - Криптокошелёк',
                        'app.loading' => 'Загрузка...',
                        'app.initializing' => 'Инициализация приложения...',
                        'navigation.home' => 'Главная',
                        'navigation.earnings' => 'Заработок',
                        'navigation.friends' => 'Друзья',
                        'tasks.title' => 'Задания',
                        'tasks.open_link' => 'Открыть ссылку',
                        'tasks.watch_video' => 'Смотреть видео',
                        'tasks.tap_banner' => 'Кликнуть по баннеру',
                        'buttons.open_link' => 'Открыть ссылку',
                        'buttons.watch_video' => 'Смотреть видео',
                        'buttons.tap_banner' => 'Кликнуть по баннеру',
                        'buttons.withdraw' => 'Вывести',
                        'buttons.cancel' => 'Отмена',
                        'buttons.confirm' => 'Подтвердить',
                        'rewards.earned' => 'Вы заработали {amount} монет!',
                        'rewards.daily_limit' => 'Дневной лимит достигнут',
                        'rewards.coins' => 'монет',
                        'balance.current' => 'Текущий баланс',
                        'balance.coins' => 'монет',
                        'withdrawal.title' => 'Вывод средств',
                        'withdrawal.minimum' => 'Минимальный вывод: {amount} монет',
                        'referrals.title' => 'Пригласить друзей',
                        'referrals.your_code' => 'Ваш реферальный код',
                        'admin.title' => 'Панель администратора',
                        'admin.users' => 'Пользователи',
                        'admin.withdrawals' => 'Выводы',
                        'errors.network' => 'Ошибка сети. Попробуйте ещё раз.',
                        'errors.server' => 'Ошибка сервера. Попробуйте позже.',
                        'status.loading' => 'Загрузка',
                        'status.pending' => 'Ожидание',
                        'status.completed' => 'Завершено',
                        'currencies.TON' => 'TON',
                        'currencies.USDT' => 'USDT (TRC20)',
                        'currencies.BTC' => 'Биткоин'
                    ];

                    echo '<div class="progress"><div class="progress-bar" style="width: 25%"></div></div>';
                    
                    // Import English
                    $enCount = Translation::importTranslations($enTranslations, 'en', 'general');
                    echo '<div class="status success">✅ Imported ' . $enCount . ' English translations</div>';
                    
                    echo '<div class="progress"><div class="progress-bar" style="width: 75%"></div></div>';
                    
                    // Import Russian
                    $ruCount = Translation::importTranslations($ruTranslations, 'ru', 'general');
                    echo '<div class="status success">✅ Imported ' . $ruCount . ' Russian translations</div>';
                    
                    echo '<div class="progress"><div class="progress-bar" style="width: 100%"></div></div>';
                    
                    $total = $enCount + $ruCount;
                    echo '<div class="status success"><strong>🎉 Import completed successfully!</strong><br>Total translations imported: ' . $total . '</div>';
                    
                } catch (Exception $e) {
                    echo '<div class="status error">❌ Import failed: ' . htmlspecialchars($e->getMessage()) . '</div>';
                }
            }
        }
        ?>

        <form method="POST">
            <button type="submit" name="action" value="import" class="btn">🚀 Import Translations</button>
        </form>

        <div style="margin-top: 30px;">
            <h3>📊 Current Status</h3>
            <?php
            try {
                $stats = Translation::getStats();
                echo '<pre>';
                echo 'Total translations: ' . ($stats['total'] ?? 0) . "\n";
                echo 'Languages: ' . implode(', ', array_keys($stats['by_language'] ?? [])) . "\n";
                echo 'Categories: ' . implode(', ', array_keys($stats['by_category'] ?? [])) . "\n";
                echo '</pre>';
            } catch (Exception $e) {
                echo '<div class="status error">Could not load stats: ' . htmlspecialchars($e->getMessage()) . '</div>';
            }
            ?>
        </div>

        <div style="margin-top: 20px; text-align: center;">
            <a href="../" class="btn">← Back to App</a>
        </div>
    </div>

    <script>
        // Auto-refresh progress bars
        document.querySelectorAll('.progress-bar').forEach(bar => {
            const width = bar.style.width;
            bar.style.width = '0%';
            setTimeout(() => {
                bar.style.width = width;
            }, 100);
        });
    </script>
</body>
</html>
