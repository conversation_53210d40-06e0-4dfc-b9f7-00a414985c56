Telegram
Instructions for Connection and Custom Triggering
Step 1: Checking tg script connection
Please ensure that your application's website is connected to Telegram using the following script:
html
<script src="https://telegram.org/js/telegram-web-app.js?56"></script>
According to the documentation:
https://core.telegram.org/bots/webapps#initializing-mini-apps

Step 2: Connecting the Library
Add a script to load the TelegramAdsController library into your HTML file. Make sure the specified path is correct:
html
<script src="https://richinfo.co/richpartners/telegram/js/tg-ob.js"></script>
This script loads the TelegramAdsController class, which will be used to manage advertising functionality.

Step 3: Initializing the Controller
javascript
КопироватьРедактировать
window.TelegramAdsController = new TelegramAdsController(); 

window.TelegramAdsController.initialize({  
    pubId: "12345",  
    appId: "54321",  
});
Parameters for initialize:
pubId (required): Unique publisher identifier.
appId (required): Application identifier.
debug (optional): Enables debug mode to display additional information in the console.

Step 4: Setting Up an Event Handler
Add a click event handler for a button (the trigger can be any element). When clicked, the triggerInterstitialBanner method will be called to display an ad:
javascript
const button = document.getElementById('ad-button');
button?.addEventListener('click', () => {
    window.TelegramAdsController.triggerInterstitialBanner().then((result) => {
        console.log(result);
    }).catch((result) => {
        console.log(result);
    });
});
For rewarded ads, the possible responses are:
resolve – Successful interaction with the ad
reject – The ad was not displayed or an error occurred
To display an offer immediately after a click (without showing a creative in between), pass true in the function call. By default, this parameter is false:
javascript
window.TelegramAdsController.triggerInterstitialBanner(true).then((result) => {
    console.log(result);
}).catch((result) => {
    console.log(result);
});

Step 5: Debug Mode
When debug: true is enabled, test ads will be returned for display.
To disable automatic ad display, contact your manager. The automatic ad display on click will be disabled in the uploaded settings.

Example Script Connection
html
КопироватьРедактировать
<script src="https://richinfo.co/richpartners/telegram/js/tg-ob.js"></script>
<script>
    window.TelegramAdsController = new TelegramAdsController();
    window.TelegramAdsController.initialize({
        pubId: "792361",
        appId: "1396",
    });
    window.TelegramAdsController.triggerInterstitialBanner().then((result) => {
        console.log(result);
    }).catch((result) => {
        console.log(result);
    });
</script>

