<?php
declare(strict_types=1);

namespace Security;

/**
 * Rate Limiter class for preventing abuse and spam
 * Uses file-based storage for simplicity (can be upgraded to Redis/Memcached)
 */
class RateLimiter
{
    /** @var string Directory for rate limit data */
    private string $dataDir;
    
    /** @var array<string, array<string, int>> Default rate limits */
    private array $defaultLimits = [
        'api' => ['requests' => 60, 'window' => 60], // 60 requests per minute
        'auth' => ['requests' => 5, 'window' => 300], // 5 auth attempts per 5 minutes
        'ad_view' => ['requests' => 100, 'window' => 3600], // 100 ad views per hour
        'withdrawal' => ['requests' => 3, 'window' => 3600], // 3 withdrawal requests per hour
        'referral' => ['requests' => 10, 'window' => 3600] // 10 referral actions per hour
    ];

    /**
     * Constructor
     * 
     * @param string|null $dataDir Custom data directory
     */
    public function __construct(?string $dataDir = null)
    {
        $this->dataDir = $dataDir ?? (APP_ROOT . '/database/rate_limits');
        
        if (!is_dir($this->dataDir)) {
            mkdir($this->dataDir, 0755, true);
        }
    }

    /**
     * Check if action is allowed for identifier
     * 
     * @param string $action Action type (api, auth, ad_view, etc.)
     * @param string $identifier User ID, IP address, etc.
     * @param array<string, int>|null $customLimits Custom limits [requests, window]
     * @return bool
     */
    public function isAllowed(string $action, string $identifier, ?array $customLimits = null): bool
    {
        $limits = $customLimits ?? $this->defaultLimits[$action] ?? $this->defaultLimits['api'];
        
        $key = $this->getKey($action, $identifier);
        $data = $this->getData($key);
        
        $now = time();
        $windowStart = $now - $limits['window'];
        
        // Clean old entries
        $data = array_filter($data, fn($timestamp) => $timestamp > $windowStart);
        
        // Check if limit exceeded
        if (count($data) >= $limits['requests']) {
            return false;
        }
        
        return true;
    }

    /**
     * Record an action
     * 
     * @param string $action Action type
     * @param string $identifier User ID, IP address, etc.
     * @return void
     */
    public function recordAction(string $action, string $identifier): void
    {
        $key = $this->getKey($action, $identifier);
        $data = $this->getData($key);
        
        $now = time();
        $data[] = $now;
        
        // Keep only recent entries (last 24 hours max)
        $dayAgo = $now - 86400;
        $data = array_filter($data, fn($timestamp) => $timestamp > $dayAgo);
        
        $this->saveData($key, $data);
    }

    /**
     * Check and record action in one call
     * 
     * @param string $action Action type
     * @param string $identifier User ID, IP address, etc.
     * @param array<string, int>|null $customLimits Custom limits
     * @return bool True if allowed and recorded, false if rate limited
     */
    public function attempt(string $action, string $identifier, ?array $customLimits = null): bool
    {
        if (!$this->isAllowed($action, $identifier, $customLimits)) {
            return false;
        }
        
        $this->recordAction($action, $identifier);
        return true;
    }

    /**
     * Get remaining attempts for action
     * 
     * @param string $action Action type
     * @param string $identifier User ID, IP address, etc.
     * @param array<string, int>|null $customLimits Custom limits
     * @return int
     */
    public function getRemainingAttempts(string $action, string $identifier, ?array $customLimits = null): int
    {
        $limits = $customLimits ?? $this->defaultLimits[$action] ?? $this->defaultLimits['api'];
        
        $key = $this->getKey($action, $identifier);
        $data = $this->getData($key);
        
        $now = time();
        $windowStart = $now - $limits['window'];
        
        // Count recent entries
        $recentCount = count(array_filter($data, fn($timestamp) => $timestamp > $windowStart));
        
        return max(0, $limits['requests'] - $recentCount);
    }

    /**
     * Get time until next attempt is allowed
     * 
     * @param string $action Action type
     * @param string $identifier User ID, IP address, etc.
     * @param array<string, int>|null $customLimits Custom limits
     * @return int Seconds until next attempt, 0 if allowed now
     */
    public function getTimeUntilReset(string $action, string $identifier, ?array $customLimits = null): int
    {
        if ($this->isAllowed($action, $identifier, $customLimits)) {
            return 0;
        }
        
        $limits = $customLimits ?? $this->defaultLimits[$action] ?? $this->defaultLimits['api'];
        
        $key = $this->getKey($action, $identifier);
        $data = $this->getData($key);
        
        if (empty($data)) {
            return 0;
        }
        
        // Find oldest entry in current window
        $now = time();
        $windowStart = $now - $limits['window'];
        $recentEntries = array_filter($data, fn($timestamp) => $timestamp > $windowStart);
        
        if (count($recentEntries) < $limits['requests']) {
            return 0;
        }
        
        $oldestEntry = min($recentEntries);
        return max(0, $oldestEntry + $limits['window'] - $now);
    }

    /**
     * Clear rate limit data for identifier
     * 
     * @param string $action Action type
     * @param string $identifier User ID, IP address, etc.
     * @return void
     */
    public function clearLimits(string $action, string $identifier): void
    {
        $key = $this->getKey($action, $identifier);
        $file = $this->getFilePath($key);
        
        if (file_exists($file)) {
            unlink($file);
        }
    }

    /**
     * Get rate limit statistics
     * 
     * @param string $action Action type
     * @param string $identifier User ID, IP address, etc.
     * @return array<string, mixed>
     */
    public function getStats(string $action, string $identifier): array
    {
        $limits = $this->defaultLimits[$action] ?? $this->defaultLimits['api'];
        
        return [
            'action' => $action,
            'identifier' => $identifier,
            'limit' => $limits['requests'],
            'window' => $limits['window'],
            'remaining' => $this->getRemainingAttempts($action, $identifier),
            'reset_time' => $this->getTimeUntilReset($action, $identifier),
            'is_allowed' => $this->isAllowed($action, $identifier)
        ];
    }

    /**
     * Generate key for action and identifier
     * 
     * @param string $action
     * @param string $identifier
     * @return string
     */
    private function getKey(string $action, string $identifier): string
    {
        return md5($action . ':' . $identifier);
    }

    /**
     * Get file path for key
     * 
     * @param string $key
     * @return string
     */
    private function getFilePath(string $key): string
    {
        return $this->dataDir . '/' . $key . '.json';
    }

    /**
     * Load data for key
     * 
     * @param string $key
     * @return array<int>
     */
    private function getData(string $key): array
    {
        $file = $this->getFilePath($key);
        
        if (!file_exists($file)) {
            return [];
        }
        
        $content = file_get_contents($file);
        if ($content === false) {
            return [];
        }
        
        $data = json_decode($content, true);
        return is_array($data) ? $data : [];
    }

    /**
     * Save data for key
     * 
     * @param string $key
     * @param array<int> $data
     * @return void
     */
    private function saveData(string $key, array $data): void
    {
        $file = $this->getFilePath($key);
        file_put_contents($file, json_encode($data));
    }
}
