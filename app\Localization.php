<?php
declare(strict_types=1);

/**
 * Localization class for handling multi-language support
 * Supports Russian and English based on user preference or IP detection
 */
class Localization
{
    /** @var string Current language */
    private static string $currentLanguage = 'en';
    
    /** @var array<string, array<string, string>> Loaded translations */
    private static array $translations = [];
    
    /** @var array<string> Supported languages */
    private static array $supportedLanguages = ['en', 'ru'];

    /**
     * Initialize localization system
     * 
     * @param string|null $language
     * @param string|null $ipAddress
     * @return void
     */
    public static function init(?string $language = null, ?string $ipAddress = null): void
    {
        // Determine language
        if ($language && in_array($language, self::$supportedLanguages)) {
            self::$currentLanguage = $language;
        } elseif ($ipAddress) {
            self::$currentLanguage = self::detectLanguageByIP($ipAddress);
        } else {
            self::$currentLanguage = 'en'; // Default
        }
        
        // Load translations
        self::loadTranslations();
    }

    /**
     * Get translated text
     * 
     * @param string $key Translation key (e.g., 'buttons.open_link')
     * @param array<string, mixed> $params Parameters for substitution
     * @return string
     */
    public static function get(string $key, array $params = []): string
    {
        $translation = self::$translations[self::$currentLanguage][$key] ?? 
                      self::$translations['en'][$key] ?? 
                      $key;
        
        // Replace parameters
        foreach ($params as $param => $value) {
            $translation = str_replace("{{$param}}", (string)$value, $translation);
        }
        
        return $translation;
    }

    /**
     * Get current language
     * 
     * @return string
     */
    public static function getCurrentLanguage(): string
    {
        return self::$currentLanguage;
    }

    /**
     * Set current language
     * 
     * @param string $language
     * @return void
     */
    public static function setLanguage(string $language): void
    {
        if (in_array($language, self::$supportedLanguages)) {
            self::$currentLanguage = $language;
            self::loadTranslations();
        }
    }

    /**
     * Detect language by IP address
     * 
     * @param string $ipAddress
     * @return string
     */
    private static function detectLanguageByIP(string $ipAddress): string
    {
        // Simple Russian IP detection (can be improved with GeoIP service)
        $russianRanges = [
            '5.', '37.', '46.', '62.', '77.', '78.', '79.', '80.', '81.', '82.', 
            '83.', '84.', '85.', '86.', '87.', '88.', '89.', '90.', '91.', '92.', 
            '93.', '94.', '95.', '176.', '178.', '185.', '188.', '194.', '195.'
        ];
        
        foreach ($russianRanges as $range) {
            if (str_starts_with($ipAddress, $range)) {
                return 'ru';
            }
        }
        
        return 'en';
    }

    /**
     * Load translations for current language
     * 
     * @return void
     */
    private static function loadTranslations(): void
    {
        $langFile = APP_ROOT . "/locales/{self::$currentLanguage}.json";
        
        if (file_exists($langFile)) {
            $content = file_get_contents($langFile);
            if ($content !== false) {
                $translations = json_decode($content, true);
                if (is_array($translations)) {
                    self::$translations[self::$currentLanguage] = $translations;
                }
            }
        }
        
        // Always load English as fallback
        if (self::$currentLanguage !== 'en') {
            $enFile = APP_ROOT . "/locales/en.json";
            if (file_exists($enFile)) {
                $content = file_get_contents($enFile);
                if ($content !== false) {
                    $translations = json_decode($content, true);
                    if (is_array($translations)) {
                        self::$translations['en'] = $translations;
                    }
                }
            }
        }
    }

    /**
     * Get all translations for current language
     * 
     * @return array<string, string>
     */
    public static function getAllTranslations(): array
    {
        return self::$translations[self::$currentLanguage] ?? [];
    }

    /**
     * Check if translation exists
     * 
     * @param string $key
     * @return bool
     */
    public static function has(string $key): bool
    {
        return isset(self::$translations[self::$currentLanguage][$key]) ||
               isset(self::$translations['en'][$key]);
    }

    /**
     * Get supported languages
     * 
     * @return array<string>
     */
    public static function getSupportedLanguages(): array
    {
        return self::$supportedLanguages;
    }

    /**
     * Format number according to current locale
     * 
     * @param int|float $number
     * @return string
     */
    public static function formatNumber(int|float $number): string
    {
        if (self::$currentLanguage === 'ru') {
            return number_format($number, 0, ',', ' ');
        }
        
        return number_format($number, 0, '.', ',');
    }

    /**
     * Format currency according to current locale
     * 
     * @param float $amount
     * @param string $currency
     * @return string
     */
    public static function formatCurrency(float $amount, string $currency = 'USD'): string
    {
        $formatted = number_format($amount, 2, '.', ',');
        
        if (self::$currentLanguage === 'ru') {
            return $formatted . ' ' . $currency;
        }
        
        return $currency . ' ' . $formatted;
    }
}
