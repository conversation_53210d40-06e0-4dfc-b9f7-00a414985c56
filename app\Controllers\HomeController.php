<?php
declare(strict_types=1);

require_once __DIR__ . '/Controller.php';
require_once __DIR__ . '/../Models/User.php';
require_once __DIR__ . '/../config.php';

use Controllers\Controller;

/**
 * HomeController handles the main page display and user data
 */
class HomeController extends Controller
{
    /**
     * Display the main home page
     * 
     * @return void
     */
    public function index(): void
    {
        try {
            // Get user data from Telegram WebApp or use default values
            $userData = $this->getUserData();
            
            // Pass data to the view
            $this->render('index', [
                'userData' => $userData,
                'pageTitle' => 'UniQPaid - Earn Crypto'
            ]);
            
        } catch (Exception $e) {
            // Log error and show fallback page
            error_log("HomeController error: " . $e->getMessage());
            
            // Show page with default data
            $this->render('index', [
                'userData' => [
                    'first_name' => 'User',
                    'balance' => 0,
                    'user_id' => 0
                ],
                'pageTitle' => 'UniQPaid - Earn Crypto'
            ]);
        }
    }
    
    /**
     * Get user data from Telegram WebApp or database
     * 
     * @return array User data array
     */
    private function getUserData(): array
    {
        // Default user data
        $defaultData = [
            'first_name' => 'User',
            'balance' => 0,
            'user_id' => 0,
            'username' => '',
            'language_code' => 'en'
        ];
        
        try {
            // Try to get data from Telegram WebApp initData
            if (isset($_GET['tgWebAppData']) || isset($_POST['initData'])) {
                // Parse Telegram data (simplified for now)
                $initData = $_GET['tgWebAppData'] ?? $_POST['initData'] ?? '';
                
                if (!empty($initData)) {
                    // Parse the init data
                    parse_str($initData, $params);
                    
                    if (isset($params['user'])) {
                        $user = json_decode($params['user'], true);
                        
                        if ($user) {
                            return [
                                'first_name' => $user['first_name'] ?? 'User',
                                'username' => $user['username'] ?? '',
                                'user_id' => $user['id'] ?? 0,
                                'language_code' => $user['language_code'] ?? 'en',
                                'balance' => $this->getUserBalance($user['id'] ?? 0)
                            ];
                        }
                    }
                }
            }
            
            // If no Telegram data, try to get from session or use demo data
            if (isset($_SESSION['user_id'])) {
                $userId = $_SESSION['user_id'];
                return [
                    'first_name' => $_SESSION['first_name'] ?? 'User',
                    'username' => $_SESSION['username'] ?? '',
                    'user_id' => $userId,
                    'language_code' => $_SESSION['language_code'] ?? 'en',
                    'balance' => $this->getUserBalance($userId)
                ];
            }
            
            // Return demo data for development
            return [
                'first_name' => 'Demo User',
                'username' => 'demo_user',
                'user_id' => 12345,
                'language_code' => 'en',
                'balance' => 488 // Demo balance like in the image
            ];
            
        } catch (Exception $e) {
            error_log("Error getting user data: " . $e->getMessage());
            return $defaultData;
        }
    }
    
    /**
     * Get user balance from database
     * 
     * @param int $userId User ID
     * @return int User balance
     */
    private function getUserBalance(int $userId): int
    {
        if ($userId <= 0) {
            return 0;
        }
        
        try {
            $userModel = new User();
            $user = $userModel->findById($userId);
            
            return $user['balance'] ?? 0;
            
        } catch (Exception $e) {
            error_log("Error getting user balance: " . $e->getMessage());
            return 0;
        }
    }
}
