/**
 * === navigation-oop.js ===
 * Класс для управления навигацией между страницами
 * Идеальная ООП система с анимациями и состоянием
 */

/**
 * Класс для управления навигацией приложения
 */
class NavigationManager {
  constructor() {
    this.currentPageElement = null;
    this.isTransitioning = false;
    this.isInitialized = false;
    this.pages = new Map();
    this.navButtons = new Map();
    this.transitionDuration = AppConfig.PAGE_TRANSITION_DURATION;
    this.navigationHistory = [];
    this.maxHistoryLength = 10;
  }

  /**
   * Инициализация навигации
   */
  init() {
    if (this.isInitialized) {
      AppUtils.warn('NavigationManager', 'Навигация уже инициализирована');
      return;
    }

    AppUtils.log('NavigationManager', 'Инициализация навигации...');
    
    // Инициализируем DOM Manager если не инициализирован
    if (!domManager.initialized) {
      domManager.init();
    }

    // Кешируем страницы и кнопки
    this.cacheElements();
    
    // Добавляем обработчики событий
    this.setupEventListeners();
    
    // Показываем главную страницу по умолчанию
    this.showMainContent();
    
    this.isInitialized = true;
    AppUtils.log('NavigationManager', 'Навигация инициализирована');
  }

  /**
   * Кеширование элементов страниц и кнопок навигации
   */
  cacheElements() {
    // Кешируем страницы (используем реальные ID из HTML)
    const pageIds = ['main-content', 'earn-section', 'friends-section'];
    pageIds.forEach(id => {
      const element = document.getElementById(id);
      if (element) {
        this.pages.set(id, element);
        AppUtils.log('NavigationManager', `✅ Страница "${id}" найдена`);
      } else {
        AppUtils.warn('NavigationManager', `❌ Страница "${id}" не найдена`);
      }
    });

    // Кешируем кнопки навигации (используем реальные ID из HTML)
    const buttonIds = ['nav-home', 'nav-earn', 'nav-friends'];
    buttonIds.forEach(buttonId => {
      const element = document.getElementById(buttonId);
      if (element) {
        this.navButtons.set(buttonId, element);
        AppUtils.log('NavigationManager', `✅ Кнопка "${buttonId}" найдена`);
      } else {
        AppUtils.warn('NavigationManager', `❌ Кнопка "${buttonId}" не найдена`);
      }
    });

    // Устанавливаем главную страницу как текущую
    this.currentPageElement = this.pages.get('main-content');
    this.currentPageId = 'main-content';

    AppUtils.log('NavigationManager', `Текущая страница: ${this.currentPageId}`);
  }

  /**
   * Настройка обработчиков событий
   */
  setupEventListeners() {
    // Обработчики для кнопок навигации
    const navHome = this.navButtons.get('nav-home');
    const navEarn = this.navButtons.get('nav-earn');
    const navFriends = this.navButtons.get('nav-friends');

    if (navHome) {
      navHome.addEventListener('click', () => this.showMainContent());
      AppUtils.log('NavigationManager', 'Обработчик для nav-home добавлен');
    }

    if (navEarn) {
      navEarn.addEventListener('click', () => this.showEarnSection());
      AppUtils.log('NavigationManager', 'Обработчик для nav-earn добавлен');
    }

    if (navFriends) {
      navFriends.addEventListener('click', () => this.showFriendsSection());
      AppUtils.log('NavigationManager', 'Обработчик для nav-friends добавлен');
    }

    // Обработчик для истории браузера (если нужно)
    window.addEventListener('popstate', (event) => {
      if (event.state && event.state.page) {
        this.navigateToPage(event.state.page, false);
      }
    });
  }

  /**
   * Переключение страниц с анимацией
   * @param {HTMLElement} nextPageElement - Элемент новой страницы
   * @param {HTMLElement} activeNavButton - Активная кнопка навигации
   * @param {string} pageId - ID страницы для истории
   */
  switchPageAnimated(nextPageElement, activeNavButton, pageId) {
    if (!nextPageElement || nextPageElement === this.currentPageElement || this.isTransitioning) {
      AppUtils.log('NavigationManager', 
        `Переключение отменено: next=${nextPageElement?.id}, current=${this.currentPageElement?.id}, transitioning=${this.isTransitioning}`
      );
      return;
    }
    
    AppUtils.log('NavigationManager', `Анимация: ${this.currentPageElement?.id} -> ${nextPageElement.id}`);
    
    // Устанавливаем флаг перехода
    this.isTransitioning = true;
    appState.set('isTransitioning', true);
    
    const pageOutElement = this.currentPageElement;

    // Добавляем в историю навигации
    this.addToHistory(pageId, nextPageElement.id);

    // Обновляем кнопку навигации
    this.updateActiveNavButton(activeNavButton);

    // Подготовка новой страницы
    this.preparePageForEntry(nextPageElement);

    // Анимация ухода старой страницы
    if (pageOutElement) {
      this.animatePageExit(pageOutElement);
    }

    // Запуск анимации входа новой страницы
    requestAnimationFrame(() => {
      this.animatePageEntry(nextPageElement);
    });

    // Обновляем текущую страницу
    this.currentPageElement = nextPageElement;

    // Завершение анимации
    setTimeout(() => {
      this.completePageTransition(pageOutElement);
    }, this.transitionDuration);
  }

  /**
   * Подготовка страницы для входа
   * @param {HTMLElement} pageElement - Элемент страницы
   */
  preparePageForEntry(pageElement) {
    pageElement.classList.remove("page-hidden");
    pageElement.style.display = "";
    pageElement.classList.remove("page-leave-active", "page-enter-active", "page-enter");
    pageElement.classList.add("page-enter");
  }

  /**
   * Анимация выхода страницы
   * @param {HTMLElement} pageElement - Элемент страницы
   */
  animatePageExit(pageElement) {
    pageElement.classList.remove("page-enter-active");
    pageElement.classList.add("page-leave-active");
  }

  /**
   * Анимация входа страницы
   * @param {HTMLElement} pageElement - Элемент страницы
   */
  animatePageEntry(pageElement) {
    pageElement.classList.remove("page-enter");
    pageElement.classList.add("page-enter-active");
  }

  /**
   * Завершение перехода между страницами
   * @param {HTMLElement} pageOutElement - Элемент старой страницы
   */
  completePageTransition(pageOutElement) {
    // Убираем классы анимации с новой страницы
    this.currentPageElement.classList.remove("page-enter-active");
    
    // Прячем старую страницу
    if (pageOutElement) {
      pageOutElement.classList.add("page-hidden");
      pageOutElement.classList.remove("page-leave-active");
      pageOutElement.style.display = "";
    }

    // Сбрасываем флаг перехода
    this.isTransitioning = false;
    appState.set('isTransitioning', false);
    
    AppUtils.log('NavigationManager', `Анимация завершена. Активна: ${this.currentPageElement.id}`);

    // Обрабатываем завершение перехода
    this.handlePageTransitionComplete();
  }

  /**
   * Обработка завершения перехода между страницами
   */
  handlePageTransitionComplete() {
    const pageId = this.currentPageElement.id;
    
    if (pageId === 'friends-page') {
      // Инициализация реферальной системы
      if (window.referralManager) {
        window.referralManager.init();
      } else {
        if (window.generateReferralLink) window.generateReferralLink();
        if (window.loadReferralStats) window.loadReferralStats();
      }
    }
    
    if (pageId === 'earn-page') {
      // Инициализация системы вывода
      if (window.withdrawalManager) {
        window.withdrawalManager.init();
      } else {
        if (window.updateWithdrawalSection) window.updateWithdrawalSection();
        if (window.updateCryptoAmountField) window.updateCryptoAmountField();
        if (window.validateWithdrawalForm) window.validateWithdrawalForm();
      }

      // Применяем данные калькулятора если есть
      this.syncCalculatorData();
      
      // Загружаем историю выплат
      if (window.loadAndDisplayWithdrawalHistory) {
        window.loadAndDisplayWithdrawalHistory();
      }
    }
  }

  /**
   * Синхронизация данных калькулятора с формой вывода
   */
  syncCalculatorData() {
    const calcData = calculatorData.get();
    if (calcData.amount > 0 && calcData.currency) {
      AppUtils.log('NavigationManager', `Синхронизация данных калькулятора: ${calcData.amount} монет, валюта ${calcData.currency}`);
      
      domManager.setValue('withdrawal-amount', calcData.amount);
      domManager.setValue('crypto-currency', calcData.currency);
      
      // Обновляем расчет
      if (window.updateCryptoAmountField) window.updateCryptoAmountField();
      if (window.updateAddressPlaceholder) window.updateAddressPlaceholder();
      if (window.validateWithdrawalForm) window.validateWithdrawalForm();
    }
  }

  /**
   * Обновление активной кнопки навигации
   * @param {HTMLElement} activeButton - Активная кнопка
   */
  updateActiveNavButton(activeButton) {
    // Убираем активный класс со всех кнопок
    this.navButtons.forEach(button => {
      if (button) button.classList.remove("active");
    });
    
    // Добавляем активный класс к выбранной кнопке
    if (activeButton) {
      activeButton.classList.add("active");
    }
  }

  /**
   * Добавить запись в историю навигации
   * @param {string} fromPage - Страница, с которой переходим
   * @param {string} toPage - Страница, на которую переходим
   */
  addToHistory(fromPage, toPage) {
    const historyEntry = {
      timestamp: new Date().toISOString(),
      fromPage,
      toPage
    };

    this.navigationHistory.push(historyEntry);

    // Ограничиваем размер истории
    if (this.navigationHistory.length > this.maxHistoryLength) {
      this.navigationHistory.shift();
    }
  }

  /**
   * Показать главную страницу
   */
  showMainContent() {
    this.showPage('main-content');
  }

  /**
   * Показать страницу заработка
   */
  showEarnSection() {
    this.showPage('earn-section');
  }

  /**
   * Показать страницу друзей
   */
  showFriendsSection() {
    this.showPage('friends-section');
  }

  /**
   * Простое переключение страниц
   * @param {string} pageId - ID страницы
   */
  showPage(pageId) {
    AppUtils.log('NavigationManager', `🔄 Переключение на: ${pageId}`);

    const targetPage = this.pages.get(pageId);
    if (!targetPage) {
      AppUtils.error('NavigationManager', `Страница "${pageId}" не найдена`);
      return;
    }

    // Скрываем все страницы
    this.pages.forEach((page, id) => {
      page.classList.remove('active-section');
      page.classList.add('page-hidden');
      AppUtils.log('NavigationManager', `Скрыта: ${id}`);
    });

    // Деактивируем все кнопки
    this.navButtons.forEach((button, id) => {
      button.classList.remove('active');
    });

    // Показываем целевую страницу
    targetPage.classList.remove('page-hidden');
    targetPage.classList.add('active-section');

    // Активируем соответствующую кнопку
    const buttonMapping = {
      'main-content': 'nav-home',
      'earn-section': 'nav-earn',
      'friends-section': 'nav-friends'
    };

    const buttonId = buttonMapping[pageId];
    const targetButton = this.navButtons.get(buttonId);
    if (targetButton) {
      targetButton.classList.add('active');
      AppUtils.log('NavigationManager', `Активирована кнопка: ${buttonId}`);
    }

    // Обновляем состояние
    this.currentPageElement = targetPage;
    this.currentPageId = pageId;

    AppUtils.log('NavigationManager', `✅ Переключение завершено: ${pageId}`);
  }

  /**
   * Получить текущую страницу
   * @returns {HTMLElement} Текущая страница
   */
  getCurrentPage() {
    return this.currentPageElement;
  }

  /**
   * Получить историю навигации
   * @param {number} limit - Максимальное количество записей
   * @returns {Array} История навигации
   */
  getHistory(limit = 5) {
    return this.navigationHistory.slice(-limit);
  }
}

// Создаем глобальный экземпляр
const navigationManager = new NavigationManager();

// Экспорт в глобальную область видимости
window.NavigationManager = NavigationManager;
window.navigationManager = navigationManager;

// Функции для обратной совместимости
window.initNavigation = () => navigationManager.init();
window.showMainContent = () => navigationManager.showMainContent();
window.showEarnSection = () => navigationManager.showEarnSection();
window.showFriendsSection = () => navigationManager.showFriendsSection();
window.switchPageAnimated = (page, button) => navigationManager.switchPageAnimated(page, button);
window.updateActiveNavButton = (button) => navigationManager.updateActiveNavButton(button);
window.getCurrentPage = () => navigationManager.getCurrentPage();

console.log('📦 [NavigationManager] Система навигации загружена');
