<?php
declare(strict_types=1);

namespace Security;

/**
 * TelegramAuth class for validating Telegram WebApp authentication data
 * Implements official Telegram WebApp authentication validation
 */
class TelegramAuth
{
    /** @var string Telegram bot token */
    private string $botToken;
    
    /** @var int Maximum age of auth data in seconds (5 minutes) */
    private int $maxAge = 300;

    /**
     * Constructor
     * 
     * @param string $botToken Telegram bot token
     */
    public function __construct(string $botToken)
    {
        $this->botToken = $botToken;
    }

    /**
     * Validate Telegram WebApp init data
     * 
     * @param string $initData Raw init data from Telegram WebApp
     * @return array<string, mixed>|null User data if valid, null if invalid
     */
    public function validateInitData(string $initData): ?array
    {
        try {
            // Parse init data
            parse_str($initData, $data);
            
            if (!isset($data['hash'])) {
                return null;
            }
            
            $hash = $data['hash'];
            unset($data['hash']);
            
            // Check auth_date
            if (!isset($data['auth_date'])) {
                return null;
            }
            
            $authDate = (int)$data['auth_date'];
            if (time() - $authDate > $this->maxAge) {
                return null; // Data too old
            }
            
            // Verify hash
            if (!$this->verifyHash($data, $hash)) {
                return null;
            }
            
            // Parse user data
            if (isset($data['user'])) {
                $userData = json_decode($data['user'], true);
                if ($userData) {
                    $userData['auth_date'] = $authDate;
                    return $userData;
                }
            }
            
            return null;
            
        } catch (Exception $e) {
            error_log("Telegram auth validation error: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Verify hash using Telegram's algorithm
     * 
     * @param array<string, string> $data
     * @param string $hash
     * @return bool
     */
    private function verifyHash(array $data, string $hash): bool
    {
        // Create data check string
        ksort($data);
        $dataCheckString = '';
        
        foreach ($data as $key => $value) {
            $dataCheckString .= $key . '=' . $value . "\n";
        }
        
        $dataCheckString = rtrim($dataCheckString, "\n");
        
        // Create secret key
        $secretKey = hash('sha256', $this->botToken, true);
        
        // Calculate hash
        $calculatedHash = hash_hmac('sha256', $dataCheckString, $secretKey);
        
        return hash_equals($hash, $calculatedHash);
    }

    /**
     * Generate session token for authenticated user
     * 
     * @param array<string, mixed> $userData
     * @return string
     */
    public function generateSessionToken(array $userData): string
    {
        $payload = [
            'user_id' => $userData['id'],
            'username' => $userData['username'] ?? '',
            'first_name' => $userData['first_name'] ?? '',
            'auth_date' => $userData['auth_date'] ?? time(),
            'expires' => time() + 86400 // 24 hours
        ];
        
        $token = base64_encode(json_encode($payload));
        $signature = hash_hmac('sha256', $token, $this->botToken);
        
        return $token . '.' . $signature;
    }

    /**
     * Validate session token
     * 
     * @param string $token
     * @return array<string, mixed>|null
     */
    public function validateSessionToken(string $token): ?array
    {
        try {
            $parts = explode('.', $token);
            if (count($parts) !== 2) {
                return null;
            }
            
            [$payload, $signature] = $parts;
            
            // Verify signature
            $expectedSignature = hash_hmac('sha256', $payload, $this->botToken);
            if (!hash_equals($signature, $expectedSignature)) {
                return null;
            }
            
            // Decode payload
            $data = json_decode(base64_decode($payload), true);
            if (!$data) {
                return null;
            }
            
            // Check expiration
            if (isset($data['expires']) && time() > $data['expires']) {
                return null; // Token expired
            }
            
            return $data;
            
        } catch (Exception $e) {
            error_log("Session token validation error: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Extract user data from request headers or parameters
     * 
     * @return array<string, mixed>|null
     */
    public function getUserFromRequest(): ?array
    {
        // Try to get from Authorization header
        $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
        if (str_starts_with($authHeader, 'Bearer ')) {
            $token = substr($authHeader, 7);
            return $this->validateSessionToken($token);
        }
        
        // Try to get from Telegram WebApp init data
        $initData = $_POST['_auth'] ?? $_GET['_auth'] ?? '';
        if ($initData) {
            return $this->validateInitData($initData);
        }
        
        // Try to get from session
        if (isset($_SESSION['telegram_user'])) {
            $userData = $_SESSION['telegram_user'];
            
            // Check if session is still valid
            if (isset($userData['auth_date']) && time() - $userData['auth_date'] < 86400) {
                return $userData;
            }
        }
        
        return null;
    }

    /**
     * Store user data in session
     * 
     * @param array<string, mixed> $userData
     * @return void
     */
    public function storeUserSession(array $userData): void
    {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        $_SESSION['telegram_user'] = $userData;
        $_SESSION['auth_time'] = time();
    }

    /**
     * Clear user session
     * 
     * @return void
     */
    public function clearUserSession(): void
    {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        unset($_SESSION['telegram_user']);
        unset($_SESSION['auth_time']);
    }

    /**
     * Check if user is authenticated
     * 
     * @return bool
     */
    public function isAuthenticated(): bool
    {
        return $this->getUserFromRequest() !== null;
    }

    /**
     * Get current authenticated user
     * 
     * @return array<string, mixed>|null
     */
    public function getCurrentUser(): ?array
    {
        return $this->getUserFromRequest();
    }

    /**
     * Require authentication (throw exception if not authenticated)
     * 
     * @return array<string, mixed>
     * @throws \Exception
     */
    public function requireAuth(): array
    {
        $user = $this->getCurrentUser();
        
        if (!$user) {
            throw new \Exception('Authentication required', 401);
        }
        
        return $user;
    }
}
