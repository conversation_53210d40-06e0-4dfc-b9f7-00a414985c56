<?php
declare(strict_types=1);

/**
 * app/autoload.php
 * Simple autoloader for the application classes
 * 
 * This autoloader follows PSR-4 standard and automatically loads classes
 * from the appropriate directories based on their namespace.
 */

spl_autoload_register(function ($className) {
    // Define the base directory for the namespace prefix
    $baseDir = __DIR__ . '/';
    
    // Define namespace mappings
    $namespaceMap = [
        'Models\\' => 'Models/',
        'Controllers\\' => 'Controllers/',
    ];
    
    // Check if the class has a namespace prefix we can handle
    foreach ($namespaceMap as $prefix => $dir) {
        if (strpos($className, $prefix) === 0) {
            // Remove the namespace prefix from the class name
            $relativeClass = substr($className, strlen($prefix));
            
            // Replace namespace separators with directory separators
            $relativeClass = str_replace('\\', '/', $relativeClass);
            
            // Build the full file path
            $file = $baseDir . $dir . $relativeClass . '.php';
            
            // If the file exists, require it
            if (file_exists($file)) {
                require_once $file;
                return;
            }
        }
    }
    
    // If no namespace prefix matched, try to load from the app directory directly
    $file = $baseDir . str_replace('\\', '/', $className) . '.php';
    if (file_exists($file)) {
        require_once $file;
        return;
    }
    
    // If still not found, try without any directory structure
    $file = $baseDir . $className . '.php';
    if (file_exists($file)) {
        require_once $file;
    }
});

// Load core classes that don't follow namespace conventions
$coreClasses = [
    'Database',
    'Router',
    'Security',
    'NOWPaymentsService',
    'TelegramValidator'
];

foreach ($coreClasses as $class) {
    $file = __DIR__ . '/' . $class . '.php';
    if (file_exists($file)) {
        require_once $file;
    }
}
