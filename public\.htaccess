# public/.htaccess
# Htaccess file for public directory
# Handles API routing and static file serving

RewriteEngine On

# Allow access to static files (CSS, JS, images) and PHP files
RewriteCond %{REQUEST_FILENAME} -f
RewriteRule \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|mp3|wav|php)$ - [L]

# Route all other requests to index.php
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [QSA,L]

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# Disable server signature
ServerSignature Off

# Prevent access to .htaccess files
<Files .htaccess>
    Order allow,deny
    Deny from all
</Files>
