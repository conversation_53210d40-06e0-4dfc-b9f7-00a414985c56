<?php
declare(strict_types=1);

/**
 * Router.php
 * Simple router class for handling HTTP requests and routing them to controllers
 * 
 * This router supports basic HTTP methods (GET, POST, PUT, DELETE) and
 * routes requests to the appropriate controller methods.
 */
class Router
{
    /** @var array<string, array<string, string>> Routes storage */
    private array $routes = [];

    /**
     * Add a route to the router
     * 
     * @param string $method HTTP method (GET, POST, PUT, DELETE)
     * @param string $path URL path pattern
     * @param string $handler Controller@method format
     */
    public function addRoute(string $method, string $path, string $handler): void
    {
        $this->routes[$method][$path] = $handler;
    }

    /**
     * Handle incoming HTTP request
     * 
     * @param string $method HTTP method
     * @param string $uri Request URI
     * @throws Exception If route not found or controller error
     */
    public function handleRequest(string $method, string $uri): void
    {
        // Remove query string from URI
        $path = parse_url($uri, PHP_URL_PATH) ?: '/';
        
        // Check if route exists
        if (!isset($this->routes[$method][$path])) {
            http_response_code(404);
            echo json_encode(['error' => 'Route not found']);
            return;
        }

        $handler = $this->routes[$method][$path];
        
        // Parse controller and method
        if (!str_contains($handler, '@')) {
            throw new Exception("Invalid handler format: {$handler}");
        }

        [$controllerName, $methodName] = explode('@', $handler, 2);

        // Instantiate controller
        $controllerClass = "Controllers\\{$controllerName}";
        
        if (!class_exists($controllerClass)) {
            throw new Exception("Controller not found: {$controllerClass}");
        }

        $controller = new $controllerClass();

        if (!method_exists($controller, $methodName)) {
            throw new Exception("Method not found: {$controllerClass}::{$methodName}");
        }

        // Call controller method
        $controller->$methodName();
    }

    /**
     * Get all registered routes
     * 
     * @return array<string, array<string, string>>
     */
    public function getRoutes(): array
    {
        return $this->routes;
    }
}
