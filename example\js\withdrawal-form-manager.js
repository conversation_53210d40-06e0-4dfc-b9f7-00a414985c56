// === withdrawal-form-manager.js ===
// Файл: js/withdrawal-form-manager.js
// Описание: Управляет логикой формы запроса на вывод.

class WithdrawalFormManager {
  constructor() {
    this.isInitialized = false;
    this.elements = {
      withdrawalAmountInput: document.getElementById("withdrawal-amount"),
      withdrawalAddressInput: document.getElementById("withdrawal-address"),
      cryptoCurrencySelect: document.getElementById("crypto-currency"),
      requestWithdrawalButton: document.getElementById(
        "request-withdrawal-button"
      ),
      withdrawalError: document.getElementById("withdrawal-error"),
      cryptoAmountField: document.getElementById("crypto-amount"),
    };

    // Переменные для валидации (из оригинала)
    this.minBalanceForWithdrawal = 100;
    this.minWithdrawalAmount = 0;

    // Экспорт элементов для обратной совместимости (из оригинала)
    window.withdrawalAmountInput = this.elements.withdrawalAmountInput;
    window.withdrawalAddressInput = this.elements.withdrawalAddressInput;
    window.cryptoCurrencySelect = this.elements.cryptoCurrencySelect;
    window.requestWithdrawalButton = this.elements.requestWithdrawalButton;
    window.cryptoAmountField = this.elements.cryptoAmountField;
  }

  init() {
    if (this.isInitialized) {
      console.log("[WithdrawalFormManager] Уже инициализирован.");
      return;
    }

    console.log("[WithdrawalFormManager] Инициализация формы вывода...");
    this.setupEventListeners();
    this.loadSettings();
    this.updateAddressPlaceholder();
    this.validateForm();

    this.isInitialized = true;
  }

  setupEventListeners() {
    console.log("[WithdrawalFormManager] Настройка обработчиков событий...");

    // Обработчик кнопки запроса вывода (из оригинала)
    if (this.elements.requestWithdrawalButton) {
      this.elements.requestWithdrawalButton.addEventListener("click", () =>
        this.handleRequestWithdrawal()
      );
    }

    // Обработчик изменения валюты (из оригинала)
    if (this.elements.cryptoCurrencySelect) {
      this.elements.cryptoCurrencySelect.addEventListener("change", () => {
        this.updateAddressPlaceholder();
        this.updateCryptoAmountField();
        this.validateForm();
      });
    }

    // Обработчики для валидации формы в реальном времени (из оригинала)
    if (this.elements.withdrawalAmountInput) {
      this.elements.withdrawalAmountInput.addEventListener(
        "input",
        async () => {
          await this.validateForm();
          this.updateCryptoAmountField();
        }
      );
    }

    if (this.elements.withdrawalAddressInput) {
      this.elements.withdrawalAddressInput.addEventListener(
        "input",
        async () => {
          await this.validateForm();
        }
      );
    }
  }

  /**
   * Загружает настройки из глобальной конфигурации (из оригинала)
   */
  loadSettings() {
    if (window.appSettings) {
      this.minBalanceForWithdrawal =
        window.appSettings.get("min_balance_for_withdrawal") || 100;
      this.minWithdrawalAmount =
        window.appSettings.get("min_withdrawal_amount") || 0;
    }
  }

  /**
   * Валидирует форму вывода средств и активирует/деактивирует кнопку (из оригинала)
   */
  async validateForm() {
    if (
      !this.elements.withdrawalAmountInput ||
      !this.elements.withdrawalAddressInput ||
      !this.elements.requestWithdrawalButton ||
      !this.elements.cryptoCurrencySelect
    ) {
      return false;
    }

    const amountStr = this.elements.withdrawalAmountInput.value.trim();
    const amount = parseInt(amountStr);
    const address = this.elements.withdrawalAddressInput.value.trim();
    const currency = this.elements.cryptoCurrencySelect.value;
    const currentUserBalance = window.balanceManager
      ? window.balanceManager.getBalance()
      : 0;

    let apiBlockerError = null;

    const validationState = {
      hasMinBalance: currentUserBalance >= this.minBalanceForWithdrawal,
      hasValidAmount:
        amountStr && amountStr.trim() !== "" && !isNaN(amount) && amount > 0,
      hasEnoughBalance: !isNaN(amount) && amount <= currentUserBalance,
      meetsMinimum:
        !this.minWithdrawalAmount ||
        (!isNaN(amount) && amount >= this.minWithdrawalAmount),
      hasAddress: address.length > 0,
      hasValidAddress: this.isValidCryptoAddress(address, currency),
      isApiCheckSuccessful: true,
    };

    if (
      validationState.hasValidAmount &&
      validationState.hasEnoughBalance &&
      amount > 0
    ) {
      try {
        const response = await fetch(
          `${window.API_BASE_URL}/calculateWithdrawalAmount.php`,
          {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              coins_amount: amount,
              currency: currency,
            }),
          }
        );

        if (response.ok) {
          const data = await response.json();
          if (!data.success) {
            const blockingErrorCodes = [
              "INSUFFICIENT_COINS",
              "AMOUNT_TOO_LOW_AFTER_FEE",
              "FEE_EXCEEDS_AMOUNT",
            ];
            if (blockingErrorCodes.includes(data.error_code)) {
              validationState.isApiCheckSuccessful = false;
              // НОВОЕ: Карта для сопоставления кодов ошибок с ключами локализации
              const errorCodeToLocKey = {
                INSUFFICIENT_COINS: "earnings.insufficient_balance",
                AMOUNT_TOO_LOW_AFTER_FEE: "earnings.increase_withdrawal_amount",
                FEE_EXCEEDS_AMOUNT: "earnings.fee_exceeds_amount",
              };
              const locKey =
                errorCodeToLocKey[data.error_code] || "earnings.unknown_error";
              // ИЗМЕНЕНО: Получаем локализованный текст ошибки
              apiBlockerError = window.appLocalization
                ? window.appLocalization.get(locKey)
                : data.error;
            }
          }
        } else {
          validationState.isApiCheckSuccessful = false;
          // ИЗМЕНЕНО: Используем ключ локализации
          apiBlockerError = window.appLocalization
            ? window.appLocalization.get("app.common.server_error")
            : "Server error. Please try again later.";
        }
      } catch (error) {
        console.warn(
          "[WithdrawalFormManager] Не удалось проверить через FeeCalculator:",
          error
        );
        validationState.isApiCheckSuccessful = false;
        // ИЗМЕНЕНО: Используем ключ локализации
        apiBlockerError = window.appLocalization
          ? window.appLocalization.get("app.common.network_error")
          : "Connection error. Please check your internet.";
      }
    }

    const isFormValid = Object.values(validationState).every(
      (state) => state === true
    );

    this.elements.requestWithdrawalButton.disabled = !isFormValid;

    if (isFormValid) {
      this.elements.requestWithdrawalButton.classList.remove("disabled");
      this.elements.requestWithdrawalButton.classList.add("enabled");
    } else {
      this.elements.requestWithdrawalButton.classList.add("disabled");
      this.elements.requestWithdrawalButton.classList.remove("enabled");
    }

    this.updateValidationMessages(
      validationState,
      amount,
      currentUserBalance,
      apiBlockerError
    );

    return isFormValid;
  }

  /**
   * Обновляет сообщения валидации (из оригинала)
   */
  updateValidationMessages(
    validationState,
    amount,
    currentUserBalance,
    apiBlockerError = null
  ) {
    let message = "";
    let messageType = "info";

    // ИЗМЕНЕНО: Логика осталась прежней, но теперь apiBlockerError приходит уже локализованным
    if (!validationState.isApiCheckSuccessful && apiBlockerError) {
      message = apiBlockerError;
      messageType = "error";
    } else if (!validationState.hasMinBalance) {
      message = window.appLocalization
        ? window.appLocalization.get("earnings.min_balance_required", {
            amount: this.minBalanceForWithdrawal,
          })
        : `Минимальный баланс для вывода ${this.minBalanceForWithdrawal} ${
            window.appLocalization
              ? window.appLocalization.get("currency.coins")
              : "монет"
          }`;
      messageType = "warning";
    } else if (!validationState.hasValidAmount) {
      message = window.appLocalization
        ? window.appLocalization.get("earnings.enter_correct_amount")
        : "Введите корректную сумму";
      messageType = "warning";
    } else if (!validationState.hasEnoughBalance) {
      const needed = amount - currentUserBalance;
      message = window.appLocalization
        ? window.appLocalization.get("earnings.insufficient_balance_need", {
            amount: needed,
          })
        : `Недостаточно средств, нужно еще ${needed} ${
            window.appLocalization
              ? window.appLocalization.get("currency.coins")
              : "монет"
          }`;
      messageType = "error";
    } else if (!validationState.meetsMinimum) {
      message = window.appLocalization
        ? window.appLocalization.get("earnings.min_withdrawal_amount", {
            amount: this.minWithdrawalAmount,
          })
        : `Минимальная сумма вывода ${this.minWithdrawalAmount} ${
            window.appLocalization
              ? window.appLocalization.get("currency.coins")
              : "монет"
          }`;
      messageType = "warning";
    } else if (!validationState.hasAddress) {
      message = window.appLocalization
        ? window.appLocalization.get("earnings.enter_wallet_address")
        : "Введите адрес кошелька";
      messageType = "warning";
    } else if (!validationState.hasValidAddress) {
      message = window.appLocalization
        ? window.appLocalization.get("earnings.invalid_wallet_address")
        : "Некорректный адрес кошелька";
      messageType = "error";
    } else {
      message = window.appLocalization
        ? window.appLocalization.get("earnings.form_valid")
        : "Форма заполнена корректно";
      messageType = "success";
    }

    this.showValidationMessage(message, messageType);
  }

  /**
   * Показывает сообщение валидации в плашке (из оригинала)
   */
  showValidationMessage(message, messageType) {
    let errorElement = this.elements.withdrawalError;

    if (!errorElement) {
      errorElement =
        document.getElementById("withdrawal-error") ||
        document.getElementById("action-status") ||
        document.getElementById("withdrawal-status") ||
        document.querySelector(".action-status-card") ||
        document.querySelector(".withdrawal-error");
    }

    if (!errorElement) {
      console.log(
        "[WithdrawalFormManager] Создаем элемент для отображения ошибок"
      );
      errorElement = document.createElement("p");
      errorElement.id = "withdrawal-error";
      errorElement.className = "hint error-message";

      const withdrawalButton = this.elements.requestWithdrawalButton;
      if (withdrawalButton && withdrawalButton.parentNode) {
        withdrawalButton.parentNode.insertBefore(
          errorElement,
          withdrawalButton.nextSibling
        );
        this.elements.withdrawalError = errorElement;
      }
    }

    if (errorElement) {
      errorElement.textContent = message;
      errorElement.style.display = "block";

      if (messageType === "success") {
        errorElement.className = "hint success-message";
        errorElement.style.color = "";
        errorElement.style.background = "";
        errorElement.style.border = "";
        console.log(
          "[WithdrawalFormManager] Применен класс success-message для зеленой плашки"
        );
      } else if (messageType === "error") {
        errorElement.className = "hint error-message";
        errorElement.style.color = "";
        errorElement.style.background = "";
        errorElement.style.border = "";
      } else if (messageType === "warning") {
        errorElement.className = "hint warning-message";
        errorElement.style.color = "";
        errorElement.style.background = "";
        errorElement.style.border = "";
      } else {
        errorElement.className = "hint";
        errorElement.style.color = "var(--cyber-text-secondary, #ccc)";
      }

      console.log(
        `[WithdrawalFormManager] ✅ Сообщение отображено в плашке: ${message}`
      );
    } else {
      console.warn(
        "[WithdrawalFormManager] ❌ Не удалось создать элемент для отображения ошибок"
      );
      if (window.appUtils) {
        window.appUtils.showStatus(message, messageType);
      }
    }
  }

  /**
   * Проверяет валидность адреса для конкретной криптовалюты (из оригинала)
   */
  isValidCryptoAddress(address, currency) {
    if (!address || address.length === 0) return false;

    const trimmedAddress = address.trim();

    switch (currency.toLowerCase()) {
      case "ton":
        return (
          /^[UEkuek]Q[A-Za-z0-9_-]{42,46}$/.test(trimmedAddress) ||
          /^[0-9a-fA-F]{64}$/.test(trimmedAddress)
        );
      case "eth":
        return /^0x[a-fA-F0-9]{40}$/.test(trimmedAddress);
      case "usdttrc20":
        return /^T[A-Za-z1-9]{33}$/.test(trimmedAddress);
      case "btc":
        return (
          /^[13][a-km-zA-HJ-NP-Z1-9]{25,34}$/.test(trimmedAddress) ||
          /^bc1[a-z0-9]{39,59}$/.test(trimmedAddress)
        );
      case "trx":
        return /^T[A-Za-z1-9]{33}$/.test(trimmedAddress);
      case "ltc":
        return (
          /^[LM][a-km-zA-HJ-NP-Z1-9]{26,33}$/.test(trimmedAddress) ||
          /^ltc1[a-z0-9]{39,59}$/.test(trimmedAddress)
        );
      case "bch":
        return (
          /^[qp][a-z0-9]{41}$/.test(trimmedAddress) ||
          /^[13][a-km-zA-HJ-NP-Z1-9]{25,34}$/.test(trimmedAddress)
        );
      case "xrp":
        return /^r[1-9A-HJ-NP-Za-km-z]{25,34}$/.test(trimmedAddress);
      case "ada":
        return (
          /^addr1[a-z0-9]{98}$/.test(trimmedAddress) ||
          /^[A-Za-z0-9]{59,104}$/.test(trimmedAddress)
        );
      case "dot":
        return /^1[a-km-zA-HJ-NP-Z1-9]{46,47}$/.test(trimmedAddress);
      default:
        return trimmedAddress.length >= 20;
    }
  }

  /**
   * Обработчик для кнопки "Запросить вывод" (из оригинала)
   */
  async handleRequestWithdrawal() {
    console.log("[WithdrawalFormManager] Начало обработки запроса на вывод");

    if (window.withdrawalFormBlocker?.isFormBlocked()) {
      const blockInfo = window.withdrawalFormBlocker.getBlockInfo();
      if (window.appUtils) {
        window.appUtils.showBlockedFormNotification(
          blockInfo.reason,
          blockInfo.activeWithdrawals
        );
      }
      console.warn(
        "[WithdrawalFormManager] Попытка отправки заблокированной формы",
        blockInfo
      );
      return;
    }

    const isFormValid = await this.validateForm();
    if (!isFormValid) {
      console.error(
        "[WithdrawalFormManager] Форма невалидна, отправка невозможна"
      );
      if (window.appUtils) {
        window.appUtils.showStatus(
          "Ошибка: форма заполнена некорректно",
          "error"
        );
      }
      return;
    }

    const initData = window.Telegram?.WebApp?.initData;
    if (!initData) {
      if (window.appUtils) {
        window.appUtils.showStatus("Ошибка: Нет данных Telegram.", "error");
      }
      return;
    }

    const amount = parseInt(this.elements.withdrawalAmountInput.value);
    const address = this.elements.withdrawalAddressInput.value.trim();
    const currency = this.elements.cryptoCurrencySelect.value;

    this.blockFormTemporarily();
    if (window.appUtils) {
      window.appUtils.showStatus("Расчет точной суммы...");
    }

    try {
      if (!address || !this.isValidCryptoAddress(address, currency)) {
        throw new Error("Пожалуйста, укажите корректный адрес кошелька");
      }

      console.log(
        "[WithdrawalFormManager] Получение точной суммы криптовалюты",
        { amount, currency, address }
      );
      const cryptoResult = await window.calculateCryptoAmount(
        amount,
        currency,
        address,
        true
      );
      console.log("[WithdrawalFormManager] Результат расчета:", cryptoResult);

      if (cryptoResult.status !== "success") {
        throw new Error(cryptoResult.message || "Ошибка расчета суммы");
      }

      const cryptoAmount = parseFloat(
        cryptoResult.raw_amount || cryptoResult.amount
      );

      if (window.appUtils) {
        window.appUtils.showStatus("Отправка запроса...");
      }

      console.log("[WithdrawalFormManager] Отправка запроса на сервер", {
        amount,
        address,
        currency,
        crypto_amount: cryptoAmount,
      });

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000);

      if (!currency) {
        throw new Error("Не указана криптовалюта для вывода");
      }

      const requestBody = {
        initData: initData,
        amount: amount,
        address: address,
        currency: currency.toLowerCase(),
        crypto_amount: cryptoAmount,
        crypto_address: address,
        wallet_address: address,
      };

      console.log("Request body:", JSON.stringify(requestBody, null, 2));

      const response = await fetch(
        `${window.API_BASE_URL}/requestWithdrawal.php`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
            "X-Requested-With": "XMLHttpRequest",
            Authorization: `Bearer ${window.Telegram?.WebApp?.initData || ""}`,
            "X-API-Key": window.API_KEY || "default_key",
          },
          credentials: "include",
          signal: controller.signal,
          body: JSON.stringify(requestBody),
        }
      );
      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorText = await response.text();
        console.error("Withdrawal API error:", {
          status: response.status,
          url: response.url,
          error: errorText,
        });

        if (response.status === 403) {
          throw new Error("Доступ запрещен. Проверьте авторизационные данные");
        }
        throw new Error(`Ошибка сервера: ${response.status} - ${errorText}`);
      }

      const data = await response.json();

      if (!data || typeof data !== "object") {
        throw new Error("Некорректный формат ответа сервера");
      }

      if (data.error_code) {
        switch (data.error_code) {
          case "FEE_CHANGED_RECALCULATE":
            const feeChangedMessage =
              data.error ||
              (window.appLocalization
                ? window.appLocalization.get("earnings.fee_changed_recalculate")
                : "Комиссия изменилась, пересчитайте сумму");
            this.showValidationMessage(`⚠️ ${feeChangedMessage}`, "warning");
            if (
              window.apiClient &&
              typeof window.apiClient.loadActualCurrencyData === "function"
            ) {
              window.apiClient
                .loadActualCurrencyData()
                .then(() => this.updateCryptoAmountField());
            }
            this.unblockFormTemporarily();
            return;
        }
      }

      if (data.error) {
        throw new Error(data.error);
      }

      if (window.balanceManager && data.newBalance !== undefined) {
        window.balanceManager.updateBalance(
          data.newBalance,
          "withdrawal_request"
        );
      }

      if (window.appUtils) {
        window.appUtils.showWithdrawalSuccessNotification({
          coins_amount: amount,
          currency: currency,
          status: data.withdrawal_data?.status || "В обработке",
        });
      }

      this.showValidationMessage(
        "✅ Запрос на вывод успешно отправлен!",
        "success"
      );

      if (window.withdrawalFormBlocker) {
        setTimeout(() => {
          window.withdrawalFormBlocker.forceCheck();
        }, 1000);
      }

      setTimeout(() => {
        this.clearForm();
      }, 2000);

      if (
        window.withdrawalManager &&
        window.withdrawalManager.loadAndDisplayHistory
      ) {
        setTimeout(() => {
          window.withdrawalManager.loadAndDisplayHistory();
        }, 1000);
      }
    } catch (error) {
      console.error("[WithdrawalFormManager] Ошибка запроса вывода:", error);
      if (window.appUtils) {
        window.appUtils.showStatus(`Ошибка: ${error.message}`, "error");
      }
      this.showValidationMessage(`❌ ${error.message}`, "error");
    } finally {
      this.unblockFormTemporarily();
    }
  }

  /**
   * 🔒 Временно блокирует форму во время отправки
   */
  blockFormTemporarily() {
    console.log("[WithdrawalFormManager] Временная блокировка формы");

    if (this.elements.requestWithdrawalButton) {
      this.elements.requestWithdrawalButton.disabled = true;
      this.elements.requestWithdrawalButton.style.opacity = "0.6";
      const sendingText = window.appLocalization
        ? window.appLocalization.get("common.sending") || "Sending..."
        : "Отправка...";
      this.elements.requestWithdrawalButton.textContent = sendingText;
    }

    [
      this.elements.withdrawalAmountInput,
      this.elements.withdrawalAddressInput,
      this.elements.cryptoCurrencySelect,
    ].forEach((element) => {
      if (element) {
        element.disabled = true;
        element.style.opacity = "0.6";
      }
    });
  }

  /**
   * 🔓 Разблокирует форму после отправки
   */
  unblockFormTemporarily() {
    console.log("[WithdrawalFormManager] Снятие временной блокировки формы");

    if (window.withdrawalFormBlocker?.isFormBlocked()) {
      console.log(
        "[WithdrawalFormManager] Форма остается заблокированной системно"
      );
      return;
    }

    if (this.elements.requestWithdrawalButton) {
      this.elements.requestWithdrawalButton.disabled = false;
      this.elements.requestWithdrawalButton.style.opacity = "1";
      const buttonText = window.appLocalization
        ? window.appLocalization.get("buttons.request_withdrawal")
        : "Запросить вывод";
      this.elements.requestWithdrawalButton.textContent = buttonText;
    }

    [
      this.elements.withdrawalAmountInput,
      this.elements.withdrawalAddressInput,
      this.elements.cryptoCurrencySelect,
    ].forEach((element) => {
      if (element) {
        element.disabled = false;
        element.style.opacity = "1";
      }
    });
  }

  /**
   * Обновляет поле суммы в криптовалюте с учетом баланса (из оригинала)
   */
  async updateCryptoAmountField() {
    if (!this.elements.cryptoAmountField) return;

    let withdrawalAmount =
      parseFloat(this.elements.withdrawalAmountInput?.value) || 0;

    if (withdrawalAmount === 0) {
      const calcAmountInput = document.getElementById("calc-amount");
      withdrawalAmount = parseFloat(calcAmountInput?.value) || 0;
    }

    if (withdrawalAmount <= 0) {
      this.elements.cryptoAmountField.value = "";
      this.elements.cryptoAmountField.style.color = "";
      this.elements.cryptoAmountField.title = "";
      return;
    }

    const selectedCurrency = this.elements.cryptoCurrencySelect?.value || "ton";

    try {
      const result = await window.calculateCryptoAmount(
        withdrawalAmount,
        selectedCurrency,
        false
      );

      const userBalance = window.balanceManager
        ? window.balanceManager.getBalance()
        : 0;
      if (withdrawalAmount > userBalance) {
        const needed = withdrawalAmount - userBalance;
        const coinsText = window.appLocalization
          ? window.appLocalization.get("currency.coins")
          : "монет";
        const insufficientText = window.appLocalization
          ? window.appLocalization.get("earnings.insufficient_funds")
          : "Недостаточно средств";
        const needText = window.appLocalization
          ? window.appLocalization.get("earnings.need")
          : "Нужно";
        this.elements.cryptoAmountField.value = `❌ ${insufficientText}! ${needText}: ${needed} ${coinsText}`;
        this.elements.cryptoAmountField.style.color =
          "var(--cyber-error-color, #ff6b6b)";
        this.elements.cryptoAmountField.title = `${insufficientText}! ${needText}: ${needed} ${coinsText}`;
        return;
      }

      const currencyName =
        this.elements.cryptoCurrencySelect?.options[
          this.elements.cryptoCurrencySelect.selectedIndex
        ]?.text ||
        window.currencyData?.[selectedCurrency]?.name ||
        "USDT";
      const shortName = currencyName.split(" ")[0];

      switch (result.status) {
        case "success":
          const finalCryptoAmount = parseFloat(
            result.raw_amount || result.amount
          );

          if (finalCryptoAmount > 0) {
            if (!window.currencyDataManager) {
              console.error(
                "[updateCryptoAmountField] CurrencyDataManager не найден!"
              );
              this.elements.cryptoAmountField.value = `❌ Ошибка системы`;
              this.elements.cryptoAmountField.style.color =
                "var(--cyber-error-color, #ff6b6b)";
              return;
            }

            const displayInfo = window.currencyDataManager.getDisplayInfo(
              selectedCurrency,
              withdrawalAmount
            );

            if (!displayInfo.success) {
              let color = "var(--cyber-error-color, #ff6b6b)";
              if (displayInfo.type === "insufficient_minimum") {
                color = "var(--cyber-warning-color, #ffa726)";
              }

              this.elements.cryptoAmountField.value = `⚠️ ${displayInfo.message}`;
              this.elements.cryptoAmountField.style.color = color;
              this.elements.cryptoAmountField.title = displayInfo.details;

              console.log(
                `[updateCryptoAmountField] ${selectedCurrency}: ${displayInfo.message} - ${displayInfo.details}`
              );
              return;
            }

            const formatResult = window.currencyDataManager.formatDisplayAmount(
              selectedCurrency,
              withdrawalAmount,
              finalCryptoAmount
            );

            this.elements.cryptoAmountField.value = formatResult.display;
            this.elements.cryptoAmountField.style.color =
              "var(--cyber-success-color, #00ff88)";
            this.elements.cryptoAmountField.title = formatResult.title;

            console.log(
              `[updateCryptoAmountField] ${selectedCurrency}: ${formatResult.display} [комиссия: $${displayInfo.networkFee}]`
            );
          } else {
            this.elements.cryptoAmountField.value = `❌ Ошибка расчёта`;
            this.elements.cryptoAmountField.style.color =
              "var(--cyber-error-color, #ff6b6b)";
            this.elements.cryptoAmountField.title = "Ошибка расчёта";
          }
          break;

        case "AMOUNT_TOO_LOW_AFTER_FEE":
          let lowAmountMessage = window.appLocalization
            ? window.appLocalization.get("earnings.increase_withdrawal_amount")
            : "Введите большую сумму для вывода.";
          this.elements.cryptoAmountField.value = `⚠️ ${lowAmountMessage}`;
          this.elements.cryptoAmountField.style.color =
            "var(--cyber-warning-color, #ffa726)";
          this.elements.cryptoAmountField.title = lowAmountMessage;
          break;

        case "INSUFFICIENT_COINS":
          this.elements.cryptoAmountField.value = `❌ ${
            result.message ||
            (window.appLocalization
              ? window.appLocalization.get("earnings.insufficient_balance")
              : "Недостаточно средств")
          }`;
          this.elements.cryptoAmountField.style.color =
            "var(--cyber-error-color, #ff6b6b)";
          this.elements.cryptoAmountField.title =
            result.message ||
            (window.appLocalization
              ? window.appLocalization.get("earnings.insufficient_balance")
              : "Недостаточно средств");
          break;

        case "insufficient_minimum":
          this.elements.cryptoAmountField.value = `⚠️ ${result.message}`;
          this.elements.cryptoAmountField.style.color =
            "var(--cyber-warning-color, #ffa726)";
          this.elements.cryptoAmountField.title = result.message;
          break;

        case "fee_exceeds_amount":
          this.elements.cryptoAmountField.value = `💸 ${result.message}`;
          this.elements.cryptoAmountField.style.color =
            "var(--cyber-error-color, #ff6b6b)";
          this.elements.cryptoAmountField.title = result.message;
          break;

        default:
          const defaultErrorText = window.appLocalization
            ? window.appLocalization.get("earnings.calculation_error")
            : "Ошибка расчета";
          this.elements.cryptoAmountField.value = `❌ ${
            result.message || defaultErrorText
          }`;
          this.elements.cryptoAmountField.style.color =
            "var(--cyber-error-color, #ff6b6b)";
          this.elements.cryptoAmountField.title =
            result.message || defaultErrorText;
      }
    } catch (error) {
      console.error(
        "[WithdrawalFormManager] Ошибка расчета криптовалюты:",
        error
      );
      const errorText = window.appLocalization
        ? window.appLocalization.get("earnings.calculation_error")
        : "Ошибка расчета";
      this.elements.cryptoAmountField.value = `❌ ${errorText}`;
      this.elements.cryptoAmountField.style.color =
        "var(--cyber-error-color, #ff6b6b)";
      this.elements.cryptoAmountField.title = `${errorText}: ${error.message}`;
    }
  }

  /**
   * Обновляет placeholder для поля адреса кошелька (из оригинала)
   */
  updateAddressPlaceholder() {
    if (
      !this.elements.cryptoCurrencySelect ||
      !this.elements.withdrawalAddressInput
    )
      return;

    const selectedCurrency = this.elements.cryptoCurrencySelect.value;

    const placeholder = window.appLocalization
      ? window.appLocalization.get("placeholders.enter_wallet_address")
      : "Введите адрес";

    this.elements.withdrawalAddressInput.placeholder = placeholder;

    this.validateForm();
  }

  /**
   * ИСПРАВЛЕНИЕ: Обработчик обновления данных валют
   */
  onCurrencyDataUpdate(newCurrencyData) {
    console.log(
      "[WithdrawalFormManager] Получены обновленные данные валют:",
      newCurrencyData
    );

    if (
      this.elements.withdrawalAmountInput &&
      this.elements.withdrawalAmountInput.value
    ) {
      this.updateCryptoAmountField();
    }
  }

  /**
   * Очищает форму после успешного запроса (из оригинала)
   */
  clearForm() {
    if (this.elements.withdrawalAmountInput) {
      this.elements.withdrawalAmountInput.value = "";
    }
    if (this.elements.withdrawalAddressInput) {
      this.elements.withdrawalAddressInput.value = "";
    }
    if (this.elements.cryptoAmountField) {
      this.elements.cryptoAmountField.value = "";
      this.elements.cryptoAmountField.style.color = "";
      this.elements.cryptoAmountField.title = "";
    }

    this.validateForm();
  }
}

window.withdrawalFormManager = new WithdrawalFormManager();

// Экспорт функций для обратной совместимости (из оригинала)
window.validateWithdrawalForm = () =>
  window.withdrawalFormManager.validateForm();
window.updateCryptoAmountField = () =>
  window.withdrawalFormManager.updateCryptoAmountField();
window.updateAddressPlaceholder = () =>
  window.withdrawalFormManager.updateAddressPlaceholder();
window.handleRequestWithdrawal = () =>
  window.withdrawalFormManager.handleRequestWithdrawal();

console.log(
  "📝 [WithdrawalFormManager] Менеджер формы вывода загружен с полной интеграцией."
);
