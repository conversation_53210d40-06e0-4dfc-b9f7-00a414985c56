<?php
declare(strict_types=1);

/**
 * Database.php
 * Database connection and management class using PDO with SQLite
 * 
 * This class provides a singleton pattern for database connections
 * and includes methods for database initialization and migrations.
 */
class Database
{
    private static mixed $connection = null;
    private static ?Database $instance = null;

    /**
     * Private constructor to prevent direct instantiation
     */
    private function __construct()
    {
        $this->connect();
    }

    /**
     * Get singleton instance of Database
     * 
     * @return Database
     */
    public static function getInstance(): Database
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Get connection instance
     *
     * @return PDO|FileDatabase|mixed
     */
    public static function getConnection(): mixed
    {
        if (self::$connection === null) {
            self::getInstance();
        }
        return self::$connection;
    }

    /**
     * Establish database connection
     *
     * @throws Exception If connection fails
     */
    private function connect(): void
    {
        try {
            // Check if PDO SQLite is available
            if (class_exists('PDO') && in_array('sqlite', PDO::getAvailableDrivers())) {
                $this->connectPDO();
            } else {
                // Fallback to file-based storage
                error_log('PDO SQLite not available, using file-based storage');
                $this->connectFileDatabase();
            }

        } catch (Exception $e) {
            throw new Exception('Database connection failed: ' . $e->getMessage());
        }
    }

    /**
     * Connect using PDO SQLite
     */
    private function connectPDO(): void
    {
        // Ensure database directory exists
        $dbDir = dirname(DB_PATH);
        if (!is_dir($dbDir)) {
            mkdir($dbDir, 0755, true);
        }

        // Create PDO connection
        $dsn = 'sqlite:' . DB_PATH;
        self::$connection = new PDO($dsn, null, null, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
        ]);

        // Enable foreign key constraints
        self::$connection->exec('PRAGMA foreign_keys = ON');

        // Initialize database schema if needed
        $this->initializeSimpleSchema();
    }

    /**
     * Connect using file-based database
     */
    private function connectFileDatabase(): void
    {
        // Initialize file database
        $fileDb = FileDatabase::getInstance();
        $fileDb->initializeSettings();

        // Create a mock PDO-like object for compatibility
        self::$connection = new class {
            public function prepare($sql) {
                return new class($sql) {
                    private $sql;
                    public function __construct($sql) { $this->sql = $sql; }
                    public function execute($params = []) { return true; }
                    public function fetch() { return false; }
                    public function fetchAll() { return []; }
                };
            }
            public function lastInsertId() { return '1'; }
            public function beginTransaction() { return true; }
            public function commit() { return true; }
            public function rollBack() { return true; }
            public function inTransaction() { return false; }
            public function exec($sql) { return true; }
            public function query($sql) {
                return new class {
                    public function fetch() { return ['count' => 6]; }
                    public function fetchAll() { return []; }
                };
            }
        };
    }

    /**
     * Initialize simple database schema
     * Creates basic tables for file database compatibility
     */
    private function initializeSimpleSchema(): void
    {
        // Create basic users table
        $userTable = "CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            telegram_id INTEGER UNIQUE NOT NULL,
            username TEXT,
            first_name TEXT,
            last_name TEXT,
            language TEXT DEFAULT 'ru',
            balance INTEGER DEFAULT 0,
            total_earned INTEGER DEFAULT 0,
            referrer_id INTEGER,
            referral_earnings INTEGER DEFAULT 0,
            registered_at INTEGER NOT NULL,
            last_activity INTEGER NOT NULL,
            suspicious_activity_count INTEGER DEFAULT 0,
            blocked BOOLEAN DEFAULT 0,
            blocked_at INTEGER,
            device_fingerprint TEXT,
            created_at INTEGER DEFAULT (strftime('%s', 'now')),
            updated_at INTEGER DEFAULT (strftime('%s', 'now'))
        )";

        self::$connection->exec($userTable);

        // Create settings table
        $settingsTable = "CREATE TABLE IF NOT EXISTS settings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            key TEXT UNIQUE NOT NULL,
            value TEXT NOT NULL,
            description TEXT,
            updated_at INTEGER DEFAULT (strftime('%s', 'now'))
        )";

        self::$connection->exec($settingsTable);

        // Insert default settings
        $this->insertDefaultSettings();
    }

    /**
     * Insert default application settings
     */
    private function insertDefaultSettings(): void
    {
        $defaultSettings = [
            ['conversion_rate', (string)CONVERSION_RATE, 'Conversion rate from coins to USD'],
            ['min_withdrawal', (string)MIN_WITHDRAWAL_AMOUNT, 'Minimum withdrawal amount in coins'],
            ['referral_bonus', (string)REFERRAL_BONUS_PERCENT, 'Referral bonus percentage'],
            ['ad_reward_banner', (string)AD_REWARD_NATIVE_BANNER, 'Reward for banner ads'],
            ['ad_reward_interstitial', (string)AD_REWARD_INTERSTITIAL, 'Reward for interstitial ads'],
            ['ad_reward_video', (string)AD_REWARD_REWARDED_VIDEO, 'Reward for video ads'],
            ['daily_limit_banner', (string)USER_AD_LIMIT_NATIVE_BANNER, 'Daily limit for banner ads'],
            ['daily_limit_interstitial', (string)USER_AD_LIMIT_INTERSTITIAL, 'Daily limit for interstitial ads'],
            ['daily_limit_video', (string)USER_AD_LIMIT_REWARDED_VIDEO, 'Daily limit for video ads'],
        ];

        $stmt = self::$connection->prepare(
            'INSERT OR IGNORE INTO settings (key, value, description) VALUES (?, ?, ?)'
        );

        foreach ($defaultSettings as $setting) {
            $stmt->execute($setting);
        }
    }

    /**
     * Execute a query and return the result
     * 
     * @param string $sql SQL query
     * @param array<mixed> $params Query parameters
     * @return PDOStatement
     */
    public function query(string $sql, array $params = []): PDOStatement
    {
        $stmt = self::$connection->prepare($sql);
        $stmt->execute($params);
        return $stmt;
    }

    /**
     * Get the last inserted ID
     * 
     * @return string
     */
    public function lastInsertId(): string
    {
        return self::$connection->lastInsertId();
    }

    /**
     * Begin a transaction
     */
    public function beginTransaction(): void
    {
        self::$connection->beginTransaction();
    }

    /**
     * Commit a transaction
     */
    public function commit(): void
    {
        self::$connection->commit();
    }

    /**
     * Rollback a transaction
     */
    public function rollback(): void
    {
        self::$connection->rollBack();
    }

    /**
     * Check if we're in a transaction
     * 
     * @return bool
     */
    public function inTransaction(): bool
    {
        return self::$connection->inTransaction();
    }
}
