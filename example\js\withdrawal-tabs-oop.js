// === withdrawal-tabs-oop.js ===
// Файл: js/withdrawal-tabs-oop.js
// Описание: Управляет вкладками "Калькулятор" и "Заявка на вывод".

class WithdrawalTabsManager {
  constructor() {
    this.elements = {
      calculatorTab: document.getElementById('calculator-tab'),
      withdrawalTab: document.getElementById('withdrawal-tab'),
      calculatorSection: document.getElementById('calculator-section'),
      withdrawalSection: document.getElementById('withdrawal-section'),
    };
    this.activeTab = null;
  }

  init() {
    console.log('[TabsManager] Инициализация.');
    if (Object.values(this.elements).some(el => !el)) {
        console.error('[TabsManager] ❌ Не найдены элементы табов.');
        return;
    }
    this.setupEventListeners();

    // Убеждаемся, что все разделы скрыты по умолчанию
    this.hideAllSections();
    this.deactivateAllTabs();
  }

  setupEventListeners() {
    this.elements.calculatorTab.addEventListener('click', () => this.switchToTab('calculator'));
    this.elements.withdrawalTab.addEventListener('click', () => this.switchToTab('withdrawal'));
  }

  switchToTab(tabName) {
    if (this.activeTab === tabName) return;
    this.activeTab = tabName;
    const isCalculatorActive = tabName === 'calculator';
    this.elements.calculatorTab.classList.toggle('active', isCalculatorActive);
    this.elements.withdrawalTab.classList.toggle('active', !isCalculatorActive);
    this.elements.calculatorSection.classList.toggle('hidden', !isCalculatorActive);
    this.elements.withdrawalSection.classList.toggle('hidden', isCalculatorActive);
    if (!isCalculatorActive) this.syncCalculatorToWithdrawalForm();
  }

  syncCalculatorToWithdrawalForm() {
    console.log('[TabsManager] Синхронизация калькулятора с формой вывода...');
    // Заглушка для синхронизации данных между калькулятором и формой
  }

  hideAllSections() {
    Object.values(this.elements).forEach(element => {
      if (element && element.classList.contains('calculator-section') || element.classList.contains('withdrawal-section')) {
        element.classList.add('hidden');
        element.classList.remove('active');
      }
    });
  }

  deactivateAllTabs() {
    if (this.elements.calculatorTab) {
      this.elements.calculatorTab.classList.remove('active');
      this.elements.calculatorTab.classList.add('inactive');
    }
    if (this.elements.withdrawalTab) {
      this.elements.withdrawalTab.classList.remove('active');
      this.elements.withdrawalTab.classList.add('inactive');
    }
  }
}

window.withdrawalTabsManager = new WithdrawalTabsManager();
console.log('📑 [TabsManager] Менеджер табов загружен.');