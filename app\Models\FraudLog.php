<?php
declare(strict_types=1);

namespace Models;

/**
 * FraudLog.php
 * Fraud log model for tracking security events and suspicious activities
 * 
 * This model handles logging of security events, fraud detection,
 * and analysis of suspicious user behavior patterns.
 */
class FraudLog extends Model
{
    /** @var string Table name */
    protected string $table = 'fraud_logs';
    
    /** @var array<string> Mass assignable fields */
    protected array $fillable = [
        'user_id',
        'event_type',
        'description',
        'ip_address',
        'user_agent',
        'session_id',
        'severity'
    ];

    /** @var array<string> Valid severity levels */
    public const SEVERITY_LEVELS = [
        'low',
        'medium',
        'high',
        'critical'
    ];

    /** @var array<string> Common event types */
    public const EVENT_TYPES = [
        'suspicious_activity',
        'rate_limit_exceeded',
        'invalid_token',
        'duplicate_request',
        'user_blocked',
        'multiple_devices',
        'vpn_detected',
        'bot_behavior',
        'unusual_pattern'
    ];

    /**
     * Create new fraud log entry
     * 
     * @param int|null $userId
     * @param string $eventType
     * @param string $description
     * @param string $severity
     * @param array<string, mixed> $metadata
     * @return FraudLog|null
     */
    public static function create(
        ?int $userId,
        string $eventType,
        string $description,
        string $severity = 'low',
        array $metadata = []
    ): ?FraudLog {
        // Validate severity
        if (!in_array($severity, self::SEVERITY_LEVELS)) {
            $severity = 'low';
        }

        $fraudLog = new self([
            'user_id' => $userId,
            'event_type' => $eventType,
            'description' => $description,
            'ip_address' => $metadata['ip_address'] ?? $_SERVER['REMOTE_ADDR'] ?? null,
            'user_agent' => $metadata['user_agent'] ?? $_SERVER['HTTP_USER_AGENT'] ?? null,
            'session_id' => $metadata['session_id'] ?? session_id(),
            'severity' => $severity
        ]);

        if ($fraudLog->save()) {
            error_log("Fraud log created: {$eventType} - {$description} (Severity: {$severity})");
            
            // Auto-escalate critical events
            if ($severity === 'critical' && $userId) {
                self::handleCriticalEvent($userId, $eventType, $description);
            }
            
            return $fraudLog;
        }

        return null;
    }

    /**
     * Handle critical security events
     * 
     * @param int $userId
     * @param string $eventType
     * @param string $description
     */
    private static function handleCriticalEvent(int $userId, string $eventType, string $description): void
    {
        $user = User::findByTelegramId($userId);
        if (!$user) {
            return;
        }

        // Auto-block user for critical events
        if (!$user->isAdmin()) {
            $user->block("Critical security event: {$eventType}");
            error_log("User {$userId} auto-blocked due to critical event: {$eventType}");
        }
    }

    /**
     * Get fraud logs for user
     * 
     * @param int $userId
     * @param int $limit
     * @return array<FraudLog>
     */
    public static function getUserLogs(int $userId, int $limit = 50): array
    {
        $instance = new self();
        $sql = "SELECT * FROM {$instance->table} 
                WHERE user_id = ? 
                ORDER BY created_at DESC 
                LIMIT ?";
        
        try {
            $stmt = $instance->getConnection()->prepare($sql);
            $stmt->execute([$userId, $limit]);
            $results = [];
            
            while ($data = $stmt->fetch()) {
                $model = new self($data);
                $model->exists = true;
                $results[] = $model;
            }
            
            return $results;
        } catch (\PDOException $e) {
            error_log("Error getting user fraud logs: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get recent fraud logs by severity
     * 
     * @param string $severity
     * @param int $hours Hours to look back
     * @param int $limit
     * @return array<FraudLog>
     */
    public static function getRecentBySeverity(string $severity, int $hours = 24, int $limit = 100): array
    {
        $startTime = time() - ($hours * 3600);
        $instance = new self();
        $sql = "SELECT * FROM {$instance->table} 
                WHERE severity = ? AND created_at >= ? 
                ORDER BY created_at DESC 
                LIMIT ?";
        
        try {
            $stmt = $instance->getConnection()->prepare($sql);
            $stmt->execute([$severity, $startTime, $limit]);
            $results = [];
            
            while ($data = $stmt->fetch()) {
                $model = new self($data);
                $model->exists = true;
                $results[] = $model;
            }
            
            return $results;
        } catch (\PDOException $e) {
            error_log("Error getting fraud logs by severity: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Analyze user behavior patterns
     * 
     * @param int $userId
     * @param int $days Days to analyze
     * @return array<string, mixed>
     */
    public static function analyzeUserBehavior(int $userId, int $days = 7): array
    {
        $startTime = time() - ($days * 24 * 3600);
        $logs = self::getUserLogs($userId, 1000);
        
        // Filter logs within time range
        $recentLogs = array_filter($logs, function($log) use ($startTime) {
            return $log->created_at >= $startTime;
        });

        $analysis = [
            'total_events' => count($recentLogs),
            'severity_breakdown' => [],
            'event_type_breakdown' => [],
            'risk_score' => 0,
            'recommendations' => []
        ];

        // Count by severity
        foreach (self::SEVERITY_LEVELS as $severity) {
            $count = count(array_filter($recentLogs, fn($log) => $log->severity === $severity));
            $analysis['severity_breakdown'][$severity] = $count;
        }

        // Count by event type
        foreach ($recentLogs as $log) {
            $type = $log->event_type;
            $analysis['event_type_breakdown'][$type] = ($analysis['event_type_breakdown'][$type] ?? 0) + 1;
        }

        // Calculate risk score (0-100)
        $riskScore = 0;
        $riskScore += $analysis['severity_breakdown']['low'] * 1;
        $riskScore += $analysis['severity_breakdown']['medium'] * 3;
        $riskScore += $analysis['severity_breakdown']['high'] * 7;
        $riskScore += $analysis['severity_breakdown']['critical'] * 15;
        
        $analysis['risk_score'] = min(100, $riskScore);

        // Generate recommendations
        if ($analysis['risk_score'] >= 50) {
            $analysis['recommendations'][] = 'Consider blocking user due to high risk score';
        }
        if ($analysis['severity_breakdown']['critical'] > 0) {
            $analysis['recommendations'][] = 'Immediate review required - critical events detected';
        }
        if ($analysis['total_events'] > 20) {
            $analysis['recommendations'][] = 'High activity volume - monitor closely';
        }

        return $analysis;
    }

    /**
     * Get fraud statistics
     * 
     * @param int $days Days to analyze
     * @return array<string, mixed>
     */
    public static function getStats(int $days = 30): array
    {
        $startTime = time() - ($days * 24 * 3600);
        $instance = new self();
        
        try {
            // Total events by severity
            $severityStmt = $instance->getConnection()->prepare(
                "SELECT severity, COUNT(*) as count 
                 FROM {$instance->table} 
                 WHERE created_at >= ? 
                 GROUP BY severity"
            );
            $severityStmt->execute([$startTime]);
            
            $severityStats = [];
            while ($row = $severityStmt->fetch()) {
                $severityStats[$row['severity']] = (int)$row['count'];
            }

            // Top event types
            $eventStmt = $instance->getConnection()->prepare(
                "SELECT event_type, COUNT(*) as count 
                 FROM {$instance->table} 
                 WHERE created_at >= ? 
                 GROUP BY event_type 
                 ORDER BY count DESC 
                 LIMIT 10"
            );
            $eventStmt->execute([$startTime]);
            
            $eventStats = [];
            while ($row = $eventStmt->fetch()) {
                $eventStats[$row['event_type']] = (int)$row['count'];
            }

            // Users with most events
            $userStmt = $instance->getConnection()->prepare(
                "SELECT user_id, COUNT(*) as count 
                 FROM {$instance->table} 
                 WHERE created_at >= ? AND user_id IS NOT NULL 
                 GROUP BY user_id 
                 ORDER BY count DESC 
                 LIMIT 10"
            );
            $userStmt->execute([$startTime]);
            
            $userStats = [];
            while ($row = $userStmt->fetch()) {
                $userStats[(int)$row['user_id']] = (int)$row['count'];
            }

            // Daily trend
            $trendStmt = $instance->getConnection()->prepare(
                "SELECT DATE(datetime(created_at, 'unixepoch')) as date, COUNT(*) as count 
                 FROM {$instance->table} 
                 WHERE created_at >= ? 
                 GROUP BY date 
                 ORDER BY date DESC"
            );
            $trendStmt->execute([$startTime]);
            
            $trendStats = [];
            while ($row = $trendStmt->fetch()) {
                $trendStats[$row['date']] = (int)$row['count'];
            }

            return [
                'total_events' => array_sum($severityStats),
                'severity_breakdown' => $severityStats,
                'top_event_types' => $eventStats,
                'top_users' => $userStats,
                'daily_trend' => $trendStats
            ];

        } catch (\PDOException $e) {
            error_log("Error getting fraud stats: " . $e->getMessage());
            return [
                'total_events' => 0,
                'severity_breakdown' => [],
                'top_event_types' => [],
                'top_users' => [],
                'daily_trend' => []
            ];
        }
    }

    /**
     * Clean old fraud logs
     * 
     * @param int $days Keep logs newer than this many days
     * @return int Number of deleted records
     */
    public static function cleanOldLogs(int $days = 90): int
    {
        $cutoffTime = time() - ($days * 24 * 3600);
        $instance = new self();
        
        try {
            $stmt = $instance->getConnection()->prepare(
                "DELETE FROM {$instance->table} WHERE created_at < ?"
            );
            $stmt->execute([$cutoffTime]);
            
            $deletedCount = $stmt->rowCount();
            error_log("Cleaned {$deletedCount} old fraud log entries");
            
            return $deletedCount;
        } catch (\PDOException $e) {
            error_log("Error cleaning old fraud logs: " . $e->getMessage());
            return 0;
        }
    }
}
