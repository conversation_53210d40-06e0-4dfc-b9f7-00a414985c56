/**
 * Плавно прокручивает родительский scroll-контейнер к элементу с указанным ID.
 * @param {string} elementId ID элемента, к которому нужно прокрутить.
 */
function smoothScrollTo(elementId) {
    const targetElement = document.getElementById(elementId);
    if (!targetElement) {
        console.warn(`[smoothScrollTo] Элемент с ID '${elementId}' не найден.`);
        return;
    }

    // Находим ближайший прокручиваемый родительский элемент (активную секцию).
    const scrollContainer = targetElement.closest('.app-section');

    if (!scrollContainer) {
        console.warn(`[smoothScrollTo] Не найден прокручиваемый контейнер для элемента '${elementId}'.`);
        return;
    }

    // Рассчитываем позицию для прокрутки внутри контейнера.
    const containerTop = scrollContainer.getBoundingClientRect().top;
    const targetTop = targetElement.getBoundingClientRect().top;
    const currentScrollTop = scrollContainer.scrollTop;
    const targetPosition = currentScrollTop + targetTop - containerTop;

    // Прокручиваем сам контейнер
    scrollContainer.scrollTo({
        top: targetPosition,
        behavior: 'smooth'
    });

    console.log(`[smoothScrollTo] Прокрутка контейнера к ${elementId} на позицию ${targetPosition}`);
}

console.log('✅ [smooth-scroll.js] Модуль плавной прокрутки загружен (v4 - container scroll).');