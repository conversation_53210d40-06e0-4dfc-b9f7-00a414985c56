<?php
declare(strict_types=1);

require_once __DIR__ . '/../app/config.php';
require_once __DIR__ . '/../app/Database.php';
require_once __DIR__ . '/../app/FileDatabase.php';
require_once __DIR__ . '/../app/Models/Translation.php';

use Models\Translation;

/**
 * Import translations into database
 * This script imports all translations from arrays into the database
 */

try {
    echo "Importing translations into database...\n";

    // English translations
    $enTranslations = [
        // App
        'app.name' => 'UniQPaid',
        'app.title' => 'UniQPaid - Crypto Wallet',
        'app.loading' => 'Loading...',
        'app.initializing' => 'Initializing application...',
        'app.error' => 'Error',
        'app.success' => 'Success',
        
        // Navigation
        'navigation.home' => 'Home',
        'navigation.earnings' => 'Earnings',
        'navigation.friends' => 'Friends',
        
        // Tasks
        'tasks.title' => 'Tasks',
        'tasks.open_link' => 'Open Link',
        'tasks.watch_video' => 'Watch Video',
        'tasks.tap_banner' => 'Tap and Earn',
        
        // Buttons
        'buttons.open_link' => 'Open Link',
        'buttons.watch_video' => 'Watch Video',
        'buttons.tap_banner' => 'Tap and Earn',
        'buttons.claim_reward' => 'Claim Reward',
        'buttons.invite_friends' => 'Invite Friends',
        'buttons.withdraw' => 'Withdraw',
        'buttons.cancel' => 'Cancel',
        'buttons.confirm' => 'Confirm',
        'buttons.close' => 'Close',
        'buttons.back' => 'Back',
        'buttons.save' => 'Save',
        'buttons.copy' => 'Copy',
        'buttons.share' => 'Share',
        
        // Rewards
        'rewards.earned' => 'You earned {amount} coins!',
        'rewards.daily_limit' => 'Daily limit reached',
        'rewards.no_ads' => 'No ads available',
        'rewards.try_again' => 'Try again later',
        'rewards.coins' => 'coins',
        
        // Balance
        'balance.current' => 'Current Balance',
        'balance.total_earned' => 'Total Earned',
        'balance.available' => 'Available for withdrawal',
        'balance.coins' => 'coins',
        
        // Withdrawal
        'withdrawal.title' => 'Withdraw Funds',
        'withdrawal.minimum' => 'Minimum withdrawal: {amount} coins',
        'withdrawal.select_currency' => 'Select cryptocurrency',
        'withdrawal.enter_address' => 'Enter wallet address',
        'withdrawal.amount' => 'Amount',
        'withdrawal.fee' => 'Network fee',
        'withdrawal.total' => 'Total to receive',
        'withdrawal.request' => 'Request Withdrawal',
        'withdrawal.success' => 'Withdrawal request submitted successfully!',
        
        // Referrals
        'referrals.title' => 'Invite Friends',
        'referrals.your_code' => 'Your referral code',
        'referrals.invite_link' => 'Invitation link',
        'referrals.friends_count' => 'Friends invited',
        'referrals.total_earned' => 'Earned from referrals',
        'referrals.bonus_percent' => 'You get {percent}% from friends\' earnings',
        'referrals.copy_link' => 'Copy invitation link',
        'referrals.share_link' => 'Share invitation link',
        'referrals.invite_text' => 'Join UniQPaid and earn crypto! Use my referral link:',
        
        // Admin
        'admin.title' => 'Admin Panel',
        'admin.users' => 'Users',
        'admin.withdrawals' => 'Withdrawals',
        'admin.statistics' => 'Statistics',
        'admin.settings' => 'Settings',
        'admin.total_users' => 'Total Users',
        'admin.active_users' => 'Active Users',
        'admin.approve' => 'Approve',
        'admin.reject' => 'Reject',
        'admin.block_user' => 'Block User',
        'admin.unblock_user' => 'Unblock User',
        
        // Errors
        'errors.network' => 'Network error. Please try again.',
        'errors.server' => 'Server error. Please try again later.',
        'errors.invalid_data' => 'Invalid data provided.',
        'errors.unauthorized' => 'Unauthorized access.',
        'errors.rate_limit' => 'Too many requests. Please wait.',
        'errors.blocked' => 'Your account has been blocked.',
        
        // Status
        'status.online' => 'Online',
        'status.offline' => 'Offline',
        'status.loading' => 'Loading',
        'status.pending' => 'Pending',
        'status.completed' => 'Completed',
        'status.failed' => 'Failed',
        'status.cancelled' => 'Cancelled',
        
        // Limits
        'limits.daily_ads' => 'Daily ad limit: {current}/{max}',
        'limits.views_left' => '{count} views left',
        'limits.limit_reached' => 'Daily limit reached',
        
        // Currencies
        'currencies.TON' => 'TON',
        'currencies.USDT' => 'USDT (TRC20)',
        'currencies.BTC' => 'Bitcoin',
        'currencies.ETH' => 'Ethereum'
    ];

    // Russian translations
    $ruTranslations = [
        // App
        'app.name' => 'UniQPaid',
        'app.title' => 'UniQPaid - Криптокошелёк',
        'app.loading' => 'Загрузка...',
        'app.initializing' => 'Инициализация приложения...',
        'app.error' => 'Ошибка',
        'app.success' => 'Успешно',
        
        // Navigation
        'navigation.home' => 'Главная',
        'navigation.earnings' => 'Заработок',
        'navigation.friends' => 'Друзья',
        
        // Tasks
        'tasks.title' => 'Задания',
        'tasks.open_link' => 'Открыть ссылку',
        'tasks.watch_video' => 'Смотреть видео',
        'tasks.tap_banner' => 'Кликнуть по баннеру',
        
        // Buttons
        'buttons.open_link' => 'Открыть ссылку',
        'buttons.watch_video' => 'Смотреть видео',
        'buttons.tap_banner' => 'Кликнуть по баннеру',
        'buttons.claim_reward' => 'Получить награду',
        'buttons.invite_friends' => 'Пригласить друзей',
        'buttons.withdraw' => 'Вывести',
        'buttons.cancel' => 'Отмена',
        'buttons.confirm' => 'Подтвердить',
        'buttons.close' => 'Закрыть',
        'buttons.back' => 'Назад',
        'buttons.save' => 'Сохранить',
        'buttons.copy' => 'Копировать',
        'buttons.share' => 'Поделиться',
        
        // Rewards
        'rewards.earned' => 'Вы заработали {amount} монет!',
        'rewards.daily_limit' => 'Дневной лимит достигнут',
        'rewards.no_ads' => 'Нет доступной рекламы',
        'rewards.try_again' => 'Попробуйте позже',
        'rewards.coins' => 'монет',
        
        // Balance
        'balance.current' => 'Текущий баланс',
        'balance.total_earned' => 'Всего заработано',
        'balance.available' => 'Доступно для вывода',
        'balance.coins' => 'монет',
        
        // Withdrawal
        'withdrawal.title' => 'Вывод средств',
        'withdrawal.minimum' => 'Минимальный вывод: {amount} монет',
        'withdrawal.select_currency' => 'Выберите криптовалюту',
        'withdrawal.enter_address' => 'Введите адрес кошелька',
        'withdrawal.amount' => 'Сумма',
        'withdrawal.fee' => 'Комиссия сети',
        'withdrawal.total' => 'К получению',
        'withdrawal.request' => 'Запросить вывод',
        'withdrawal.success' => 'Запрос на вывод успешно отправлен!',
        
        // Referrals
        'referrals.title' => 'Пригласить друзей',
        'referrals.your_code' => 'Ваш реферальный код',
        'referrals.invite_link' => 'Ссылка-приглашение',
        'referrals.friends_count' => 'Приглашено друзей',
        'referrals.total_earned' => 'Заработано с рефералов',
        'referrals.bonus_percent' => 'Вы получаете {percent}% с заработка друзей',
        'referrals.copy_link' => 'Копировать ссылку-приглашение',
        'referrals.share_link' => 'Поделиться ссылкой-приглашением',
        'referrals.invite_text' => 'Присоединяйтесь к UniQPaid и зарабатывайте криптовалюту! Используйте мою реферальную ссылку:',
        
        // Admin
        'admin.title' => 'Панель администратора',
        'admin.users' => 'Пользователи',
        'admin.withdrawals' => 'Выводы',
        'admin.statistics' => 'Статистика',
        'admin.settings' => 'Настройки',
        'admin.total_users' => 'Всего пользователей',
        'admin.active_users' => 'Активных пользователей',
        'admin.approve' => 'Одобрить',
        'admin.reject' => 'Отклонить',
        'admin.block_user' => 'Заблокировать пользователя',
        'admin.unblock_user' => 'Разблокировать пользователя',
        
        // Errors
        'errors.network' => 'Ошибка сети. Попробуйте ещё раз.',
        'errors.server' => 'Ошибка сервера. Попробуйте позже.',
        'errors.invalid_data' => 'Предоставлены неверные данные.',
        'errors.unauthorized' => 'Неавторизованный доступ.',
        'errors.rate_limit' => 'Слишком много запросов. Подождите.',
        'errors.blocked' => 'Ваш аккаунт заблокирован.',
        
        // Status
        'status.online' => 'Онлайн',
        'status.offline' => 'Оффлайн',
        'status.loading' => 'Загрузка',
        'status.pending' => 'Ожидание',
        'status.completed' => 'Завершено',
        'status.failed' => 'Неудачно',
        'status.cancelled' => 'Отменено',
        
        // Limits
        'limits.daily_ads' => 'Дневной лимит рекламы: {current}/{max}',
        'limits.views_left' => 'Осталось просмотров: {count}',
        'limits.limit_reached' => 'Дневной лимит достигнут',
        
        // Currencies
        'currencies.TON' => 'TON',
        'currencies.USDT' => 'USDT (TRC20)',
        'currencies.BTC' => 'Биткоин',
        'currencies.ETH' => 'Эфириум'
    ];

    // Import English translations
    $enCount = Translation::importTranslations($enTranslations, 'en', 'general');
    echo "Imported {$enCount} English translations\n";

    // Import Russian translations
    $ruCount = Translation::importTranslations($ruTranslations, 'ru', 'general');
    echo "Imported {$ruCount} Russian translations\n";

    echo "✅ Translation import completed successfully!\n";
    echo "Total translations: " . ($enCount + $ruCount) . "\n";

} catch (Exception $e) {
    echo "❌ Translation import failed: " . $e->getMessage() . "\n";
    exit(1);
}
