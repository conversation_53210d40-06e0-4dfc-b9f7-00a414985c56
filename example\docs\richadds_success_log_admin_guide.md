# Руководство по блоку "Информация об устройстве в момент success"

## Обзор

В админке в разделе "Безопасность" → "Антифрод система" добавлен блок "Информация об устройстве в момент success", который отображает данные из файла `richadds_success_log.csv` и позволяет экспортировать их в CSV формате.

## Функциональность

### 1. Отображение данных
- **Источник данных**: `database/richadds_success_log.csv`
- **Пагинация**: 20 записей на страницу
- **Автоматическая загрузка**: Данные загружаются при открытии вкладки

### 2. Фильтрация данных
- **По дате**: Фильтрация по диапазону дат (от и до)
- **По поиску**: Поиск по Telegram User ID или IP адресу
- **Применение фильтров**: Кнопка "Применить" или клавиша Enter

### 3. Экспорт данных
- **Формат**: CSV файл
- **Логика экспорта**:
  - **Без фильтров**: Экспортируются ВСЕ записи из базы данных
  - **С фильтрами**: Экспортируются только отфильтрованные данные
- **Имена файлов**:
  - Все записи: `richadds_success_log_all_[количество]_records_YYYY-MM-DD_HH-mm-ss.csv`
  - Отфильтрованные: `richadds_success_log_filtered_[найдено]_of_[всего]_YYYY-MM-DD_HH-mm-ss.csv`
- **Подтверждение**: Перед экспортом показывается диалог с информацией о том, что будет экспортировано
- **Автоматическое скачивание**: Файл автоматически скачивается в браузере

## Структура данных

### Отображаемые колонки
1. **Telegram User ID** - ID пользователя в Telegram
2. **IP Address** - IP адрес пользователя
3. **Device Type** - Тип устройства
4. **Platform** - Платформа/операционная система (отформатированная)
5. **Screen Resolution** - Разрешение экрана
6. **Time** - Время в формате HH:MM:SS (UTC)

### Исходная структура CSV файла
```csv
"Telegram User ID","IP Address","Device Type","Platform","Screen Resolution","Device Manufacturer","Time"
```

### Экспортируемая структура
```csv
"Telegram User ID","IP Address","Device Type","Platform","Screen Resolution","Time"
```

**Примечание**: Колонка "Device Manufacturer" исключена из отображения и экспорта согласно требованиям.

## API Endpoints

### 1. Получение данных
- **URL**: `/api/admin/get_richadds_log.php`
- **Метод**: GET
- **Параметры**:
  - `page` - номер страницы (по умолчанию 1)
  - `search` - поисковый запрос
  - `date_from` - дата начала фильтрации (YYYY-MM-DD)
  - `date_to` - дата окончания фильтрации (YYYY-MM-DD)

**Пример запроса**:
```
GET /api/admin/get_richadds_log.php?page=1&search=123456&date_from=2025-07-01&date_to=2025-07-08
```

**Ответ**:
```json
{
  "success": true,
  "data": [
    ["7971051670", "***************", "Linux armv7l", "Android (ARM 32-bit)", "360x800", "Google Inc.", "12:21:43"]
  ],
  "pagination": {
    "page": 1,
    "total": 73,
    "totalPages": 4
  }
}
```

### 2. Экспорт данных
- **URL**: `/api/admin/export_richadds_log.php`
- **Метод**: GET
- **Параметры**: те же, что и для получения данных
- **Авторизация**: Требуется сессия администратора

**Пример запроса**:
```
GET /api/admin/export_richadds_log.php?search=123456&date_from=2025-07-01&date_to=2025-07-08
```

## JavaScript функции

### 1. loadRichaddsLog(page)
Загружает данные с сервера с учетом текущих фильтров.

### 2. applyRichaddsFilter()
Применяет фильтры и загружает первую страницу результатов.

### 3. exportRichaddsData()
Инициирует экспорт отфильтрованных данных в CSV файл.

### 4. updateRichaddsLogTable(data)
Обновляет таблицу с полученными данными.

### 5. updateRichaddsLogPagination(pagination)
Обновляет элементы пагинации.

## Особенности реализации

### 1. Парсинг времени
- Время в CSV файле хранится в формате HH:MM:SS
- Для фильтрации по датам используется текущая дата + время из файла
- Функция `parseTimeFromCsv()` обрабатывает преобразование

### 2. Безопасность
- Экспорт доступен только авторизованным администраторам
- Проверка сессии в `export_richadds_log.php`

### 3. Обработка ошибок
- Проверка существования файла лога
- Валидация структуры данных CSV
- Обработка некорректных записей

## Использование

1. Откройте админку → Безопасность → Антифрод система
2. Прокрутите до блока "Информация об устройстве в момент success"
3. Используйте фильтры для поиска нужных данных:
   - Выберите диапазон дат
   - Введите поисковый запрос
   - Нажмите "Применить"
4. Для экспорта данных нажмите "Экспорт данных в CSV"
5. Файл автоматически скачается в браузере

## Файлы проекта

- `api/admin/security.php` - основная страница админки с интерфейсом
- `api/admin/get_richadds_log.php` - API для получения данных
- `api/admin/export_richadds_log.php` - API для экспорта данных
- `database/richadds_success_log.csv` - файл с данными логов
- `api/log_richadds_success.php` - скрипт для записи логов (создает CSV файл)
