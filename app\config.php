<?php
declare(strict_types=1);

/**
 * app/config.php
 * Application configuration file
 *
 * This file contains all configuration constants and settings for the application.
 * All sensitive data should be moved to environment variables in production.
 */

// Application settings
define('DEBUG_MODE', true); // Set to false in production
define('APP_NAME', 'UniQPaid');
define('APP_VERSION', '2.0.0');

// Database configuration
define('DB_PATH', APP_ROOT . '/database/app.sqlite');

// Telegram Bot configuration
define('BOT_TOKEN', '7479775119:AAGJKLMNOPQRSTUVWXYZabcdefghijklmnop'); // Replace with your bot token
define('BOT_USERNAME', 'UniQPaidBot'); // Replace with your bot username

// Admin user IDs (these users bypass all security restrictions)
define('ADMIN_USER_IDS', [5880288830]); // Add your admin Telegram IDs here

// Ad rewards configuration
define('AD_REWARD_NATIVE_BANNER', 10);    // Coins for banner ads
define('AD_REWARD_INTERSTITIAL', 8);      // Coins for interstitial ads
define('AD_REWARD_REWARDED_VIDEO', 2);    // Coins for video ads

// Daily ad limits per user
define('USER_AD_LIMIT_NATIVE_BANNER', 10);
define('USER_AD_LIMIT_INTERSTITIAL', 10);
define('USER_AD_LIMIT_REWARDED_VIDEO', 20);

// Referral system
define('REFERRAL_BONUS_PERCENT', 0.1); // 10% bonus for referrer

// Withdrawal settings
define('MIN_WITHDRAWAL_AMOUNT', 100);        // Minimum coins to withdraw
define('MIN_BALANCE_FOR_WITHDRAWAL', 1);     // Minimum balance to access withdrawal
define('CONVERSION_RATE', 0.001);            // 1 coin = 0.001 USD
define('SHOW_FEES_TO_USER', true);

// NOWPayments API configuration
define('NOWPAYMENTS_API_KEY', '18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7');
define('NOWPAYMENTS_PUBLIC_KEY', 'f6627c2b-98ac-4d30-90dc-c01324330248');
define('NOWPAYMENTS_IPN_SECRET', '+dtLfBgWRcW4ybhampqglG39/zxiGgwX');
define('NOWPAYMENTS_API_URL', 'https://api.nowpayments.io/v1');
define('NOWPAYMENTS_EMAIL', '<EMAIL>');
define('NOWPAYMENTS_PASSWORD', 'Yjen10,er20');
define('NOWPAYMENTS_TEST_MODE', false);

// Security settings
define('API_AUTH_ENABLED', true);
define('TELEGRAM_VALIDATION_ENABLED', true);
define('RATE_LIMIT_ENABLED', true);
define('FRAUD_DETECTION_ENABLED', true);

// Rate limiting
define('RATE_LIMIT_REQUESTS_PER_MINUTE', 60);
define('RATE_LIMIT_AD_VIEWS_PER_HOUR', 50);

// Session settings
define('SESSION_LIFETIME', 3600); // 1 hour in seconds

// Logging
define('LOG_LEVEL', 'INFO'); // DEBUG, INFO, WARNING, ERROR
define('LOG_FILE', APP_ROOT . '/logs/app.log');

// Timezone
define('APP_TIMEZONE', 'UTC');