<svg xmlns="http://www.w3.org/2000/svg" style="display: none;">
  <defs>
    <!-- Утонченные градиенты для иконок -->
    <linearGradient id="refinedGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#00ffff;stop-opacity:0.8"/>
      <stop offset="100%" style="stop-color:#0099cc;stop-opacity:0.9"/>
    </linearGradient>

    <linearGradient id="refinedGradient2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#00ff88;stop-opacity:0.8"/>
      <stop offset="100%" style="stop-color:#0088ff;stop-opacity:0.9"/>
    </linearGradient>

    <linearGradient id="refinedGradient3" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ff6b35;stop-opacity:0.8"/>
      <stop offset="100%" style="stop-color:#8a2be2;stop-opacity:0.9"/>
    </linearGradient>

    <!-- Тонкие линии -->
    <style>
      .refined-icon {
        stroke-width: 1.5;
        stroke-linecap: round;
        stroke-linejoin: round;
        fill: none;
      }
    </style>
  </defs>
  
  <!-- Утонченная иконка дома -->
  <symbol id="cyber-home" viewBox="0 0 24 24">
    <path class="refined-icon" stroke="url(#refinedGradient1)"
          d="M3 12 L12 3 L21 12 M5 12 L5 20 A1 1 0 0 0 6 21 L10 21 M19 12 L19 20 A1 1 0 0 1 18 21 L14 21"/>
    <path class="refined-icon" stroke="url(#refinedGradient1)"
          d="M10 21 L10 15 A1 1 0 0 1 11 14 L13 14 A1 1 0 0 1 14 15 L14 21"/>
    <circle cx="12" cy="7" r="0.5" fill="url(#refinedGradient1)" opacity="0.8"/>
  </symbol>

  <!-- Утонченная иконка монеты -->
  <symbol id="cyber-coin" viewBox="0 0 24 24">
    <circle class="refined-icon" cx="12" cy="12" r="9" stroke="url(#refinedGradient2)"/>
    <circle class="refined-icon" cx="12" cy="12" r="6" stroke="url(#refinedGradient2)" opacity="0.6"/>
    <path class="refined-icon" stroke="url(#refinedGradient2)"
          d="M9 9 Q12 7 15 9 Q15 12 12 14 Q9 16 9 16"/>
    <line class="refined-icon" x1="12" y1="5" x2="12" y2="8" stroke="url(#refinedGradient2)"/>
    <line class="refined-icon" x1="12" y1="16" x2="12" y2="19" stroke="url(#refinedGradient2)"/>
  </symbol>

  <!-- Утонченная иконка друзей -->
  <symbol id="cyber-friends" viewBox="0 0 24 24">
    <circle class="refined-icon" cx="9" cy="7" r="3" stroke="url(#refinedGradient1)"/>
    <circle class="refined-icon" cx="15" cy="7" r="3" stroke="url(#refinedGradient3)"/>
    <path class="refined-icon" stroke="url(#refinedGradient1)"
          d="M3 21 Q3 17 9 17 Q12 17 12 19"/>
    <path class="refined-icon" stroke="url(#refinedGradient3)"
          d="M12 19 Q12 17 15 17 Q21 17 21 21"/>
    <circle cx="12" cy="19" r="0.5" fill="url(#refinedGradient2)" opacity="0.8"/>
  </symbol>
  
  <!-- Утонченная иконка ссылки -->
  <symbol id="cyber-link" viewBox="0 0 24 24">
    <path class="refined-icon" stroke="url(#refinedGradient2)"
          d="M8 12 Q6 12 6 14 Q6 16 8 16 L10 16"/>
    <path class="refined-icon" stroke="url(#refinedGradient2)"
          d="M14 12 L16 12 Q18 12 18 14 Q18 16 16 16 L14 16"/>
    <line class="refined-icon" x1="10" y1="14" x2="14" y2="14" stroke="url(#refinedGradient2)"/>
    <circle cx="8" cy="14" r="1" fill="url(#refinedGradient1)" opacity="0.6"/>
    <circle cx="16" cy="14" r="1" fill="url(#refinedGradient3)" opacity="0.6"/>
  </symbol>

  <!-- Утонченная иконка пользователя -->
  <symbol id="cyber-user" viewBox="0 0 24 24">
    <circle class="refined-icon" cx="12" cy="8" r="4" stroke="url(#refinedGradient1)"/>
    <path class="refined-icon" stroke="url(#refinedGradient1)"
          d="M4 20 Q4 16 12 16 Q20 16 20 20"/>
    <circle cx="10" cy="7" r="0.5" fill="url(#refinedGradient3)" opacity="0.8"/>
    <circle cx="14" cy="7" r="0.5" fill="url(#refinedGradient3)" opacity="0.8"/>
    <path class="refined-icon" stroke="url(#refinedGradient3)" opacity="0.6"
          d="M10 9 Q12 10 14 9"/>
  </symbol>
  
</svg>
