# Защита includes директории
# Запрещаем прямой доступ к файлам классов
# Настройки для корректной работы с Telegram мини-приложением

<IfModule mod_headers.c>
    # РАЗРЕШАЕМ отображение в iframe для Telegram
    Header always unset X-Frame-Options
    Header always set X-Frame-Options "ALLOWALL"

    # Разрешаем CORS для PHP файлов
    Header always set Access-Control-Allow-Origin "*"
    Header always set Access-Control-Allow-Methods "GET, POST, OPTIONS"
    Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With"
</IfModule>

# Запрещаем доступ ко всем файлам
<Files "*">
    Order Deny,Allow
    Deny from all
</Files>

# Запрещаем просмотр директории
Options -Indexes

# Дополнительная защита PHP файлов
<Files "*.php">
    Order Deny,Allow
    Deny from all
</Files>
