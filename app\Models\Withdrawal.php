<?php
declare(strict_types=1);

namespace Models;

/**
 * Withdrawal.php
 * Withdrawal model for managing cryptocurrency withdrawals
 * 
 * This model handles withdrawal requests, status tracking,
 * and integration with NOWPayments API.
 */
class Withdrawal extends Model
{
    /** @var string Table name */
    protected string $table = 'withdrawals';
    
    /** @var array<string> Mass assignable fields */
    protected array $fillable = [
        'user_id',
        'amount',
        'crypto_amount',
        'currency',
        'address',
        'status',
        'nowpayments_id',
        'fee_amount',
        'requested_at',
        'processed_at',
        'completed_at',
        'error_message'
    ];

    /** @var array<string> Valid withdrawal statuses */
    public const VALID_STATUSES = [
        'pending',
        'processing', 
        'completed',
        'failed',
        'cancelled'
    ];

    /**
     * Create new withdrawal request
     * 
     * @param int $userId
     * @param int $amount Amount in coins
     * @param string $currency Crypto currency code
     * @param string $address Withdrawal address
     * @return Withdrawal|null
     */
    public static function createRequest(
        int $userId,
        int $amount,
        string $currency,
        string $address
    ): ?Withdrawal {
        // Get user
        $user = User::findByTelegramId($userId);
        if (!$user) {
            error_log("User not found for withdrawal: {$userId}");
            return null;
        }

        // Validate withdrawal
        if (!$user->canWithdraw($amount)) {
            error_log("User {$userId} cannot withdraw {$amount} coins");
            return null;
        }

        // Calculate crypto amount
        $usdAmount = $amount * CONVERSION_RATE;
        $cryptoAmount = self::calculateCryptoAmount($usdAmount, $currency);
        
        if ($cryptoAmount <= 0) {
            error_log("Invalid crypto amount calculated for {$amount} coins to {$currency}");
            return null;
        }

        // Create withdrawal record
        $withdrawal = new self([
            'user_id' => $userId,
            'amount' => $amount,
            'crypto_amount' => $cryptoAmount,
            'currency' => strtoupper($currency),
            'address' => $address,
            'status' => 'pending',
            'requested_at' => time()
        ]);

        if ($withdrawal->save()) {
            // Deduct amount from user balance
            $user->subtractBalance($amount, 'withdrawal_request');
            
            error_log("Withdrawal request created: ID {$withdrawal->id}, User {$userId}, Amount {$amount} coins");
            return $withdrawal;
        }

        return null;
    }

    /**
     * Calculate crypto amount from USD
     * 
     * @param float $usdAmount
     * @param string $currency
     * @return float
     */
    private static function calculateCryptoAmount(float $usdAmount, string $currency): float
    {
        // This would normally call NOWPayments API to get current rates
        // For now, using approximate rates
        $rates = [
            'BTC' => 45000.0,
            'ETH' => 3000.0,
            'USDT' => 1.0,
            'TON' => 2.5,
            'LTC' => 100.0,
            'BCH' => 300.0
        ];

        $rate = $rates[strtoupper($currency)] ?? 1.0;
        return $usdAmount / $rate;
    }

    /**
     * Process withdrawal through NOWPayments
     * 
     * @return bool
     */
    public function process(): bool
    {
        if ($this->status !== 'pending') {
            return false;
        }

        try {
            // Update status to processing
            $this->status = 'processing';
            $this->processed_at = time();
            $this->save();

            // Here would be the actual NOWPayments API call
            // For now, we'll simulate it
            $success = $this->simulateNOWPaymentsCall();

            if ($success) {
                $this->status = 'completed';
                $this->completed_at = time();
                $this->nowpayments_id = 'sim_' . uniqid();
            } else {
                $this->status = 'failed';
                $this->error_message = 'Payment processing failed';
                
                // Refund user balance
                $user = User::findByTelegramId($this->user_id);
                if ($user) {
                    $user->addBalance($this->amount, 'withdrawal_refund');
                }
            }

            return $this->save();

        } catch (\Exception $e) {
            $this->status = 'failed';
            $this->error_message = $e->getMessage();
            $this->save();

            // Refund user balance
            $user = User::findByTelegramId($this->user_id);
            if ($user) {
                $user->addBalance($this->amount, 'withdrawal_error_refund');
            }

            error_log("Withdrawal processing error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Simulate NOWPayments API call
     * 
     * @return bool
     */
    private function simulateNOWPaymentsCall(): bool
    {
        // Simulate 90% success rate
        return mt_rand(1, 10) <= 9;
    }

    /**
     * Cancel withdrawal
     * 
     * @param string $reason
     * @return bool
     */
    public function cancel(string $reason = 'user_request'): bool
    {
        if (!in_array($this->status, ['pending', 'processing'])) {
            return false;
        }

        $this->status = 'cancelled';
        $this->error_message = "Cancelled: {$reason}";
        
        // Refund user balance
        $user = User::findByTelegramId($this->user_id);
        if ($user) {
            $user->addBalance($this->amount, 'withdrawal_cancelled');
        }

        return $this->save();
    }

    /**
     * Get user's withdrawal history
     * 
     * @param int $userId
     * @param int $limit
     * @return array<Withdrawal>
     */
    public static function getUserHistory(int $userId, int $limit = 50): array
    {
        $instance = new self();
        $sql = "SELECT * FROM {$instance->table} 
                WHERE user_id = ? 
                ORDER BY requested_at DESC 
                LIMIT ?";
        
        try {
            $stmt = $instance->getConnection()->prepare($sql);
            $stmt->execute([$userId, $limit]);
            $results = [];
            
            while ($data = $stmt->fetch()) {
                $model = new self($data);
                $model->exists = true;
                $results[] = $model;
            }
            
            return $results;
        } catch (\PDOException $e) {
            error_log("Error getting withdrawal history: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get pending withdrawals
     * 
     * @return array<Withdrawal>
     */
    public static function getPending(): array
    {
        return self::where(['status' => 'pending']);
    }

    /**
     * Get withdrawal statistics
     * 
     * @param int|null $userId
     * @param int|null $days
     * @return array<string, mixed>
     */
    public static function getStats(?int $userId = null, ?int $days = 30): array
    {
        $startTime = time() - ($days * 24 * 3600);
        $instance = new self();
        
        $whereClause = "WHERE requested_at >= ?";
        $params = [$startTime];
        
        if ($userId) {
            $whereClause .= " AND user_id = ?";
            $params[] = $userId;
        }
        
        $sql = "SELECT 
                    status,
                    currency,
                    COUNT(*) as count,
                    SUM(amount) as total_coins,
                    SUM(crypto_amount) as total_crypto,
                    AVG(crypto_amount) as avg_crypto
                FROM {$instance->table} 
                {$whereClause}
                GROUP BY status, currency";
        
        try {
            $stmt = $instance->getConnection()->prepare($sql);
            $stmt->execute($params);
            
            $stats = [];
            while ($row = $stmt->fetch()) {
                $key = $row['status'] . '_' . $row['currency'];
                $stats[$key] = [
                    'count' => (int)$row['count'],
                    'total_coins' => (int)$row['total_coins'],
                    'total_crypto' => (float)$row['total_crypto'],
                    'avg_crypto' => (float)$row['avg_crypto']
                ];
            }
            
            return $stats;
        } catch (\PDOException $e) {
            error_log("Error getting withdrawal stats: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Convert to array for API response
     * 
     * @return array<string, mixed>
     */
    public function toApiArray(): array
    {
        return [
            'id' => $this->id,
            'amount' => $this->amount,
            'crypto_amount' => $this->crypto_amount,
            'currency' => $this->currency,
            'address' => $this->address,
            'status' => $this->status,
            'requested_at' => $this->requested_at,
            'processed_at' => $this->processed_at,
            'completed_at' => $this->completed_at,
            'error_message' => $this->error_message
        ];
    }
}
