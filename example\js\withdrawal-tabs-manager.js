/**
 * Менеджер табов калькулятора и выплат (из оригинала)
 * Описание: Управляет переключением между разделами калькулятора и выплат
 */

class WithdrawalTabsManager {
  constructor() {
    this.currentTab = null; // По умолчанию ничего не показано
    
    // Элементы разделов (из оригинала)
    this.sections = {
      calculator: document.getElementById('calculator-section'),
      withdrawal: document.getElementById('withdrawal-section')
    };
    
    // Кнопки переключения (из оригинала)
    this.tabs = {
      calculator: document.getElementById('calculator-tab'),
      withdrawal: document.getElementById('withdrawal-tab')
    };
    
    this.init();
  }

  init() {
    console.log('[WithdrawalTabsManager] Инициализация менеджера табов...');

    // Проверяем что элементы найдены
    this.checkElements();

    // Настраиваем обработчики
    this.setupEventListeners();

    // ИСПРАВЛЕНИЕ: По умолчанию все разделы скрыты, кнопки неактивны
    this.hideAllSections();
    this.deactivateAllTabs();

    // Сбрасываем текущий таб
    this.currentTab = null;

    console.log('[WithdrawalTabsManager] ✅ Менеджер табов инициализирован (все разделы скрыты)');
  }

  checkElements() {
    const missingSections = Object.entries(this.sections)
      .filter(([key, element]) => !element)
      .map(([key]) => key);
    
    const missingTabs = Object.entries(this.tabs)
      .filter(([key, element]) => !element)
      .map(([key]) => key);
    
    if (missingSections.length > 0) {
      console.warn('[WithdrawalTabsManager] Не найдены разделы:', missingSections);
    }
    if (missingTabs.length > 0) {
      console.warn('[WithdrawalTabsManager] Не найдены кнопки табов:', missingTabs);
    }
  }

  setupEventListeners() {
    console.log('[WithdrawalTabsManager] Настройка обработчиков событий...');

    // Обработчик для кнопки калькулятора
    if (this.tabs.calculator) {
      this.tabs.calculator.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        console.log('[WithdrawalTabsManager] 🧮 Клик по кнопке калькулятора');
        this.switchToTab('calculator');
        smoothScrollTo('calculator-section');
      });
      console.log('[WithdrawalTabsManager] ✅ Обработчик кнопки калькулятора установлен');
    } else {
      console.warn('[WithdrawalTabsManager] ❌ Кнопка калькулятора не найдена');
    }

    // Обработчик для кнопки выплат
    if (this.tabs.withdrawal) {
      this.tabs.withdrawal.addEventListener('click', () => {
        console.log('[WithdrawalTabsManager] Клик по кнопке выплат');
        this.switchToTab('withdrawal');
        smoothScrollTo('withdrawal-section');
      });
      console.log('[WithdrawalTabsManager] ✅ Обработчик кнопки выплат установлен');
    } else {
      console.warn('[WithdrawalTabsManager] ❌ Кнопка выплат не найдена');
    }
  }

  /**
   * Переключает на указанный таб (из оригинала)
   */
  switchToTab(tabName) {
    console.log(`[WithdrawalTabsManager] Переключение на таб: ${tabName}`);
    
    if (!this.sections[tabName] || !this.tabs[tabName]) {
      console.error(`[WithdrawalTabsManager] Таб ${tabName} не найден`);
      return;
    }

    // ИСПРАВЛЕНИЕ: Убираем проверку активного таба - всегда показываем раздел при клике
    if (this.currentTab === tabName) {
      console.log(`[WithdrawalTabsManager] Таб ${tabName} уже активен, но принудительно обновляем`);
      // НЕ возвращаемся, продолжаем выполнение
    }

    // Скрываем все разделы
    this.hideAllSections();
    
    // Деактивируем все кнопки
    this.deactivateAllTabs();
    
    // ИСПРАВЛЕНИЕ: Показываем нужный раздел принудительно
    this.sections[tabName].classList.remove('hidden');
    this.sections[tabName].classList.add('active');
    this.sections[tabName].style.display = 'block';
    this.sections[tabName].style.visibility = 'visible';
    
    // Активируем нужную кнопку
    this.tabs[tabName].classList.add('active');
    // ИСПРАВЛЕНИЕ: Убираем класс inactive с активной кнопки
    this.tabs[tabName].classList.remove('inactive');
    
    // Обновляем текущий таб
    this.currentTab = tabName;
    
    console.log(`[WithdrawalTabsManager] ✅ Переключено на таб: ${tabName}`);
    
    // Выполняем специальные действия для табов
    this.onTabSwitched(tabName);
  }

  /**
   * Скрывает все разделы (из оригинала)
   */
  hideAllSections() {
    Object.values(this.sections).forEach(section => {
      if (section) {
        section.classList.add('hidden');
        section.classList.remove('active');
      }
    });
  }

  /**
   * Деактивирует все кнопки табов (из оригинала)
   */
  deactivateAllTabs() {
    Object.values(this.tabs).forEach(tab => {
      if (tab) {
        tab.classList.remove('active');
        // ИСПРАВЛЕНИЕ: Добавляем класс inactive когда таб неактивен
        tab.classList.add('inactive');
      }
    });
  }

  /**
   * Выполняет специальные действия при переключении табов (из оригинала)
   */
  onTabSwitched(tabName) {
    if (tabName === 'calculator') {
      // Инициализируем калькулятор
      if (window.calculatorManager && window.calculatorManager.init) {
        window.calculatorManager.init();
      }
      
      // Обновляем баланс
      if (window.balanceManager && window.calculatorManager) {
        window.calculatorManager.updateBalanceDisplay();
      }
      
      console.log('[WithdrawalTabsManager] Калькулятор активирован');
    }
    
    if (tabName === 'withdrawal') {
      // Инициализируем форму выплат
      if (window.withdrawalFormManager && window.withdrawalFormManager.init) {
        window.withdrawalFormManager.init();
      }
      
      // Валидируем форму
      if (window.withdrawalFormManager && window.withdrawalFormManager.validateForm) {
        window.withdrawalFormManager.validateForm();
      }
      
      console.log('[WithdrawalTabsManager] Форма выплат активирована');
    }
  }

  /**
   * Возвращает текущий активный таб
   */
  getCurrentTab() {
    return this.currentTab;
  }

  /**
   * Проверяет активен ли указанный таб
   */
  isTabActive(tabName) {
    return this.currentTab === tabName;
  }
}

// Создаем экземпляр менеджера
try {
  window.withdrawalTabsManager = new WithdrawalTabsManager();
  
  // Экспорт функций для обратной совместимости
  window.switchToCalculator = () => window.withdrawalTabsManager.switchToTab('calculator');
  window.switchToWithdrawal = () => window.withdrawalTabsManager.switchToTab('withdrawal');
  
  console.log('📊 [WithdrawalTabsManager] Менеджер табов загружен.');
} catch (error) {
  console.error('📊 [WithdrawalTabsManager] Ошибка при создании менеджера табов:', error);
  
  // Создаем заглушку
  window.withdrawalTabsManager = {
    switchToTab: (tabName) => console.log(`[WithdrawalTabsManager] Заглушка переключения на ${tabName}`),
    getCurrentTab: () => null,
    isTabActive: () => false
  };
  
  window.switchToCalculator = () => console.log('[WithdrawalTabsManager] Заглушка калькулятора');
  window.switchToWithdrawal = () => console.log('[WithdrawalTabsManager] Заглушка выплат');
}
