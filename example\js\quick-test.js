/**
 * === quick-test.js ===
 * Быстрое тестирование ООП системы без полной загрузки
 */

// Простая функция для тестирования навигации
function quickTestNavigation() {
  console.log('🧪 Быстрое тестирование навигации...');
  
  // Находим кнопки навигации
  const navButtons = document.querySelectorAll('.nav-button');
  const pages = document.querySelectorAll('.page, .app-section, .app-main');
  
  console.log(`Найдено кнопок навигации: ${navButtons.length}`);
  console.log(`Найдено страниц: ${pages.length}`);
  
  // Показываем ID всех найденных элементов
  navButtons.forEach(btn => {
    console.log(`Кнопка: ID="${btn.id}", data-page="${btn.dataset.page}"`);
  });
  
  pages.forEach(page => {
    console.log(`Страница: ID="${page.id}", классы="${page.className}"`);
  });
  
  // Простая навигация без анимаций
  navButtons.forEach(button => {
    button.addEventListener('click', () => {
      const targetPageId = button.dataset.page || button.getAttribute('data-target');
      console.log(`Клик по кнопке: ${button.id}, цель: ${targetPageId}`);
      
      if (targetPageId) {
        // Скрываем все страницы
        pages.forEach(page => {
          page.style.display = 'none';
          page.classList.remove('active');
        });
        
        // Показываем целевую страницу
        const targetPage = document.getElementById(targetPageId);
        if (targetPage) {
          targetPage.style.display = 'block';
          targetPage.classList.add('active');
          console.log(`✅ Показана страница: ${targetPageId}`);
        } else {
          console.warn(`❌ Страница не найдена: ${targetPageId}`);
        }
        
        // Обновляем активную кнопку
        navButtons.forEach(btn => btn.classList.remove('active'));
        button.classList.add('active');
      }
    });
  });
  
  console.log('✅ Быстрое тестирование навигации настроено');
}

// Простая функция для показа статуса
function quickShowStatus(message, type = 'info') {
  console.log(`[${type.toUpperCase()}] ${message}`);
  
  const statusEl = document.getElementById('status-message');
  if (statusEl) {
    statusEl.textContent = message;
    statusEl.className = `status-message ${type}`;
    statusEl.style.display = 'block';
    
    setTimeout(() => {
      statusEl.style.display = 'none';
    }, 3000);
  }
}

// Простая инициализация
function quickInit() {
  console.log('🚀 Быстрая инициализация для тестирования...');
  
  // Ждем готовности DOM
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      quickTestNavigation();
      quickShowStatus('Быстрое тестирование готово!', 'success');
    });
  } else {
    quickTestNavigation();
    quickShowStatus('Быстрое тестирование готово!', 'success');
  }
}

// Экспорт функций
window.quickInit = quickInit;
window.quickTestNavigation = quickTestNavigation;
window.quickShowStatus = quickShowStatus;

console.log('📦 [QuickTest] Быстрое тестирование загружено');

// Автоматический запуск
quickInit();
