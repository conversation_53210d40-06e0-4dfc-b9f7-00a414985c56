<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Тёмный градиент фона -->
    <linearGradient id="darkBgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1a1a1a;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#2d2d2d;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3a3a3a;stop-opacity:1" />
    </linearGradient>
    <!-- Солнечно-жёлтый градиент -->
    <linearGradient id="sunnyGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FFA500;stop-opacity:1" />
    </linearGradient>
    <!-- Мягкое свечение -->
    <radialGradient id="softGlow" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#FFD700;stop-opacity:0.15" />
      <stop offset="100%" style="stop-color:#FFA500;stop-opacity:0" />
    </radialGradient>
    <!-- Фильтр для мягкой тени -->
    <filter id="softShadow">
      <feDropShadow dx="0" dy="2" stdDeviation="4" flood-color="#FFD700" flood-opacity="0.3"/>
    </filter>
  </defs>

  <!-- Тёмный фон -->
  <circle cx="256" cy="256" r="256" fill="url(#darkBgGradient)"/>

  <!-- Мягкое свечение -->
  <circle cx="256" cy="256" r="180" fill="url(#softGlow)"/>

  <!-- Основная монета в солнечных тонах -->
  <circle cx="256" cy="200" r="75" fill="url(#sunnyGradient)" stroke="#FFD700" stroke-width="4" filter="url(#softShadow)"/>

  <!-- Внутренний круг монеты -->
  <circle cx="256" cy="200" r="58" fill="none" stroke="#2d2d2d" stroke-width="2" opacity="0.7"/>

  <!-- Стильная иконка кошелька -->
  <g transform="translate(256, 200)" fill="#2d2d2d">
    <rect x="-28" y="-14" width="56" height="28" rx="5" fill="none" stroke="currentColor" stroke-width="3"/>
    <path d="M-28 -2h56" stroke="currentColor" stroke-width="3"/>
    <circle cx="16" cy="3" r="2.5" fill="currentColor"/>
    <path d="M-20 -14v-5a5 5 0 0 1 5-5h28a5 5 0 0 1 5 5v5" fill="none" stroke="currentColor" stroke-width="3"/>
  </g>

  <!-- Орбитальные монеты в солнечных тонах -->
  <circle cx="160" cy="150" r="24" fill="url(#sunnyGradient)" opacity="0.9"/>
  <text x="160" y="159" font-family="Arial Black, sans-serif" font-size="18" font-weight="bold" text-anchor="middle" fill="#2d2d2d">$</text>

  <circle cx="360" cy="170" r="28" fill="url(#sunnyGradient)" opacity="0.8"/>
  <text x="360" y="180" font-family="Arial Black, sans-serif" font-size="22" font-weight="bold" text-anchor="middle" fill="#2d2d2d">$</text>

  <circle cx="180" cy="280" r="19" fill="url(#sunnyGradient)" opacity="0.7"/>
  <text x="180" y="287" font-family="Arial Black, sans-serif" font-size="15" font-weight="bold" text-anchor="middle" fill="#2d2d2d">$</text>

  <circle cx="340" cy="260" r="21" fill="url(#sunnyGradient)" opacity="0.7"/>
  <text x="340" y="267" font-family="Arial Black, sans-serif" font-size="17" font-weight="bold" text-anchor="middle" fill="#2d2d2d">$</text>

  <!-- Название бота в солнечных тонах -->
  <text x="256" y="340" font-family="Inter, sans-serif" font-size="42" font-weight="700" text-anchor="middle" fill="#FFD700" filter="url(#softShadow)">UniQ</text>
  <text x="256" y="380" font-family="Inter, sans-serif" font-size="42" font-weight="700" text-anchor="middle" fill="#FFA500" filter="url(#softShadow)">Paid</text>

  <!-- Подзаголовок -->
  <text x="256" y="410" font-family="Inter, sans-serif" font-size="18" font-weight="500" text-anchor="middle" fill="#FFD700" opacity="0.8">CRYPTO WALLET</text>

  <!-- Стильные декоративные элементы -->
  <circle cx="120" cy="380" r="3" fill="#FFD700" opacity="0.7"/>
  <circle cx="390" cy="350" r="2.5" fill="#FFA500" opacity="0.6"/>
  <circle cx="140" cy="120" r="2" fill="#FF8C00" opacity="0.5"/>
  <circle cx="380" cy="400" r="3.5" fill="#FFD700" opacity="0.6"/>

  <!-- Стильная иконка рекламы -->
  <rect x="220" y="440" width="72" height="50" rx="8" fill="#FFD700" opacity="0.9" filter="url(#softShadow)"/>
  <circle cx="256" cy="465" r="7" fill="#2d2d2d"/>
  <polygon points="252,462 252,468 262,465" fill="#2d2d2d"/>
  <text x="256" y="505" font-family="Arial, sans-serif" font-size="10" font-weight="bold" text-anchor="middle" fill="#2d2d2d">ADS</text>
</svg>
