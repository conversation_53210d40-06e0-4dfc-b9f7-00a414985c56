<?php
declare(strict_types=1);

/**
 * TelegramValidator.php
 * Telegram WebApp initData validation class
 * 
 * This class validates Telegram WebApp initData to ensure
 * the authenticity of user data and prevent tampering.
 */
class TelegramValidator
{
    /**
     * Validate Telegram initData
     * 
     * @param string $initData
     * @return array<string, mixed>|false User data if valid, false otherwise
     */
    public function validate(string $initData): array|false
    {
        if (!TELEGRAM_VALIDATION_ENABLED) {
            return $this->extractUserDataForDevelopment($initData);
        }

        try {
            // Parse initData
            parse_str($initData, $data);
            
            if (!isset($data['hash'])) {
                error_log('TelegramValidator: No hash found in initData');
                return false;
            }

            $hash = $data['hash'];
            unset($data['hash']);

            // Create data check string
            $dataCheckString = $this->createDataCheckString($data);
            
            // Calculate secret key
            $secretKey = hash_hmac('sha256', BOT_TOKEN, 'WebAppData', true);
            
            // Calculate expected hash
            $expectedHash = hash_hmac('sha256', $dataCheckString, $secretKey);
            
            // Verify hash
            if (!hash_equals($expectedHash, $hash)) {
                error_log('TelegramValidator: Hash verification failed');
                return false;
            }

            // Check auth_date (data should not be older than 24 hours)
            if (isset($data['auth_date'])) {
                $authDate = (int)$data['auth_date'];
                $currentTime = time();
                
                if ($currentTime - $authDate > 86400) { // 24 hours
                    error_log('TelegramValidator: initData is too old');
                    return false;
                }
            }

            // Extract and return user data
            if (isset($data['user'])) {
                $userData = json_decode($data['user'], true);
                if ($userData && isset($userData['id'])) {
                    return $userData;
                }
            }

            error_log('TelegramValidator: No valid user data found');
            return false;

        } catch (Exception $e) {
            error_log('TelegramValidator: Validation error - ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Create data check string for hash verification
     * 
     * @param array<string, mixed> $data
     * @return string
     */
    private function createDataCheckString(array $data): string
    {
        $pairs = [];
        
        foreach ($data as $key => $value) {
            $pairs[] = $key . '=' . $value;
        }
        
        sort($pairs);
        
        return implode("\n", $pairs);
    }

    /**
     * Extract user data for development (without validation)
     * 
     * @param string $initData
     * @return array<string, mixed>
     */
    private function extractUserDataForDevelopment(string $initData): array
    {
        parse_str($initData, $data);
        
        if (isset($data['user'])) {
            $userData = json_decode($data['user'], true);
            if ($userData && isset($userData['id'])) {
                return $userData;
            }
        }
        
        // Fallback test user for development
        return [
            'id' => 12345,
            'first_name' => 'Test',
            'last_name' => 'User',
            'username' => 'testuser',
            'language_code' => 'en'
        ];
    }

    /**
     * Validate specific user ID against initData
     * 
     * @param string $initData
     * @param int $expectedUserId
     * @return bool
     */
    public function validateUserId(string $initData, int $expectedUserId): bool
    {
        $userData = $this->validate($initData);
        
        if (!$userData) {
            return false;
        }
        
        return (int)$userData['id'] === $expectedUserId;
    }

    /**
     * Extract query ID from initData (for callback queries)
     * 
     * @param string $initData
     * @return string|null
     */
    public function extractQueryId(string $initData): ?string
    {
        parse_str($initData, $data);
        return $data['query_id'] ?? null;
    }

    /**
     * Check if initData contains specific fields
     * 
     * @param string $initData
     * @param array<string> $requiredFields
     * @return bool
     */
    public function hasRequiredFields(string $initData, array $requiredFields): bool
    {
        parse_str($initData, $data);
        
        foreach ($requiredFields as $field) {
            if (!isset($data[$field])) {
                return false;
            }
        }
        
        return true;
    }
}
