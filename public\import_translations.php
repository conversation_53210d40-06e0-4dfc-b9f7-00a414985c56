<?php
declare(strict_types=1);

header('Content-Type: text/plain');

define('APP_ROOT', dirname(__DIR__));
require_once APP_ROOT . '/app/config.php';
require_once APP_ROOT . '/app/Models/Translation.php';

use Models\Translation;

echo "Importing translations into database...\n";

try {
    // English translations
    $enTranslations = [
        // App
        'app.name' => 'UniQPaid',
        'app.title' => 'UniQPaid - Crypto Wallet',
        'app.loading' => 'Loading...',
        'app.initializing' => 'Initializing application...',
        'app.error' => 'Error',
        'app.success' => 'Success',
        
        // Tasks
        'tasks.title' => 'Tasks',
        'tasks.open_link' => 'Open Link',
        'tasks.watch_video' => 'Watch Video',
        'tasks.tap_banner' => 'Tap and Earn',
        
        // Buttons
        'buttons.open_link' => 'Open Link',
        'buttons.watch_video' => 'Watch Video',
        'buttons.tap_banner' => 'Tap and Earn',
        'buttons.withdraw' => 'Withdraw',
        'buttons.cancel' => 'Cancel',
        'buttons.confirm' => 'Confirm',
        
        // Rewards
        'rewards.earned' => 'You earned {amount} coins!',
        'rewards.daily_limit' => 'Daily limit reached',
        'rewards.coins' => 'coins',
        
        // Balance
        'balance.current' => 'Current Balance',
        'balance.coins' => 'coins',
        
        // Withdrawal
        'withdrawal.title' => 'Withdraw Funds',
        'withdrawal.minimum' => 'Minimum withdrawal: {amount} coins',
        'withdrawal.success' => 'Withdrawal request submitted successfully!',
        
        // Referrals
        'referrals.title' => 'Invite Friends',
        'referrals.your_code' => 'Your referral code',
        'referrals.invite_link' => 'Invitation link',
        
        // Admin
        'admin.title' => 'Admin Panel',
        'admin.users' => 'Users',
        'admin.withdrawals' => 'Withdrawals',
        
        // Errors
        'errors.network' => 'Network error. Please try again.',
        'errors.server' => 'Server error. Please try again later.',
        'errors.blocked' => 'Your account has been blocked.',
        
        // Status
        'status.loading' => 'Loading',
        'status.pending' => 'Pending',
        'status.completed' => 'Completed'
    ];

    // Russian translations
    $ruTranslations = [
        // App
        'app.name' => 'UniQPaid',
        'app.title' => 'UniQPaid - Криптокошелёк',
        'app.loading' => 'Загрузка...',
        'app.initializing' => 'Инициализация приложения...',
        'app.error' => 'Ошибка',
        'app.success' => 'Успешно',
        
        // Tasks
        'tasks.title' => 'Задания',
        'tasks.open_link' => 'Открыть ссылку',
        'tasks.watch_video' => 'Смотреть видео',
        'tasks.tap_banner' => 'Кликнуть по баннеру',
        
        // Buttons
        'buttons.open_link' => 'Открыть ссылку',
        'buttons.watch_video' => 'Смотреть видео',
        'buttons.tap_banner' => 'Кликнуть по баннеру',
        'buttons.withdraw' => 'Вывести',
        'buttons.cancel' => 'Отмена',
        'buttons.confirm' => 'Подтвердить',
        
        // Rewards
        'rewards.earned' => 'Вы заработали {amount} монет!',
        'rewards.daily_limit' => 'Дневной лимит достигнут',
        'rewards.coins' => 'монет',
        
        // Balance
        'balance.current' => 'Текущий баланс',
        'balance.coins' => 'монет',
        
        // Withdrawal
        'withdrawal.title' => 'Вывод средств',
        'withdrawal.minimum' => 'Минимальный вывод: {amount} монет',
        'withdrawal.success' => 'Запрос на вывод успешно отправлен!',
        
        // Referrals
        'referrals.title' => 'Пригласить друзей',
        'referrals.your_code' => 'Ваш реферальный код',
        'referrals.invite_link' => 'Ссылка-приглашение',
        
        // Admin
        'admin.title' => 'Панель администратора',
        'admin.users' => 'Пользователи',
        'admin.withdrawals' => 'Выводы',
        
        // Errors
        'errors.network' => 'Ошибка сети. Попробуйте ещё раз.',
        'errors.server' => 'Ошибка сервера. Попробуйте позже.',
        'errors.blocked' => 'Ваш аккаунт заблокирован.',
        
        // Status
        'status.loading' => 'Загрузка',
        'status.pending' => 'Ожидание',
        'status.completed' => 'Завершено'
    ];

    // Import English translations
    $enCount = Translation::importTranslations($enTranslations, 'en', 'general');
    echo "Imported {$enCount} English translations\n";

    // Import Russian translations
    $ruCount = Translation::importTranslations($ruTranslations, 'ru', 'general');
    echo "Imported {$ruCount} Russian translations\n";

    echo "✅ Translation import completed successfully!\n";
    echo "Total translations: " . ($enCount + $ruCount) . "\n";

} catch (Exception $e) {
    echo "❌ Translation import failed: " . $e->getMessage() . "\n";
}
