# .htaccess для папки images
# Настройки для корректной работы с изображениями в Telegram мини-приложении

<IfModule mod_headers.c>
    # РАЗРЕШАЕМ отображение в iframe для Telegram
    Header always unset X-Frame-Options
    Header always set X-Frame-Options "ALLOWALL"
    
    # Разрешаем CORS для изображений
    Header always set Access-Control-Allow-Origin "*"
    Header always set Access-Control-Allow-Methods "GET, OPTIONS"
    Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With"
</IfModule>

# Разрешаем доступ ко всем изображениям
<Files "*.svg">
    Order Allow,Deny
    Allow from all
</Files>

<Files "*.png">
    Order Allow,Deny
    Allow from all
</Files>

<Files "*.jpg">
    Order Allow,<PERSON>y
    Allow from all
</Files>

<Files "*.jpeg">
    Order Allow,<PERSON>y
    Allow from all
</Files>

<Files "*.gif">
    Order Allow,Deny
    Allow from all
</Files>

<Files "*.webp">
    Order Allow,Deny
    Allow from all
</Files>

# Кэширование изображений
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
</IfModule>

# Сжатие SVG файлов
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE image/svg+xml
</IfModule>

# Запрещаем просмотр директорий
Options -Indexes
