/**
 * js/server-ad-counters.js
 * Модуль для управления счетчиками показов рекламы через сервер
 */

class ServerAdCountersManager {
  constructor() {
    this.apiUrl = 'api/ad_limits_manager.php';
    this.userId = null;
    this.isInitialized = false;
    this.localization = null;

    // Маппинг типов рекламы на ID элементов (из AdsConfig)
    this.adTypeMapping = {};
    this.initializeFromConfig();

    // Кэш данных для быстрого доступа
    this.cachedLimits = {};

    console.log('[ServerAdCounters] 🚀 Инициализирован');
  }

  /**
   * Инициализация конфигурации из AdsConfig
   */
  initializeFromConfig() {
    if (window.AdsConfig) {
      // Получаем типы рекламы и создаем маппинги
      Object.values(window.AdsConfig.AD_TYPES).forEach(adType => {
        this.adTypeMapping[adType.id] = adType.counterId;
      });

      console.log('[ServerAdCounters] ✅ Конфигурация загружена из AdsConfig:', this.adTypeMapping);
    } else {
      // Fallback на старые значения
      this.adTypeMapping = {
        'native_banner': 'native-banner-counter',
        'rewarded_video': 'rewarded-video-counter',
        'interstitial': 'interstitial-counter'
      };

      console.warn('[ServerAdCounters] ⚠️ AdsConfig не найден, используем fallback конфигурацию');
    }
  }

  /**
   * Инициализация менеджера
   */
  async init() {
    if (this.isInitialized) {
      console.warn('[ServerAdCounters] Уже инициализирован');
      return;
    }

    try {
      // Получаем ID пользователя из Telegram WebApp
      this.userId = this.getTelegramUserId();
      
      if (!this.userId) {
        console.warn('[ServerAdCounters] Не удалось получить ID пользователя');
        return;
      }

      // Ждем загрузки локализации
      await this.waitForLocalization();

      // Загружаем текущие лимиты с сервера
      await this.loadUserLimits();

      // Обновляем отображение
      this.updateAllCounters();

      // Обновляем блокировку кнопок при инициализации
      if (window.adsManagerFull && window.adsManagerFull.updateLimitsDisplay) {
        console.log(`[ServerAdCounters] 🔄 Обновляем блокировку кнопок при инициализации`);
        window.adsManagerFull.updateLimitsDisplay();
      }

      this.isInitialized = true;
      console.log(`[ServerAdCounters] ✅ Инициализирован для пользователя ${this.userId}`);
      
    } catch (error) {
      console.error('[ServerAdCounters] Ошибка инициализации:', error);
    }
  }

  /**
   * Получает ID пользователя из Telegram WebApp
   */
  getTelegramUserId() {
    // Пробуем получить из Telegram WebApp
    if (window.Telegram && window.Telegram.WebApp && window.Telegram.WebApp.initDataUnsafe) {
      const user = window.Telegram.WebApp.initDataUnsafe.user;
      if (user && user.id) {
        return user.id.toString();
      }
    }

    // Fallback: пробуем получить из других источников
    if (window.telegramUserId) {
      return window.telegramUserId.toString();
    }

    // Для тестирования всегда используем фиксированный ID 5880288830
    console.log('[ServerAdCounters] 🧪 Используем тестовый ID: 5880288830');
    return '5880288830';
  }

  /**
   * Ожидание загрузки системы локализации
   */
  async waitForLocalization() {
    let attempts = 0;
    const maxAttempts = 50; // 5 секунд максимум

    while (attempts < maxAttempts) {
      if (window.appLocalization && window.appLocalization.isLoaded) {
        this.localization = window.appLocalization;
        console.log('[ServerAdCounters] ✅ Локализация загружена');
        return;
      }
      
      await new Promise(resolve => setTimeout(resolve, 100));
      attempts++;
    }
    
    console.warn('[ServerAdCounters] ⚠️ Локализация не загружена, используем дефолтные тексты');
  }

  /**
   * Загружает лимиты пользователя с сервера
   */
  async loadUserLimits() {
    try {
      const response = await fetch(`${this.apiUrl}?action=get_user_limits&user_id=${this.userId}`);
      const result = await response.json();
      
      if (result.success) {
        this.cachedLimits = result.data;
        console.log('[ServerAdCounters] 📊 Лимиты загружены с сервера:', this.cachedLimits);
      } else {
        throw new Error(result.error || 'Неизвестная ошибка');
      }
      
    } catch (error) {
      console.error('[ServerAdCounters] ❌ Ошибка загрузки лимитов:', error);
      throw error;
    }
  }

  /**
   * Увеличивает счетчик на сервере
   */
  async incrementCounter(adType) {
    if (!this.userId) {
      console.error('[ServerAdCounters] Нет ID пользователя');
      return false;
    }

    try {
      console.log(`[ServerAdCounters] 📈 Увеличиваем счетчик ${adType} для пользователя ${this.userId}`);
      
      const formData = new FormData();
      formData.append('action', 'increment_counter');
      formData.append('user_id', this.userId);
      formData.append('ad_type', adType);

      const response = await fetch(this.apiUrl, {
        method: 'POST',
        body: formData
      });

      const result = await response.json();
      
      if (result.success) {
        // Обновляем кэш
        if (!this.cachedLimits[adType]) {
          this.cachedLimits[adType] = {};
        }
        
        this.cachedLimits[adType].current = result.data.new_count;
        this.cachedLimits[adType].remaining = result.data.remaining;
        this.cachedLimits[adType].isLimitReached = result.data.is_limit_reached;
        
        // Обновляем отображение
        this.updateCounter(adType);

        // КРИТИЧЕСКИ ВАЖНО: Обновляем блокировку кнопок при достижении лимитов
        if (window.adsManagerFull && window.adsManagerFull.updateLimitsDisplay) {
          console.log(`[ServerAdCounters] 🔄 Обновляем блокировку кнопок`);
          window.adsManagerFull.updateLimitsDisplay();
        }

        console.log(`[ServerAdCounters] ✅ Счетчик ${adType} увеличен: ${result.data.new_count}, осталось: ${result.data.remaining}`);

        return result.data;
      } else {
        throw new Error(result.error || 'Ошибка увеличения счетчика');
      }
      
    } catch (error) {
      console.error(`[ServerAdCounters] ❌ Ошибка увеличения счетчика ${adType}:`, error);
      return false;
    }
  }

  /**
   * Обновляет отображение конкретного счетчика
   */
  updateCounter(adType) {
    const counterId = this.adTypeMapping[adType];
    if (!counterId) {
      console.warn(`[ServerAdCounters] Неизвестный тип рекламы: ${adType}`);
      return;
    }

    const counterElement = document.getElementById(counterId);
    if (!counterElement) {
      console.warn(`[ServerAdCounters] Элемент счетчика не найден: ${counterId}`);
      return;
    }

    const limitData = this.cachedLimits[adType];
    if (!limitData) {
      console.warn(`[ServerAdCounters] Нет данных для типа рекламы: ${adType}`);
      return;
    }

    const remaining = limitData.remaining || 0;
    const counterText = this.formatCounterText(remaining);

    // Анимированное обновление
    this.animateCounterUpdate(counterElement, counterText);

    // Добавляем класс для стилизации если лимит достигнут
    if (remaining === 0) {
      counterElement.classList.add('limit-reached');
    } else {
      counterElement.classList.remove('limit-reached');
    }

    console.log(`[ServerAdCounters] 🔄 Обновлен счетчик ${adType}: ${counterText}`);
  }

  /**
   * Обновляет все счетчики
   */
  updateAllCounters() {
    console.log('[ServerAdCounters] 🔄 Обновление всех счетчиков');

    Object.keys(this.adTypeMapping).forEach(adType => {
      this.updateCounter(adType);
    });
  }

  /**
   * Получает информацию о всех лимитах (для совместимости с ads-manager-full.js)
   */
  getAllLimitsInfo() {
    const info = {};

    Object.keys(this.adTypeMapping).forEach(adType => {
      const limitData = this.cachedLimits[adType];
      if (limitData) {
        info[adType] = {
          current: limitData.current || 0,
          limit: limitData.limit || 20,
          remaining: limitData.remaining || 20,
          isLimitReached: limitData.isLimitReached || false
        };
      } else {
        // Fallback данные если лимиты не загружены
        info[adType] = {
          current: 0,
          limit: 20,
          remaining: 20,
          isLimitReached: false
        };
      }
    });

    return info;
  }

  /**
   * Форматирует текст счетчика с учетом локализации
   */
  formatCounterText(count) {
    const language = this.localization?.currentLanguage || 'ru';

    // Простая и надежная система переводов
    if (language === 'en') {
      if (count === 0) return 'limit reached';
      if (count === 1) return '1 ad view left';
      return `${count} ad views left`;
    } else {
      // Русский язык с правильными склонениями
      if (count === 0) return 'лимит исчерпан';
      if (count === 1) return 'остался 1 показ';
      if (count >= 2 && count <= 4) return `осталось ${count} показа`;
      return `осталось ${count} показов`;
    }
  }

  /**
   * Анимированное обновление текста счетчика
   */
  animateCounterUpdate(element, newText) {
    if (!element) return;
    
    // Если текст не изменился, не анимируем
    if (element.textContent === newText) return;
    
    // Плавная анимация изменения
    element.style.transition = 'opacity 0.3s ease-in-out, transform 0.3s ease-in-out';
    element.style.opacity = '0.7';
    element.style.transform = 'scale(0.95)';
    
    setTimeout(() => {
      element.textContent = newText;
      element.style.opacity = '1';
      element.style.transform = 'scale(1)';
      
      // Убираем стили через некоторое время
      setTimeout(() => {
        element.style.transition = '';
        element.style.transform = '';
      }, 300);
    }, 150);
  }

  /**
   * Проверяет лимит для типа рекламы
   */
  isLimitReached(adType) {
    const limitData = this.cachedLimits[adType];
    return limitData ? limitData.isLimitReached : false;
  }

  /**
   * Получает оставшееся количество показов
   */
  getRemainingCount(adType) {
    const limitData = this.cachedLimits[adType];
    return limitData ? limitData.remaining : 0;
  }

  /**
   * Получает полную информацию о лимитах
   */
  getAllLimitsInfo() {
    return { ...this.cachedLimits };
  }

  /**
   * Перезагружает лимиты с сервера
   */
  async refreshLimits() {
    if (!this.isInitialized) {
      console.warn('[ServerAdCounters] Не инициализирован');
      return;
    }

    try {
      await this.loadUserLimits();
      this.updateAllCounters();
      console.log('[ServerAdCounters] 🔄 Лимиты обновлены с сервера');
    } catch (error) {
      console.error('[ServerAdCounters] ❌ Ошибка обновления лимитов:', error);
    }
  }

  /**
   * Сбрасывает счетчики пользователя (для тестирования)
   */
  async resetCounters() {
    if (!this.userId) {
      console.error('[ServerAdCounters] Нет ID пользователя');
      return false;
    }

    try {
      const formData = new FormData();
      formData.append('action', 'reset_user');
      formData.append('user_id', this.userId);

      const response = await fetch(this.apiUrl, {
        method: 'POST',
        body: formData
      });

      const result = await response.json();
      
      if (result.success) {
        await this.refreshLimits();
        console.log('[ServerAdCounters] 🔄 Счетчики сброшены');
        return true;
      } else {
        throw new Error(result.error || 'Ошибка сброса счетчиков');
      }
      
    } catch (error) {
      console.error('[ServerAdCounters] ❌ Ошибка сброса счетчиков:', error);
      return false;
    }
  }
}

// Создаем глобальный экземпляр только если его еще нет
if (!window.serverAdCountersManager) {
  window.serverAdCountersManager = new ServerAdCountersManager();
}

// Функция для тестирования серверной системы счетчиков
window.testServerAdCounters = function() {
  console.log('[ServerAdCounters] 🧪 ТЕСТ СЕРВЕРНОЙ СИСТЕМЫ СЧЕТЧИКОВ');

  if (!window.serverAdCountersManager) {
    console.error('[ServerAdCounters] ❌ serverAdCountersManager не найден');
    return;
  }

  console.log('[ServerAdCounters] 📊 Инициализирован:', window.serverAdCountersManager.isInitialized);
  console.log('[ServerAdCounters] 👤 User ID:', window.serverAdCountersManager.userId);
  console.log('[ServerAdCounters] 📊 Маппинги:', window.serverAdCountersManager.adTypeMapping);
  console.log('[ServerAdCounters] 📊 Лимиты пользователя:', window.serverAdCountersManager.cachedLimits);

  // Проверяем элементы в DOM
  Object.entries(window.serverAdCountersManager.adTypeMapping).forEach(([adType, counterId]) => {
    const element = document.getElementById(counterId);
    console.log(`[ServerAdCounters] 🔍 ${adType} -> ${counterId}:`, element ? '✅ найден' : '❌ не найден');
    if (element) {
      console.log(`[ServerAdCounters] 📝 Текущий текст: "${element.textContent}"`);
    }
  });

  // Принудительно обновляем счетчики
  if (window.serverAdCountersManager.isInitialized) {
    window.serverAdCountersManager.updateAllCounters();
  } else {
    console.warn('[ServerAdCounters] ⚠️ Система не инициализирована');
  }
};

// Функция для принудительного обновления лимитов с сервера
window.refreshServerLimits = async function() {
  console.log('[ServerAdCounters] 🔄 Принудительное обновление лимитов с сервера');

  if (!window.serverAdCountersManager) {
    console.error('[ServerAdCounters] ❌ serverAdCountersManager не найден');
    return;
  }

  try {
    await window.serverAdCountersManager.loadUserLimits();
    window.serverAdCountersManager.updateAllCounters();
    console.log('[ServerAdCounters] ✅ Лимиты обновлены');
  } catch (error) {
    console.error('[ServerAdCounters] ❌ Ошибка обновления лимитов:', error);
  }
};

// Функция для симуляции просмотра рекламы на сервере
window.simulateServerAdView = async function(adType = 'native_banner') {
  console.log(`[ServerAdCounters] 🎬 Симуляция просмотра рекламы на сервере: ${adType}`);

  if (!window.serverAdCountersManager) {
    console.error('[ServerAdCounters] ❌ serverAdCountersManager не найден');
    return;
  }

  try {
    const result = await window.serverAdCountersManager.incrementCounter(adType);
    if (result) {
      console.log(`[ServerAdCounters] ✅ Счетчик ${adType} увеличен на сервере`);
    } else {
      console.error(`[ServerAdCounters] ❌ Ошибка увеличения счетчика ${adType}`);
    }
  } catch (error) {
    console.error(`[ServerAdCounters] ❌ Ошибка симуляции просмотра:`, error);
  }
};

// Функция для проверки системы сброса лимитов
window.checkResetSystem = function() {
  console.log('[ServerAdCounters] 🕛 ПРОВЕРКА СИСТЕМЫ СБРОСА ЛИМИТОВ');

  // Получаем текущее время UTC
  const now = new Date();
  const currentUTCDate = now.getUTCFullYear() + '-' +
                        String(now.getUTCMonth() + 1).padStart(2, '0') + '-' +
                        String(now.getUTCDate()).padStart(2, '0');
  const currentUTCTime = String(now.getUTCHours()).padStart(2, '0') + ':' +
                        String(now.getUTCMinutes()).padStart(2, '0') + ':' +
                        String(now.getUTCSeconds()).padStart(2, '0');

  console.log(`[ServerAdCounters] 📅 Текущая дата UTC: ${currentUTCDate}`);
  console.log(`[ServerAdCounters] 🕐 Текущее время UTC: ${currentUTCTime}`);

  // Вычисляем время до следующего сброса
  const nextDay = new Date(now);
  nextDay.setUTCDate(now.getUTCDate() + 1);
  nextDay.setUTCHours(0, 0, 0, 0);

  const msUntilReset = nextDay.getTime() - now.getTime();
  const hoursUntilReset = Math.floor(msUntilReset / (1000 * 60 * 60));
  const minutesUntilReset = Math.floor((msUntilReset % (1000 * 60 * 60)) / (1000 * 60));

  console.log(`[ServerAdCounters] ⏰ До сброса лимитов: ${hoursUntilReset}ч ${minutesUntilReset}мин`);
  console.log(`[ServerAdCounters] 🔄 Следующий сброс: ${nextDay.toISOString()}`);

  // Проверяем данные из файла лимитов
  if (window.serverAdCountersManager && window.serverAdCountersManager.cachedLimits) {
    console.log('[ServerAdCounters] 📊 Текущие лимиты из кэша:', window.serverAdCountersManager.cachedLimits);
  }
};

console.log('[ServerAdCounters] 📊 Модуль загружен');
console.log('[ServerAdCounters] 🧪 Для тестирования выполните: testServerAdCounters()');
console.log('[ServerAdCounters] 🔄 Для обновления лимитов: refreshServerLimits()');
console.log('[ServerAdCounters] 🎬 Для симуляции просмотра: simulateServerAdView("native_banner")');
console.log('[ServerAdCounters] 🕛 Для проверки сброса лимитов: checkResetSystem()');
