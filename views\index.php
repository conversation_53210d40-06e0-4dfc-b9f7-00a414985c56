<!DOCTYPE html>
<html lang="ru" id="app-html">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, shrink-to-fit=no, user-scalable=no, viewport-fit=cover">

    <!-- ОТКЛЮЧЕНИЕ КЭША ДЛЯ МИНИ-АППА -->
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <meta name="cache-control" content="no-cache">
    <meta name="expires" content="0">
    <meta name="pragma" content="no-cache">

    <title>UniQPaid - Crypto Wallet</title>
    
    <!-- Copy CSS from example -->
    <link rel="stylesheet" href="css/cyberpunk-styles.css?v=<?= time() ?>">
    <link rel="stylesheet" href="css/dynamic-design.css?v=<?= time() ?>">
    <link rel="stylesheet" href="css/fixes.css?v=<?= time() ?>">

    <!-- Telegram WebApp Script -->
    <script src="https://telegram.org/js/telegram-web-app.js?56"></script>
    
    <!-- RichAds Script -->
    <script src="https://richinfo.co/richpartners/telegram/js/tg-ob.js"></script>
</head>
<body>

    <!-- Clean Background -->
    <div class="cyberpunk-coins-bg"></div>

    <!-- === КРУПНЫЕ ПЛАВАЮЩИЕ КРИПТОИКОНКИ === -->
    <div class="crypto-background">
        <!-- Bitcoin иконка -->
        <div class="floating-crypto crypto-btc-1">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <circle cx="12" cy="12" r="10" fill="none" stroke="#ff6b35"/>
                <path d="M8 12h8M8 8h6a2 2 0 0 1 0 4M8 16h6a2 2 0 0 0 0-4" fill="none" stroke="#ff6b35"/>
                <path d="M12 6v2M12 16v2" stroke="#ff6b35"/>
            </svg>
        </div>

        <!-- Ethereum иконка -->
        <div class="floating-crypto crypto-eth-1">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2L6 12.5l6 3.5 6-3.5L12 2z" opacity="0.8" fill="#ff6b35"/>
                <path d="M12 16L6 12.5l6 9.5 6-9.5L12 16z" opacity="0.6" fill="#ff6b35"/>
                <path d="M12 2v6l5.5 2.5L12 2z" opacity="0.4" fill="#ff6b35"/>
                <path d="M12 8v8l5.5-3.5L12 8z" opacity="0.3" fill="#ff6b35"/>
            </svg>
        </div>

        <!-- USDT иконка -->
        <div class="floating-crypto crypto-usdt-1">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <circle cx="12" cy="12" r="10" fill="none" stroke="#ff6b35"/>
                <path d="M8 9h8v2H8z" fill="#ff6b35"/>
                <path d="M10 11v4h4v-4" fill="none" stroke="#ff6b35"/>
                <path d="M12 7v2M12 15v2" stroke="#ff6b35"/>
            </svg>
        </div>

        <!-- TON иконка -->
        <div class="floating-crypto crypto-ton-1">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <circle cx="12" cy="12" r="10" fill="none" stroke="#ff6b35"/>
                <path d="M8 8l8 8M16 8l-8 8" stroke="#ff6b35"/>
                <circle cx="12" cy="12" r="3" fill="rgba(255, 107, 53, 0.3)"/>
                <path d="M12 6v2M12 16v2" stroke="#ff6b35"/>
            </svg>
        </div>

        <!-- Доллар иконка -->
        <div class="floating-crypto crypto-dollar-1">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <circle cx="12" cy="12" r="10" fill="none" stroke="#ff6b35"/>
                <path d="M10 2v20M14 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6" fill="none" stroke="#ff6b35"/>
            </svg>
        </div>
    </div>

    <!-- Removed Glitch Lines for Clean Design -->
    <div class="glitch-line"></div>
    <div class="glitch-line"></div>
    <div class="glitch-line"></div>

    <!-- Контейнер приложения -->
    <div class="app-container">

        <!-- Шапка -->
        <header class="app-header">
            <div class="user-info">
                <div class="user-avatar">
                    <svg class="user-avatar-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                        <circle cx="12" cy="7" r="4"/>
                    </svg>
                </div>
                <div class="user-name" id="user-name">Загрузка...</div>
            </div>
            <div class="balance-info clickable-balance" id="header-balance-info" title="Баланс">
                <span class="balance-amount" id="balance-amount">0</span>
                <span class="balance-currency">монет</span>
            </div>
        </header>

        <!-- Основной контент (Задания) -->
        <main class="app-section active-section" id="main-content">
            <div id="status-message" class="status-message">Инициализация приложения...</div>
            <div class="tasks-header">
                <svg class="tasks-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M9 12l2 2 4-4"/>
                    <path d="M21 12c0 4.97-4.03 9-9 9s-9-4.03-9-9 4.03-9 9-9 9 4.03 9 9z"/>
                </svg>
                <h2>Tasks</h2>
            </div>
            
            <!-- Ad Buttons -->
            <button id="openLinkButton" class="action-button purple-button">
                <svg class="button-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"/>
                    <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"/>
                </svg>
                <div class="button-content">
                    <span class="button-text">Открыть ссылку</span>
                    <span class="ad-counter" id="native-banner-counter">0/10</span>
                </div>
                <div class="reward-badge">+10</div>
            </button>
            
            <button id="watchVideoButton" class="action-button blue-button">
                <svg class="button-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polygon points="23 7 16 12 23 17 23 7"/>
                    <rect x="1" y="5" width="15" height="14" rx="2" ry="2"/>
                </svg>
                <div class="button-content">
                    <span class="button-text">Смотреть видео</span>
                    <span class="ad-counter" id="rewarded-video-counter">0/20</span>
                </div>
                <div class="reward-badge">+2</div>
            </button>
            
            <button id="openAdButton" class="action-button orange-button">
                <svg class="button-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <rect x="2" y="3" width="20" height="14" rx="2" ry="2"/>
                    <line x1="8" y1="21" x2="16" y2="21"/>
                    <line x1="12" y1="17" x2="12" y2="21"/>
                </svg>
                <div class="button-content">
                    <span class="button-text">Кликнуть по баннеру</span>
                    <span class="ad-counter" id="interstitial-counter">0/10</span>
                </div>
                <div class="reward-badge">+8</div>
            </button>
        </main>

        <!-- Секция "Заработок" (Вывод средств) -->
        <section class="app-section earn-section page-hidden" id="earn-section">
            <h2 style="display: flex; align-items: center; justify-content: center; gap: 12px;">
                <svg style="width: 28px; height: 28px; color: #ff6b35;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                    <rect x="3" y="6" width="18" height="12" rx="2"/>
                    <path d="M3 10h18"/>
                    <circle cx="17" cy="14" r="1"/>
                    <path d="M7 6V4a2 2 0 0 1 2-2h6a2 2 0 0 1 2 2v2"/>
                </svg>
                <span>Заработок</span>
            </h2>
            
            <div class="earn-block">
                <h3>Ваш баланс</h3>
                <div class="current-balance-display">
                    <span class="balance-amount" id="earn-balance-amount">0</span>
                    <span class="balance-currency">монет</span>
                </div>
                
                <div class="withdrawal-section">
                    <h4>Вывод средств</h4>
                    <p>Минимальная сумма для вывода: 100 монет</p>
                    
                    <div class="withdrawal-form" id="withdrawal-form">
                        <div class="form-group">
                            <label for="withdrawal-amount">Сумма (монеты):</label>
                            <input type="number" id="withdrawal-amount" min="100" placeholder="100">
                        </div>
                        
                        <div class="form-group">
                            <label for="withdrawal-currency">Валюта:</label>
                            <select id="withdrawal-currency">
                                <option value="USDT">USDT</option>
                                <option value="BTC">Bitcoin</option>
                                <option value="ETH">Ethereum</option>
                                <option value="TON">Toncoin</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="withdrawal-address">Адрес кошелька:</label>
                            <input type="text" id="withdrawal-address" placeholder="Введите адрес">
                        </div>
                        
                        <button id="submit-withdrawal" class="action-button green-button">
                            Запросить вывод
                        </button>
                    </div>
                    
                    <div class="withdrawal-history" id="withdrawal-history">
                        <h4>История выводов</h4>
                        <div id="withdrawal-list">
                            <!-- Withdrawal history will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Секция "Рефералы" -->
        <section class="app-section referral-section page-hidden" id="referral-section">
            <h2>Рефералы</h2>
            <div class="referral-block">
                <h3>Ваша реферальная ссылка</h3>
                <div class="referral-link-container">
                    <input type="text" id="referral-link" readonly>
                    <button id="copy-referral-link" class="copy-button">Копировать</button>
                </div>
                
                <div class="referral-stats">
                    <div class="stat-item">
                        <span class="stat-label">Рефералов:</span>
                        <span class="stat-value" id="referral-count">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Заработано:</span>
                        <span class="stat-value" id="referral-earnings">0</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- Навигация -->
        <nav class="app-navigation">
            <button class="nav-button active" data-section="main-content">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M9 12l2 2 4-4"/>
                    <path d="M21 12c0 4.97-4.03 9-9 9s-9-4.03-9-9 4.03-9 9-9c1.66 0 3.22.45 4.56 1.23"/>
                </svg>
                <span>Задания</span>
            </button>
            <button class="nav-button" data-section="earn-section">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <rect x="3" y="6" width="18" height="12" rx="2" ry="2"/>
                    <path d="M3 10h18"/>
                </svg>
                <span>Заработок</span>
            </button>
            <button class="nav-button" data-section="referral-section">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/>
                    <circle cx="9" cy="7" r="4"/>
                    <path d="M22 21v-2a4 4 0 0 0-3-3.87"/>
                    <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                </svg>
                <span>Рефералы</span>
            </button>
        </nav>
    </div>

    <!-- JavaScript -->
    <script src="js/app.js?v=<?= time() ?>"></script>
</body>
</html>
