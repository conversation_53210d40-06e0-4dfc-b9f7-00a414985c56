<?php
declare(strict_types=1);

/**
 * FileDatabase.php
 * Temporary file-based database implementation for testing
 * 
 * This class provides a simple file-based storage system that mimics
 * the Database interface until SQLite is properly configured.
 */
class FileDatabase
{
    private static ?FileDatabase $instance = null;
    private string $dataDir;

    /**
     * Private constructor
     */
    private function __construct()
    {
        $this->dataDir = APP_ROOT . '/database/data';
        if (!is_dir($this->dataDir)) {
            mkdir($this->dataDir, 0755, true);
        }
    }

    /**
     * Get singleton instance
     * 
     * @return FileDatabase
     */
    public static function getInstance(): FileDatabase
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Get file path for table
     * 
     * @param string $table
     * @return string
     */
    private function getTableFile(string $table): string
    {
        return $this->dataDir . '/' . $table . '.json';
    }

    /**
     * Load data from table file
     * 
     * @param string $table
     * @return array<mixed>
     */
    private function loadTable(string $table): array
    {
        $file = $this->getTableFile($table);
        if (!file_exists($file)) {
            return [];
        }
        
        $content = file_get_contents($file);
        if ($content === false) {
            return [];
        }
        
        $data = json_decode($content, true);
        return is_array($data) ? $data : [];
    }

    /**
     * Save data to table file
     * 
     * @param string $table
     * @param array<mixed> $data
     * @return bool
     */
    private function saveTable(string $table, array $data): bool
    {
        $file = $this->getTableFile($table);
        $content = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        
        return file_put_contents($file, $content, LOCK_EX) !== false;
    }

    /**
     * Insert record into table
     * 
     * @param string $table
     * @param array<string, mixed> $data
     * @return int|false Insert ID or false on failure
     */
    public function insert(string $table, array $data): int|false
    {
        $tableData = $this->loadTable($table);
        
        // Generate ID
        $maxId = 0;
        foreach ($tableData as $record) {
            if (isset($record['id']) && $record['id'] > $maxId) {
                $maxId = $record['id'];
            }
        }
        
        $newId = $maxId + 1;
        $data['id'] = $newId;
        $data['created_at'] = $data['created_at'] ?? time();
        $data['updated_at'] = time();
        
        $tableData[] = $data;
        
        if ($this->saveTable($table, $tableData)) {
            return $newId;
        }
        
        return false;
    }

    /**
     * Update record in table
     * 
     * @param string $table
     * @param array<string, mixed> $data
     * @param array<string, mixed> $where
     * @return bool
     */
    public function update(string $table, array $data, array $where): bool
    {
        $tableData = $this->loadTable($table);
        $updated = false;
        
        foreach ($tableData as &$record) {
            $match = true;
            foreach ($where as $key => $value) {
                if (!isset($record[$key]) || $record[$key] != $value) {
                    $match = false;
                    break;
                }
            }
            
            if ($match) {
                foreach ($data as $key => $value) {
                    $record[$key] = $value;
                }
                $record['updated_at'] = time();
                $updated = true;
            }
        }
        
        if ($updated) {
            return $this->saveTable($table, $tableData);
        }
        
        return false;
    }

    /**
     * Select records from table
     * 
     * @param string $table
     * @param array<string, mixed> $where
     * @param int|null $limit
     * @return array<array<string, mixed>>
     */
    public function select(string $table, array $where = [], ?int $limit = null): array
    {
        $tableData = $this->loadTable($table);
        $results = [];
        
        foreach ($tableData as $record) {
            $match = true;
            foreach ($where as $key => $value) {
                if (!isset($record[$key]) || $record[$key] != $value) {
                    $match = false;
                    break;
                }
            }
            
            if ($match) {
                $results[] = $record;
                if ($limit && count($results) >= $limit) {
                    break;
                }
            }
        }
        
        return $results;
    }

    /**
     * Find single record
     * 
     * @param string $table
     * @param array<string, mixed> $where
     * @return array<string, mixed>|null
     */
    public function findOne(string $table, array $where): ?array
    {
        $results = $this->select($table, $where, 1);
        return $results[0] ?? null;
    }

    /**
     * Delete records from table
     * 
     * @param string $table
     * @param array<string, mixed> $where
     * @return bool
     */
    public function delete(string $table, array $where): bool
    {
        $tableData = $this->loadTable($table);
        $newData = [];
        $deleted = false;
        
        foreach ($tableData as $record) {
            $match = true;
            foreach ($where as $key => $value) {
                if (!isset($record[$key]) || $record[$key] != $value) {
                    $match = false;
                    break;
                }
            }
            
            if (!$match) {
                $newData[] = $record;
            } else {
                $deleted = true;
            }
        }
        
        if ($deleted) {
            return $this->saveTable($table, $newData);
        }
        
        return false;
    }

    /**
     * Count records in table
     * 
     * @param string $table
     * @param array<string, mixed> $where
     * @return int
     */
    public function count(string $table, array $where = []): int
    {
        return count($this->select($table, $where));
    }

    /**
     * Initialize default settings
     */
    public function initializeSettings(): void
    {
        $settings = $this->loadTable('settings');
        
        if (empty($settings)) {
            $defaultSettings = [
                ['key' => 'conversion_rate', 'value' => (string)CONVERSION_RATE, 'description' => 'Conversion rate from coins to USD'],
                ['key' => 'min_withdrawal', 'value' => (string)MIN_WITHDRAWAL_AMOUNT, 'description' => 'Minimum withdrawal amount in coins'],
                ['key' => 'referral_bonus', 'value' => (string)REFERRAL_BONUS_PERCENT, 'description' => 'Referral bonus percentage'],
                ['key' => 'ad_reward_banner', 'value' => (string)AD_REWARD_NATIVE_BANNER, 'description' => 'Reward for banner ads'],
                ['key' => 'ad_reward_interstitial', 'value' => (string)AD_REWARD_INTERSTITIAL, 'description' => 'Reward for interstitial ads'],
                ['key' => 'ad_reward_video', 'value' => (string)AD_REWARD_REWARDED_VIDEO, 'description' => 'Reward for video ads'],
            ];

            foreach ($defaultSettings as $setting) {
                $this->insert('settings', $setting);
            }
        }
    }

    /**
     * Get all tables (list files in data directory)
     * 
     * @return array<string>
     */
    public function getTables(): array
    {
        $tables = [];
        $files = glob($this->dataDir . '/*.json');
        
        foreach ($files as $file) {
            $tables[] = basename($file, '.json');
        }
        
        return $tables;
    }
}
