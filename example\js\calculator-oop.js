/**
 * === calculator-oop.js ===
 * Класс для управления калькулятором вывода криптовалют
 * Идеальная ООП система с валидацией и кешированием
 */

/**
 * Класс для управления калькулятором криптовалют
 */
class CalculatorManager {
  constructor() {
    this.isInitialized = false;
    this.currencyCache = new Map();
    this.lastCalculation = null;
    this.calculationTimeout = null;
    this.debounceDelay = 500; // мс
    this.currencyData = new Map();
    
    // Кеш элементов DOM
    this.elements = {
      amountInput: null,
      balanceEl: null,
      currencySelect: null,
      cryptoAmountEl: null,
      usdAmountEl: null,
      errorEl: null,
      submitButton: null
    };
  }

  /**
   * Инициализация калькулятора
   */
  init() {
    if (this.isInitialized) {
      AppUtils.warn('CalculatorManager', 'Калькулятор уже инициализирован');
      return;
    }

    AppUtils.log('CalculatorManager', 'Инициализация калькулятора...');
    
    // Кешируем элементы DOM
    this.cacheElements();
    
    // Настраиваем обработчики событий
    this.setupEventListeners();
    
    // Загружаем доступные валюты
    this.loadAvailableCurrencies();
    
    // Устанавливаем TON как валюту по умолчанию
    this.setDefaultCurrency();
    
    this.isInitialized = true;
    AppUtils.log('CalculatorManager', 'Калькулятор инициализирован');
  }

  /**
   * Кеширование элементов DOM
   */
  cacheElements() {
    const elementIds = {
      amountInput: 'calc-amount',
      balanceEl: 'calc-balance',
      currencySelect: 'calc-currency',
      cryptoAmountEl: 'calc-crypto-amount',
      usdAmountEl: 'calc-usd-amount',
      errorEl: 'calc-error',
      submitButton: 'calc-submit'
    };

    Object.entries(elementIds).forEach(([key, id]) => {
      if (domManager.initialized) {
        this.elements[key] = domManager.get(id);
      } else {
        this.elements[key] = document.getElementById(id);
      }
      
      if (!this.elements[key]) {
        AppUtils.warn('CalculatorManager', `Элемент "${id}" не найден`);
      }
    });
  }

  /**
   * Настройка обработчиков событий
   */
  setupEventListeners() {
    // Обработчик ввода суммы с debounce
    if (this.elements.amountInput) {
      this.elements.amountInput.addEventListener('input', () => {
        this.handleAmountInputDebounced();
      });
    }
    
    // Обработчик изменения валюты
    if (this.elements.currencySelect) {
      this.elements.currencySelect.addEventListener('change', () => {
        this.handleCurrencyChange();
      });
    }
    
    // Обработчик отправки формы
    if (this.elements.submitButton) {
      this.elements.submitButton.addEventListener('click', () => {
        this.handleSubmit();
      });
    }
  }

  /**
   * Обработчик ввода суммы с debounce
   */
  handleAmountInputDebounced() {
    // Отменяем предыдущий таймер
    if (this.calculationTimeout) {
      clearTimeout(this.calculationTimeout);
    }
    
    // Устанавливаем новый таймер
    this.calculationTimeout = setTimeout(() => {
      this.handleAmountInput();
    }, this.debounceDelay);
  }

  /**
   * Обработчик ввода суммы
   */
  handleAmountInput() {
    const amount = this.getAmountValue();
    this.updateDisplay(amount);
    this.updateBalanceCheck(amount);
  }

  /**
   * Обработчик изменения валюты
   */
  handleCurrencyChange() {
    const amount = this.getAmountValue();
    if (amount > 0) {
      this.updateDisplay(amount);
    }
  }

  /**
   * Обработчик отправки формы калькулятора
   */
  handleSubmit() {
    const amount = this.getAmountValue();
    const currency = this.getCurrencyValue();
    
    // Валидация суммы
    if (amount <= 0) {
      this.showError('Введите корректную сумму');
      return;
    }
    
    // Проверка баланса
    if (!balanceManager.checkSufficiency(amount)) {
      const available = balanceManager.getCurrentBalance();
      const coinsText = window.appLocalization ? window.appLocalization.get('currency.coins') : 'монет';
      this.showError(`Недостаточно средств. Доступно: ${available} ${coinsText}`);
      return;
    }
    
    // Сохраняем данные калькулятора
    const calcData = calculatorData.get();
    calculatorData.update({
      amount: amount,
      currency: currency,
      isValid: true,
      lastCalculation: new Date().toISOString()
    });
    
    const coinsText2 = window.appLocalization ? window.appLocalization.get('currency.coins') : 'монет';
    AppUtils.log('CalculatorManager', `Данные калькулятора сохранены: ${amount} ${coinsText2}, валюта ${currency}`);
    
    // Переходим на страницу вывода
    if (navigationManager.isInitialized) {
      navigationManager.showEarnSection();
    } else if (window.showEarnSection) {
      window.showEarnSection();
    }
  }

  /**
   * Обновить отображение калькулятора
   * @param {number} amount - Сумма в монетах
   */
  updateDisplay(amount) {
    if (amount <= 0) {
      this.clearDisplay();
      return;
    }
    
    const currency = this.getCurrencyValue();
    this.calculateAndDisplayAmounts(amount, currency);
  }

  /**
   * Рассчитать и отобразить суммы
   * @param {number} amount - Сумма в монетах
   * @param {string} currency - Валюта
   */
  async calculateAndDisplayAmounts(amount, currency) {
    try {
      // Проверяем кеш
      const cacheKey = `${amount}_${currency}`;
      if (this.currencyCache.has(cacheKey)) {
        const cachedData = this.currencyCache.get(cacheKey);
        this.displayCalculationResults(cachedData);
        return;
      }

      const response = await fetch(`${AppConfig.API_BASE_URL}/calculateWithdrawalAmount.php`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: amount,
          currency: currency
        })
      });
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      
      if (data.success) {
        // Кешируем результат
        this.currencyCache.set(cacheKey, data);
        
        // Отображаем результат
        this.displayCalculationResults(data);
        
        // Сохраняем данные для синхронизации
        calculatorData.update({
          amount: amount,
          currency: currency,
          cryptoAmount: data.crypto_amount,
          usdAmount: data.usd_amount,
          isValid: true
        });
        
        this.hideError();
      } else {
        this.showError(data.message || 'Ошибка расчета');
      }
    } catch (error) {
      AppUtils.error('CalculatorManager', 'Ошибка расчета:', error);
      this.showError('Ошибка соединения с сервером');
    }
  }

  /**
   * Отобразить результаты расчета
   * @param {Object} data - Данные расчета
   */
  displayCalculationResults(data) {
    if (this.elements.usdAmountEl) {
      this.elements.usdAmountEl.textContent = `$${data.usd_amount.toFixed(2)}`;
    }
    
    if (this.elements.cryptoAmountEl) {
      const currency = this.getCurrencyValue();
      this.elements.cryptoAmountEl.textContent = `${data.crypto_amount.toFixed(8)} ${currency.toUpperCase()}`;
    }
  }

  /**
   * Проверить достаточность баланса
   * @param {number} amount - Сумма для проверки
   */
  updateBalanceCheck(amount) {
    const currentBalance = balanceManager.getCurrentBalance();
    
    if (amount > currentBalance) {
      const coinsText = window.appLocalization ?
        window.appLocalization.get('currency.coins') :
        'монет';
      this.showError(`Недостаточно средств. Доступно: ${currentBalance} ${coinsText}`);
      this.setSubmitButtonState(false);
    } else {
      this.hideError();
      this.setSubmitButtonState(true);
    }
  }

  /**
   * Очистить отображение калькулятора
   */
  clearDisplay() {
    if (this.elements.usdAmountEl) {
      this.elements.usdAmountEl.textContent = '$0.00';
    }
    
    if (this.elements.cryptoAmountEl) {
      this.elements.cryptoAmountEl.textContent = '0.00000000';
    }
    
    this.hideError();
    
    // Очищаем данные калькулятора
    calculatorData.update({
      amount: 0,
      cryptoAmount: 0,
      usdAmount: 0,
      isValid: false
    });
  }

  /**
   * Показать ошибку калькулятора
   * @param {string} message - Сообщение об ошибке
   */
  showError(message) {
    if (this.elements.errorEl) {
      this.elements.errorEl.textContent = message;
      this.elements.errorEl.style.display = 'block';
    }
  }

  /**
   * Скрыть ошибку калькулятора
   */
  hideError() {
    if (this.elements.errorEl) {
      this.elements.errorEl.style.display = 'none';
    }
  }

  /**
   * Установить состояние кнопки отправки
   * @param {boolean} enabled - Включена ли кнопка
   */
  setSubmitButtonState(enabled) {
    if (this.elements.submitButton) {
      this.elements.submitButton.disabled = !enabled;
    }
  }

  /**
   * Загрузить доступные валюты
   */
  async loadAvailableCurrencies() {
    // Сразу загружаем fallback валюты, чтобы интерфейс работал
    this.loadFallbackCurrencies();

    try {
      // Получаем initData для API
      const initData = window.Telegram?.WebApp?.initData || 'test_data';

      // Пытаемся загрузить с сервера
      const response = await fetch(`${AppConfig.API_BASE_URL}/getAvailableCurrencies.php`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          initData: initData
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.currencies && Array.isArray(data.currencies) && this.elements.currencySelect) {
        // Очищаем текущие опции
        this.elements.currencySelect.innerHTML = '';

        // Добавляем новые опции с правильным форматом
        data.currencies.forEach(currencyCode => {
          const option = document.createElement('option');
          option.value = currencyCode.toLowerCase();
          option.textContent = `${this.getCurrencyName(currencyCode)} (${currencyCode.toUpperCase()})`;
          this.elements.currencySelect.appendChild(option);

          // Сохраняем данные валюты
          this.currencyData.set(currencyCode.toLowerCase(), {
            code: currencyCode.toLowerCase(),
            name: this.getCurrencyName(currencyCode)
          });
        });

        // Устанавливаем TON по умолчанию
        this.setDefaultCurrency();

        AppUtils.log('CalculatorManager', `Загружено ${data.currencies.length} валют с сервера`);
      }
    } catch (error) {
      AppUtils.warn('CalculatorManager', 'Ошибка загрузки валют с сервера, используем fallback:', error.message);
      // Fallback уже загружен выше
    }
  }

  /**
   * Загрузить базовые валюты (fallback)
   */
  loadFallbackCurrencies() {
    if (!this.elements.currencySelect) return;

    const fallbackCurrencies = [
      { code: 'ton', name: 'Toncoin' },
      { code: 'btc', name: 'Bitcoin' },
      { code: 'eth', name: 'Ethereum' },
      { code: 'usdt', name: 'Tether' },
      { code: 'trx', name: 'TRON' },
      { code: 'ltc', name: 'Litecoin' }
    ];

    this.elements.currencySelect.innerHTML = '';

    fallbackCurrencies.forEach(currency => {
      const option = document.createElement('option');
      option.value = currency.code;
      option.textContent = `${currency.name} (${currency.code.toUpperCase()})`;
      this.elements.currencySelect.appendChild(option);

      // Сохраняем данные валюты
      this.currencyData.set(currency.code, currency);
    });

    AppUtils.log('CalculatorManager', `Загружены fallback валюты: ${fallbackCurrencies.length}`);
  }

  /**
   * Получить название валюты по коду
   * @param {string} code - Код валюты
   * @returns {string} Название валюты
   */
  getCurrencyName(code) {
    const currencyNames = {
      'TON': 'Toncoin',
      'BTC': 'Bitcoin',
      'ETH': 'Ethereum',
      'USDT': 'Tether',
      'TRX': 'TRON',
      'LTC': 'Litecoin',
      'DOGE': 'Dogecoin',
      'XRP': 'Ripple',
      'BNB': 'Binance Coin',
      'USDC': 'USD Coin'
    };

    return currencyNames[code.toUpperCase()] || code.toUpperCase();
  }

  /**
   * Установить валюту по умолчанию
   */
  setDefaultCurrency() {
    if (this.elements.currencySelect) {
      this.elements.currencySelect.value = 'ton';
    }
  }

  /**
   * Получить значение суммы
   * @returns {number} Сумма в монетах
   */
  getAmountValue() {
    if (this.elements.amountInput) {
      return parseInt(this.elements.amountInput.value) || 0;
    }
    return 0;
  }

  /**
   * Получить значение валюты
   * @returns {string} Код валюты
   */
  getCurrencyValue() {
    if (this.elements.currencySelect) {
      return this.elements.currencySelect.value || 'ton';
    }
    return 'ton';
  }

  /**
   * Установить значение суммы
   * @param {number} amount - Сумма в монетах
   */
  setAmountValue(amount) {
    if (this.elements.amountInput) {
      this.elements.amountInput.value = amount;
      this.handleAmountInput();
    }
  }

  /**
   * Установить валюту
   * @param {string} currency - Код валюты
   */
  setCurrencyValue(currency) {
    if (this.elements.currencySelect) {
      this.elements.currencySelect.value = currency;
      this.handleCurrencyChange();
    }
  }

  /**
   * Получить данные валюты
   * @param {string} currency - Код валюты
   * @returns {Object|null} Данные валюты
   */
  getCurrencyData(currency) {
    return this.currencyData.get(currency) || null;
  }

  /**
   * Очистить кеш расчетов
   */
  clearCache() {
    this.currencyCache.clear();
    AppUtils.log('CalculatorManager', 'Кеш расчетов очищен');
  }

  /**
   * Получить статистику калькулятора
   * @returns {Object} Статистика
   */
  getStats() {
    return {
      isInitialized: this.isInitialized,
      cacheSize: this.currencyCache.size,
      currenciesLoaded: this.currencyData.size,
      lastCalculation: this.lastCalculation
    };
  }
}

// Создаем глобальный экземпляр
const calculatorManager = new CalculatorManager();

// Экспорт в глобальную область видимости
window.CalculatorManager = CalculatorManager;
window.calculatorManager = calculatorManager;

// Функции для обратной совместимости
window.initCalculator = () => calculatorManager.init();
window.updateCalculatorDisplay = (amount) => calculatorManager.updateDisplay(amount);
window.updateBalanceCheck = (amount) => calculatorManager.updateBalanceCheck(amount);
window.clearCalculatorDisplay = () => calculatorManager.clearDisplay();
window.loadAvailableCurrencies = () => calculatorManager.loadAvailableCurrencies();

console.log('📦 [CalculatorManager] Система калькулятора загружена');
