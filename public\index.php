<?php
declare(strict_types=1);

/**
 * Entry point for the UniQPaid Telegram Mini App
 * This file serves as the main router and initializes the MVC structure
 */

// Quick SQLite test
if (isset($_GET['test_sqlite'])) {
    header('Content-Type: text/plain');
    echo "Testing SQLite support...\n";

    if (!class_exists('PDO')) {
        echo "❌ PDO class not available\n";
        exit;
    }

    $drivers = PDO::getAvailableDrivers();
    echo "Available PDO drivers: " . implode(', ', $drivers) . "\n";

    if (in_array('sqlite', $drivers)) {
        echo "✅ SQLite driver is available\n";

        try {
            $testDb = dirname(__DIR__) . '/database/test.sqlite';
            $pdo = new PDO('sqlite:' . $testDb);
            echo "✅ SQLite connection successful\n";

            // Test translations import
            define('APP_ROOT', dirname(__DIR__));
            require_once APP_ROOT . '/app/config.php';
            require_once APP_ROOT . '/app/Models/Translation.php';

            $testTranslations = [
                'app.name' => 'UniQPaid',
                'app.loading' => 'Loading...',
                'tasks.title' => 'Tasks'
            ];

            $count = \Models\Translation::importTranslations($testTranslations, 'en');
            echo "✅ Translation import test: {$count} translations imported\n";

        } catch (Exception $e) {
            echo "❌ SQLite test failed: " . $e->getMessage() . "\n";
        }
    } else {
        echo "❌ SQLite driver not available\n";
    }
    exit;
}

// Include the main controller
require_once '../app/Controllers/HomeController.php';

// Initialize the controller and display the home page
$controller = new HomeController();
$controller->index();
