<?php
declare(strict_types=1);

/**
 * public/index.php
 * Main entry point for the UniQPaid Telegram Mini App
 * 
 * This file serves as the single entry point for all requests.
 * It initializes the application, handles routing, and renders the appropriate response.
 */

// Enable error reporting for development
ini_set('display_errors', '1');
error_reporting(E_ALL);

// Set timezone
date_default_timezone_set('UTC');

// Define application root directory
define('APP_ROOT', dirname(__DIR__));

// Include autoloader
require_once APP_ROOT . '/app/autoload.php';

// Include configuration
require_once APP_ROOT . '/app/config.php';

try {
    // Initialize database connection
    $database = Database::getInstance();
    
    // Initialize router
    $router = new Router();
    
    // Define API routes
    $router->addRoute('POST', '/api/user/data', 'ApiController@getUserData');
    $router->addRoute('POST', '/api/user/register', 'ApiController@registerUser');
    $router->addRoute('POST', '/api/ad/view', 'ApiController@recordAdView');
    $router->addRoute('POST', '/api/withdrawal/request', 'ApiController@requestWithdrawal');
    $router->addRoute('POST', '/api/withdrawal/history', 'ApiController@getWithdrawalHistory');
    $router->addRoute('POST', '/api/referral/register', 'ApiController@registerReferral');
    $router->addRoute('POST', '/api/referral/stats', 'ApiController@getReferralStats');
    $router->addRoute('GET', '/api/settings', 'ApiController@getAppSettings');
    $router->addRoute('GET', '/api/currencies', 'ApiController@getAvailableCurrencies');
    
    // Check if this is an API request
    $requestUri = $_SERVER['REQUEST_URI'] ?? '/';
    $requestMethod = $_SERVER['REQUEST_METHOD'] ?? 'GET';
    
    if (strpos($requestUri, '/api/') === 0) {
        // Handle API request
        header('Content-Type: application/json');
        $router->handleRequest($requestMethod, $requestUri);
    } else {
        // Serve the main application HTML
        require_once APP_ROOT . '/views/index.php';
    }
    
} catch (Exception $e) {
    // Log error
    error_log('Application Error: ' . $e->getMessage());
    
    // Check if this is an API request
    if (strpos($_SERVER['REQUEST_URI'] ?? '', '/api/') === 0) {
        header('Content-Type: application/json');
        http_response_code(500);
        echo json_encode([
            'error' => 'Internal server error',
            'message' => DEBUG_MODE ? $e->getMessage() : 'Something went wrong'
        ]);
    } else {
        // Show error page
        http_response_code(500);
        echo '<h1>Application Error</h1>';
        if (DEBUG_MODE) {
            echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
            echo '<pre>' . htmlspecialchars($e->getTraceAsString()) . '</pre>';
        } else {
            echo '<p>Something went wrong. Please try again later.</p>';
        }
    }
}
