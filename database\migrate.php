<?php
declare(strict_types=1);

/**
 * migrate.php
 * Migration script to transfer data from old JSON files to new database
 * 
 * This script migrates user data, ad views, and other information
 * from the example application to the new UniQPaid database.
 */

// Define APP_ROOT
define('APP_ROOT', dirname(__DIR__));

// Include autoloader and config
require_once APP_ROOT . '/app/autoload.php';
require_once APP_ROOT . '/app/config.php';

use Models\User;
use Models\AdView;
use Models\Referral;
use Models\FraudLog;

class DataMigrator
{
    private string $examplePath;
    private array $stats = [
        'users_migrated' => 0,
        'ad_views_migrated' => 0,
        'referrals_migrated' => 0,
        'fraud_logs_migrated' => 0,
        'errors' => []
    ];

    public function __construct()
    {
        $this->examplePath = APP_ROOT . '/example/database';
    }

    /**
     * Run the complete migration
     */
    public function migrate(): void
    {
        echo "🚀 Starting data migration from example to UniQPaid...\n\n";

        try {
            // Initialize database
            $database = Database::getInstance();
            echo "✅ Database connection established\n";

            // Migrate users
            $this->migrateUsers();

            // Migrate ad views (if data exists)
            $this->migrateAdViews();

            // Migrate referrals
            $this->migrateReferrals();

            // Migrate fraud logs
            $this->migrateFraudLogs();

            // Show statistics
            $this->showStats();

            echo "\n🎉 Migration completed successfully!\n";

        } catch (Exception $e) {
            echo "❌ Migration failed: " . $e->getMessage() . "\n";
            echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
        }
    }

    /**
     * Migrate user data from JSON to database
     */
    private function migrateUsers(): void
    {
        echo "👤 Migrating users...\n";

        $userDataFile = $this->examplePath . '/user_data.json';
        if (!file_exists($userDataFile)) {
            echo "⚠️  User data file not found: $userDataFile\n";
            return;
        }

        $userData = json_decode(file_get_contents($userDataFile), true);
        if (!$userData) {
            echo "⚠️  Could not parse user data JSON\n";
            return;
        }

        foreach ($userData as $telegramId => $data) {
            try {
                // Check if user already exists
                $existingUser = User::findByTelegramId((int)$telegramId);
                if ($existingUser) {
                    echo "  - User $telegramId already exists, skipping\n";
                    continue;
                }

                // Create new user
                $user = new User([
                    'telegram_id' => (int)$telegramId,
                    'username' => $data['username'] ?? null,
                    'first_name' => $data['first_name'] ?? '',
                    'last_name' => $data['last_name'] ?? '',
                    'language' => $data['language_code'] ?? 'ru',
                    'balance' => (int)($data['balance'] ?? 0),
                    'total_earned' => (int)($data['total_earned'] ?? 0),
                    'referrer_id' => isset($data['referrer_id']) ? (int)$data['referrer_id'] : null,
                    'referral_earnings' => (int)($data['referral_earnings'] ?? 0),
                    'registered_at' => $data['registered_at'] ?? time(),
                    'last_activity' => $data['last_activity'] ?? time(),
                    'suspicious_activity_count' => (int)($data['suspicious_activity_count'] ?? 0),
                    'blocked' => (bool)($data['blocked'] ?? false),
                    'blocked_at' => $data['blocked_at'] ?? null,
                    'device_fingerprint' => $data['device_fingerprint'] ?? null
                ]);

                if ($user->save()) {
                    $this->stats['users_migrated']++;
                    echo "  ✅ Migrated user: $telegramId\n";
                } else {
                    $this->stats['errors'][] = "Failed to save user: $telegramId";
                    echo "  ❌ Failed to save user: $telegramId\n";
                }

            } catch (Exception $e) {
                $this->stats['errors'][] = "Error migrating user $telegramId: " . $e->getMessage();
                echo "  ❌ Error migrating user $telegramId: " . $e->getMessage() . "\n";
            }
        }

        echo "✅ Users migration completed: {$this->stats['users_migrated']} users migrated\n\n";
    }

    /**
     * Migrate ad views data
     */
    private function migrateAdViews(): void
    {
        echo "📺 Migrating ad views...\n";

        $adLimitsFile = $this->examplePath . '/ad_limits.json';
        if (!file_exists($adLimitsFile)) {
            echo "⚠️  Ad limits file not found: $adLimitsFile\n";
            return;
        }

        $adLimits = json_decode(file_get_contents($adLimitsFile), true);
        if (!$adLimits) {
            echo "⚠️  Could not parse ad limits JSON\n";
            return;
        }

        // Convert ad limits to ad views
        foreach ($adLimits as $telegramId => $limits) {
            try {
                $user = User::findByTelegramId((int)$telegramId);
                if (!$user) {
                    continue; // Skip if user doesn't exist
                }

                // Create ad views based on limits (simulate past views)
                $adTypes = ['native_banner', 'interstitial', 'rewarded_video'];
                
                foreach ($adTypes as $adType) {
                    $viewCount = $limits[$adType] ?? 0;
                    
                    for ($i = 0; $i < $viewCount; $i++) {
                        $rewardAmount = AdView::getRewardAmount($adType);
                        
                        $adView = new AdView([
                            'user_id' => (int)$telegramId,
                            'ad_type' => $adType,
                            'ad_token' => 'migrated_' . uniqid(),
                            'reward_amount' => $rewardAmount,
                            'viewed_at' => time() - rand(0, 86400), // Random time in last 24h
                            'ip_address' => '127.0.0.1',
                            'user_agent' => 'Migrated Data',
                            'session_id' => 'migration_session'
                        ]);

                        if ($adView->save()) {
                            $this->stats['ad_views_migrated']++;
                        }
                    }
                }

                echo "  ✅ Migrated ad views for user: $telegramId\n";

            } catch (Exception $e) {
                $this->stats['errors'][] = "Error migrating ad views for user $telegramId: " . $e->getMessage();
                echo "  ❌ Error migrating ad views for user $telegramId: " . $e->getMessage() . "\n";
            }
        }

        echo "✅ Ad views migration completed: {$this->stats['ad_views_migrated']} ad views migrated\n\n";
    }

    /**
     * Migrate referral data
     */
    private function migrateReferrals(): void
    {
        echo "👥 Migrating referrals...\n";

        // Get users with referrers
        $userDataFile = $this->examplePath . '/user_data.json';
        if (!file_exists($userDataFile)) {
            return;
        }

        $userData = json_decode(file_get_contents($userDataFile), true);
        if (!$userData) {
            return;
        }

        foreach ($userData as $telegramId => $data) {
            if (isset($data['referrer_id']) && $data['referrer_id']) {
                try {
                    $referral = Referral::create((int)$data['referrer_id'], (int)$telegramId);
                    if ($referral) {
                        $this->stats['referrals_migrated']++;
                        echo "  ✅ Migrated referral: {$data['referrer_id']} -> $telegramId\n";
                    }
                } catch (Exception $e) {
                    $this->stats['errors'][] = "Error migrating referral $telegramId: " . $e->getMessage();
                    echo "  ❌ Error migrating referral $telegramId: " . $e->getMessage() . "\n";
                }
            }
        }

        echo "✅ Referrals migration completed: {$this->stats['referrals_migrated']} referrals migrated\n\n";
    }

    /**
     * Migrate fraud logs
     */
    private function migrateFraudLogs(): void
    {
        echo "🔒 Migrating fraud logs...\n";

        $fraudLogFile = $this->examplePath . '/fraud_log.json';
        if (!file_exists($fraudLogFile)) {
            echo "⚠️  Fraud log file not found: $fraudLogFile\n";
            return;
        }

        $fraudLogs = json_decode(file_get_contents($fraudLogFile), true);
        if (!$fraudLogs) {
            echo "⚠️  Could not parse fraud log JSON\n";
            return;
        }

        foreach ($fraudLogs as $log) {
            try {
                $fraudLog = FraudLog::create(
                    $log['user_id'] ?? null,
                    $log['event_type'] ?? 'unknown',
                    $log['description'] ?? '',
                    $log['severity'] ?? 'low',
                    [
                        'ip_address' => $log['ip_address'] ?? null,
                        'user_agent' => $log['user_agent'] ?? null,
                        'session_id' => $log['session_id'] ?? null
                    ]
                );

                if ($fraudLog) {
                    $this->stats['fraud_logs_migrated']++;
                }

            } catch (Exception $e) {
                $this->stats['errors'][] = "Error migrating fraud log: " . $e->getMessage();
                echo "  ❌ Error migrating fraud log: " . $e->getMessage() . "\n";
            }
        }

        echo "✅ Fraud logs migration completed: {$this->stats['fraud_logs_migrated']} fraud logs migrated\n\n";
    }

    /**
     * Show migration statistics
     */
    private function showStats(): void
    {
        echo "📊 Migration Statistics:\n";
        echo "  Users migrated: {$this->stats['users_migrated']}\n";
        echo "  Ad views migrated: {$this->stats['ad_views_migrated']}\n";
        echo "  Referrals migrated: {$this->stats['referrals_migrated']}\n";
        echo "  Fraud logs migrated: {$this->stats['fraud_logs_migrated']}\n";
        echo "  Errors: " . count($this->stats['errors']) . "\n";

        if (!empty($this->stats['errors'])) {
            echo "\n❌ Errors encountered:\n";
            foreach ($this->stats['errors'] as $error) {
                echo "  - $error\n";
            }
        }
    }
}

// Run migration if called directly
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    $migrator = new DataMigrator();
    $migrator->migrate();
}
