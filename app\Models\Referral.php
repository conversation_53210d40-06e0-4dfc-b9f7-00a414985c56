<?php
declare(strict_types=1);

namespace Models;

/**
 * Referral.php
 * Referral model for managing referral relationships and bonuses
 * 
 * This model handles referral tracking, bonus calculations,
 * and referral statistics.
 */
class Referral extends Model
{
    /** @var string Table name */
    protected string $table = 'referrals';
    
    /** @var array<string> Mass assignable fields */
    protected array $fillable = [
        'referrer_id',
        'referred_id',
        'bonus_amount'
    ];

    /**
     * Create new referral relationship
     * 
     * @param int $referrerId
     * @param int $referredId
     * @return Referral|null
     */
    public static function create(int $referrerId, int $referredId): ?Referral
    {
        // Prevent self-referral
        if ($referrerId === $referredId) {
            error_log("Self-referral attempt: {$referrerId}");
            return null;
        }

        // Check if referrer exists
        $referrer = User::findByTelegramId($referrerId);
        if (!$referrer) {
            error_log("Referrer not found: {$referrerId}");
            return null;
        }

        // Check if referred user exists
        $referred = User::findByTelegramId($referredId);
        if (!$referred) {
            error_log("Referred user not found: {$referredId}");
            return null;
        }

        // Check if referral already exists
        $existing = self::where([
            'referrer_id' => $referrerId,
            'referred_id' => $referredId
        ]);

        if (!empty($existing)) {
            error_log("Referral already exists: {$referrerId} -> {$referredId}");
            return $existing[0];
        }

        // Create referral record
        $referral = new self([
            'referrer_id' => $referrerId,
            'referred_id' => $referredId,
            'bonus_amount' => 0
        ]);

        if ($referral->save()) {
            error_log("Referral created: {$referrerId} -> {$referredId}");
            return $referral;
        }

        return null;
    }

    /**
     * Add bonus to referral
     * 
     * @param int $amount
     * @return bool
     */
    public function addBonus(int $amount): bool
    {
        if ($amount <= 0) {
            return false;
        }

        $this->bonus_amount = ($this->bonus_amount ?? 0) + $amount;
        return $this->save();
    }

    /**
     * Get referrals for user
     * 
     * @param int $userId
     * @return array<Referral>
     */
    public static function getUserReferrals(int $userId): array
    {
        return self::where(['referrer_id' => $userId]);
    }

    /**
     * Get referral statistics for user
     * 
     * @param int $userId
     * @return array<string, mixed>
     */
    public static function getUserStats(int $userId): array
    {
        $referrals = self::getUserReferrals($userId);
        
        $stats = [
            'total_referrals' => count($referrals),
            'total_bonus' => 0,
            'active_referrals' => 0,
            'recent_referrals' => 0
        ];

        $weekAgo = time() - (7 * 24 * 3600);

        foreach ($referrals as $referral) {
            $stats['total_bonus'] += $referral->bonus_amount ?? 0;
            
            // Check if referred user is active (has activity in last week)
            $referredUser = User::findByTelegramId($referral->referred_id);
            if ($referredUser && $referredUser->last_activity >= $weekAgo) {
                $stats['active_referrals']++;
            }
            
            // Check if referral was created in last week
            if ($referral->created_at >= $weekAgo) {
                $stats['recent_referrals']++;
            }
        }

        return $stats;
    }

    /**
     * Get top referrers
     * 
     * @param int $limit
     * @return array<array<string, mixed>>
     */
    public static function getTopReferrers(int $limit = 10): array
    {
        $instance = new self();
        $sql = "SELECT 
                    r.referrer_id,
                    u.username,
                    u.first_name,
                    COUNT(r.id) as referral_count,
                    SUM(r.bonus_amount) as total_bonus
                FROM {$instance->table} r
                LEFT JOIN users u ON r.referrer_id = u.telegram_id
                GROUP BY r.referrer_id
                ORDER BY referral_count DESC, total_bonus DESC
                LIMIT ?";
        
        try {
            $stmt = $instance->getConnection()->prepare($sql);
            $stmt->execute([$limit]);
            
            $results = [];
            while ($row = $stmt->fetch()) {
                $results[] = [
                    'user_id' => (int)$row['referrer_id'],
                    'username' => $row['username'],
                    'first_name' => $row['first_name'],
                    'referral_count' => (int)$row['referral_count'],
                    'total_bonus' => (int)$row['total_bonus']
                ];
            }
            
            return $results;
        } catch (\PDOException $e) {
            error_log("Error getting top referrers: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get referral chain for user (who referred whom)
     * 
     * @param int $userId
     * @param int $depth Maximum depth to traverse
     * @return array<array<string, mixed>>
     */
    public static function getReferralChain(int $userId, int $depth = 3): array
    {
        $chain = [];
        $currentUserId = $userId;
        
        for ($i = 0; $i < $depth; $i++) {
            $user = User::findByTelegramId($currentUserId);
            if (!$user || !$user->referrer_id) {
                break;
            }
            
            $referrer = User::findByTelegramId($user->referrer_id);
            if (!$referrer) {
                break;
            }
            
            $chain[] = [
                'level' => $i + 1,
                'user_id' => $referrer->telegram_id,
                'username' => $referrer->username,
                'first_name' => $referrer->first_name
            ];
            
            $currentUserId = $referrer->telegram_id;
        }
        
        return $chain;
    }

    /**
     * Calculate potential bonus for referrer
     * 
     * @param int $baseReward
     * @return int
     */
    public static function calculateBonus(int $baseReward): int
    {
        return (int)($baseReward * REFERRAL_BONUS_PERCENT);
    }

    /**
     * Get referral link for user
     * 
     * @param int $userId
     * @return string
     */
    public static function generateReferralLink(int $userId): string
    {
        $botUsername = BOT_USERNAME;
        return "https://t.me/{$botUsername}?start=ref_{$userId}";
    }

    /**
     * Parse referral code from start parameter
     * 
     * @param string $startParam
     * @return int|null
     */
    public static function parseReferralCode(string $startParam): ?int
    {
        if (preg_match('/^ref_(\d+)$/', $startParam, $matches)) {
            return (int)$matches[1];
        }
        
        return null;
    }

    /**
     * Get global referral statistics
     * 
     * @return array<string, mixed>
     */
    public static function getGlobalStats(): array
    {
        $instance = new self();
        
        try {
            // Total referrals
            $totalStmt = $instance->getConnection()->query("SELECT COUNT(*) as count FROM {$instance->table}");
            $totalReferrals = $totalStmt->fetch()['count'];
            
            // Total bonus paid
            $bonusStmt = $instance->getConnection()->query("SELECT SUM(bonus_amount) as total FROM {$instance->table}");
            $totalBonus = $bonusStmt->fetch()['total'] ?? 0;
            
            // Active referrers (users with at least one referral)
            $activeStmt = $instance->getConnection()->query("SELECT COUNT(DISTINCT referrer_id) as count FROM {$instance->table}");
            $activeReferrers = $activeStmt->fetch()['count'];
            
            // Recent referrals (last 7 days)
            $weekAgo = time() - (7 * 24 * 3600);
            $recentStmt = $instance->getConnection()->prepare("SELECT COUNT(*) as count FROM {$instance->table} WHERE created_at >= ?");
            $recentStmt->execute([$weekAgo]);
            $recentReferrals = $recentStmt->fetch()['count'];
            
            return [
                'total_referrals' => (int)$totalReferrals,
                'total_bonus_paid' => (int)$totalBonus,
                'active_referrers' => (int)$activeReferrers,
                'recent_referrals' => (int)$recentReferrals,
                'avg_referrals_per_user' => $activeReferrers > 0 ? round($totalReferrals / $activeReferrers, 2) : 0
            ];
        } catch (\PDOException $e) {
            error_log("Error getting global referral stats: " . $e->getMessage());
            return [
                'total_referrals' => 0,
                'total_bonus_paid' => 0,
                'active_referrers' => 0,
                'recent_referrals' => 0,
                'avg_referrals_per_user' => 0
            ];
        }
    }
}
