/* === ИСПРАВЛЕНИЯ ДЛЯ КНОПОК ПОД ДИЗАЙН === */

/* Все кнопки действий должны быть оранжевыми */
.action-button {
    background: #FFA500 !important;
    border: none !important;
    color: #000 !important;
    border-radius: 16px !important;
    padding: 16px 20px !important;
    margin-bottom: 12px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    min-height: 70px !important;
    width: 100% !important;
    font-weight: 600 !important;
    position: relative !important;
}

/* Убираем все цветовые классы */
.action-button.purple-button,
.action-button.blue-button,
.action-button.orange-button {
    background: #FFA500 !important;
}

/* Иконки слева - простые, без фона */
.action-button .button-icon {
    width: 24px !important;
    height: 24px !important;
    margin-right: 15px !important;
    flex-shrink: 0 !important;
    background: none !important;
    border-radius: 0 !important;
    padding: 0 !important;
}

/* SVG иконки */
.action-button .button-icon svg {
    width: 24px !important;
    height: 24px !important;
    color: #000 !important;
    stroke: #000 !important;
    fill: none !important;
}

/* Содержимое кнопки */
.action-button .button-content {
    flex: 1 !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: flex-start !important;
    text-align: left !important;
}

/* Заголовок кнопки */
.action-button .button-text {
    font-size: 16px !important;
    font-weight: 600 !important;
    color: #000 !important;
    margin-bottom: 4px !important;
    line-height: 1.2 !important;
}

/* Счетчик просмотров */
.action-button .ad-counter {
    font-size: 14px !important;
    font-weight: 500 !important;
    color: rgba(0, 0, 0, 0.75) !important;
    line-height: 1.1 !important;
}

/* Награда справа - оранжевый кружок */
.action-button .reward-badge {
    background: #FF6B35 !important;
    color: white !important;
    padding: 8px 12px !important;
    border-radius: 50px !important;
    font-size: 16px !important;
    font-weight: 700 !important;
    min-width: 40px !important;
    height: 40px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border: 2px solid rgba(255, 255, 255, 0.3) !important;
    flex-shrink: 0 !important;
}

/* Заголовок Tasks */
#tasks-title {
    display: flex !important;
    align-items: center !important;
    justify-content: flex-start !important;
    gap: 12px !important;
    margin-bottom: 20px !important;
    color: white !important;
}

#tasks-title svg {
    width: 24px !important;
    height: 24px !important;
    color: #FFA500 !important;
    stroke: #FFA500 !important;
}

#tasks-title span {
    font-size: 24px !important;
    font-weight: 700 !important;
    color: white !important;
}

/* Центрирование контейнера */
.app-container {
    max-width: 400px !important;
    margin: 0 auto !important;
    padding: 0 20px !important;
}

/* Исправление баланса в шапке */
.balance-info .balance-amount {
    color: #FFA500 !important;
    font-weight: 700 !important;
}

.balance-info .balance-currency {
    color: #FFA500 !important;
    font-weight: 600 !important;
}
