/* ======================================== */
/* STYLISH CYBERPUNK EFFECTS - Стильные киберпанк эффекты */
/* ======================================== */

// Инициализация стильных киберпанк эффектов
function initStylishCyberEffects() {
  // Добавляем стильные интерактивные эффекты
  addStylishInteractions();

  // Добавляем стильные анимации
  addStylishAnimations();

  // Создаем крутые частицы (только на десктопе)
  if (window.innerWidth > 768) {
    createCyberParticles();
  }

  // НЕ ТРОГАЕМ навигацию - она работает в main.js
}

// Стильные интерактивные эффекты
function addStylishInteractions() {
  // Стильные эффекты для кнопок
  const buttons = document.querySelectorAll('.action-button');
  buttons.forEach(button => {
    button.addEventListener('mouseenter', () => {
      createStylishRipple(button);
    });

    button.addEventListener('click', () => {
      createClickEffect(button);
    });
  });

  // Стильные эффекты для карточек
  const cards = document.querySelectorAll('.app-section, .friends-block, .earn-block');
  cards.forEach(card => {
    card.addEventListener('mouseenter', () => {
      card.style.borderColor = '#ff0080';
    });

    card.addEventListener('mouseleave', () => {
      card.style.borderColor = '#00d4ff';
    });
  });
}

// Создание стильного волнового эффекта
function createStylishRipple(element) {
  const ripple = document.createElement('div');
  ripple.style.cssText = `
    position: absolute;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(0, 212, 255, 0.6), transparent);
    transform: scale(0);
    animation: stylish-ripple 0.6s linear;
    left: 50%;
    top: 50%;
    width: 30px;
    height: 30px;
    margin-left: -15px;
    margin-top: -15px;
    pointer-events: none;
  `;

  element.style.position = 'relative';
  element.appendChild(ripple);

  setTimeout(() => {
    ripple.remove();
  }, 600);
}

// Эффект клика
function createClickEffect(element) {
  const effect = document.createElement('div');
  effect.style.cssText = `
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100px;
    height: 100px;
    margin-left: -50px;
    margin-top: -50px;
    background: radial-gradient(circle, rgba(255, 0, 128, 0.4), transparent);
    border-radius: 50%;
    animation: click-effect 0.5s ease-out;
    pointer-events: none;
  `;

  element.style.position = 'relative';
  element.appendChild(effect);

  setTimeout(() => {
    effect.remove();
  }, 500);
}

// Стильные анимации
function addStylishAnimations() {
  // Плавное появление элементов
  const elements = document.querySelectorAll('.app-header, .app-section, .app-nav');

  const observer = new IntersectionObserver((entries) => {
    entries.forEach((entry, index) => {
      if (entry.isIntersecting) {
        setTimeout(() => {
          entry.target.style.animation = 'cyber-fade-in 0.6s ease forwards';
          entry.target.style.opacity = '1';
        }, index * 100);
        observer.unobserve(entry.target);
      }
    });
  }, { threshold: 0.1 });

  elements.forEach(element => {
    element.style.opacity = '0';
    observer.observe(element);
  });
}

// Добавление стильных CSS анимаций
function addStylishCSS() {
  const style = document.createElement('style');
  style.textContent = `
    @keyframes stylish-ripple {
      to {
        transform: scale(4);
        opacity: 0;
      }
    }

    @keyframes click-effect {
      0% { transform: translate(-50%, -50%) scale(0); opacity: 1; }
      100% { transform: translate(-50%, -50%) scale(2); opacity: 0; }
    }
  `;
  document.head.appendChild(style);
}

// Создание крутых киберпанк частиц
function createCyberParticles() {
  const particlesContainer = document.createElement('div');
  particlesContainer.className = 'cyber-particles';
  document.body.appendChild(particlesContainer);

  // Создаем 15 частиц
  for (let i = 0; i < 15; i++) {
    createParticle(particlesContainer, i);
  }

  // Периодически создаем новые частицы
  setInterval(() => {
    if (Math.random() < 0.3) {
      createParticle(particlesContainer);
    }
  }, 2000);
}

// Создание одной частицы
function createParticle(container, delay = 0) {
  const particle = document.createElement('div');
  particle.className = 'cyber-particle';

  const colors = ['#00d4ff', '#ff0080', '#00ff88'];
  const color = colors[Math.floor(Math.random() * colors.length)];

  particle.style.cssText = `
    left: ${Math.random() * 100}%;
    background: ${color};
    box-shadow: 0 0 6px ${color};
    animation-delay: ${delay * 0.5}s;
  `;

  container.appendChild(particle);

  // Удаляем частицу через 8 секунд
  setTimeout(() => {
    if (particle.parentNode) {
      particle.remove();
    }
  }, 8000 + delay * 500);
}

// Инициализация всех стильных эффектов
document.addEventListener('DOMContentLoaded', () => {
  // Небольшая задержка чтобы не конфликтовать с main.js
  setTimeout(() => {
    addStylishCSS();
    initStylishCyberEffects();
  }, 300);
});


