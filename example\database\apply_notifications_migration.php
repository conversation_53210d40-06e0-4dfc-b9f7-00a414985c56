<?php
/**
 * Скрипт для применения миграции уведомлений
 */

require_once __DIR__ . '/../config/database.php';

try {
    // Подключение к базе данных
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "🔗 Подключение к базе данных установлено\n";
    
    // Читаем SQL файл
    $sql = file_get_contents(__DIR__ . '/notifications_settings.sql');
    
    if ($sql === false) {
        throw new Exception("Не удалось прочитать файл notifications_settings.sql");
    }
    
    // Разделяем на отдельные запросы
    $queries = array_filter(array_map('trim', explode(';', $sql)));
    
    echo "📝 Найдено " . count($queries) . " SQL запросов\n";
    
    // Выполняем каждый запрос
    foreach ($queries as $index => $query) {
        if (empty($query) || strpos($query, '--') === 0) {
            continue;
        }
        
        try {
            $pdo->exec($query);
            echo "✅ Запрос " . ($index + 1) . " выполнен успешно\n";
        } catch (PDOException $e) {
            echo "⚠️ Запрос " . ($index + 1) . " пропущен (возможно, уже существует): " . $e->getMessage() . "\n";
        }
    }
    
    echo "\n🎉 Миграция уведомлений применена успешно!\n";
    echo "📊 Созданы таблицы:\n";
    echo "   - notification_settings (настройки уведомлений)\n";
    echo "   - notification_logs (логи отправки)\n";
    echo "💾 Добавлены настройки по умолчанию\n";
    
} catch (Exception $e) {
    echo "❌ Ошибка: " . $e->getMessage() . "\n";
    exit(1);
}
?>
