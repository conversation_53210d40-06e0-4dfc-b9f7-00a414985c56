/* ======================================== */
/* REFINED CYBERPUNK EFFECTS - Утонченные киберпанк эффекты */
/* ======================================== */

// Инициализация утонченных киберпанк эффектов
function initRefinedCyberpunkEffects() {
  console.log('🎯 Инициализация утонченных киберпанк эффектов...');
  
  // Добавляем тонкие визуальные эффекты
  addSubtleVisualEffects();
  
  // Создаем утонченные интерактивные эффекты
  addRefinedInteractions();
  
  // Добавляем элегантные анимации
  addElegantAnimations();
  
  // НЕ ТРОГАЕМ навигацию - она работает в main.js
  
  console.log('✨ Утонченные киберпанк эффекты активированы!');
}

// Тонкие визуальные эффекты
function addSubtleVisualEffects() {
  // Создаем тонкие плавающие частицы
  createSubtleParticles();
  
  // Добавляем тонкое свечение к важным элементам
  addSubtleGlow();
  
  // Создаем эффект энергетических линий
  createEnergyLines();
}

// Создание тонких плавающих частиц
function createSubtleParticles() {
  const particlesContainer = document.createElement('div');
  particlesContainer.className = 'refined-particles';
  particlesContainer.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
  `;
  document.body.appendChild(particlesContainer);
  
  // Создаем только 8 тонких частиц
  for (let i = 0; i < 8; i++) {
    const particle = document.createElement('div');
    particle.style.cssText = `
      position: absolute;
      width: 1px;
      height: 1px;
      background: rgba(0, 212, 255, 0.6);
      border-radius: 50%;
      box-shadow: 0 0 4px rgba(0, 212, 255, 0.8);
      animation: refined-float ${4 + Math.random() * 4}s ease-in-out infinite;
      left: ${Math.random() * 100}%;
      top: ${Math.random() * 100}%;
      animation-delay: ${Math.random() * 4}s;
    `;
    
    particlesContainer.appendChild(particle);
  }
}

// Добавление тонкого свечения
function addSubtleGlow() {
  // Добавляем свечение к балансу при изменении
  const balanceElements = document.querySelectorAll('.balance-amount');
  balanceElements.forEach(element => {
    const observer = new MutationObserver(() => {
      element.style.animation = 'refined-glow 1s ease-in-out';
      setTimeout(() => {
        element.style.animation = '';
      }, 1000);
    });
    
    observer.observe(element, { childList: true, characterData: true, subtree: true });
  });
  
  // Добавляем пульсацию к статус сообщениям
  const statusMessages = document.querySelectorAll('.status-message');
  statusMessages.forEach(message => {
    message.style.animation = 'refined-pulse 3s ease-in-out infinite';
  });
}

// Создание энергетических линий
function createEnergyLines() {
  const linesContainer = document.createElement('div');
  linesContainer.className = 'energy-lines';
  linesContainer.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
    overflow: hidden;
  `;
  document.body.appendChild(linesContainer);
  
  // Создаем 3 тонкие энергетические линии
  for (let i = 0; i < 3; i++) {
    const line = document.createElement('div');
    line.style.cssText = `
      position: absolute;
      width: 100%;
      height: 1px;
      background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.3), transparent);
      top: ${20 + i * 30}%;
      animation: refined-scan ${8 + i * 2}s linear infinite;
      animation-delay: ${i * 2}s;
    `;
    linesContainer.appendChild(line);
  }
}

// Утонченные интерактивные эффекты
function addRefinedInteractions() {
  // Эффекты для кнопок
  const buttons = document.querySelectorAll('.action-button');
  buttons.forEach(button => {
    button.addEventListener('mouseenter', () => {
      createSubtleRipple(button);
    });
    
    button.addEventListener('click', () => {
      createClickEffect(button);
    });
  });
  
  // Эффекты для карточек
  const cards = document.querySelectorAll('.app-section, .friends-block, .earn-block');
  cards.forEach(card => {
    card.addEventListener('mouseenter', () => {
      card.style.borderColor = 'rgba(0, 212, 255, 0.4)';
    });
    
    card.addEventListener('mouseleave', () => {
      card.style.borderColor = 'rgba(0, 212, 255, 0.2)';
    });
  });
  
  // Эффекты для полей ввода
  const inputs = document.querySelectorAll('input, select, textarea');
  inputs.forEach(input => {
    input.addEventListener('focus', () => {
      input.style.boxShadow = '0 0 0 2px rgba(0, 212, 255, 0.2)';
    });
    
    input.addEventListener('blur', () => {
      input.style.boxShadow = '';
    });
  });
}

// Создание тонкого волнового эффекта
function createSubtleRipple(element) {
  const ripple = document.createElement('div');
  ripple.style.cssText = `
    position: absolute;
    border-radius: 50%;
    background: rgba(0, 212, 255, 0.2);
    transform: scale(0);
    animation: subtle-ripple 0.6s linear;
    left: 50%;
    top: 50%;
    width: 20px;
    height: 20px;
    margin-left: -10px;
    margin-top: -10px;
    pointer-events: none;
  `;
  
  element.style.position = 'relative';
  element.appendChild(ripple);
  
  setTimeout(() => {
    ripple.remove();
  }, 600);
}

// Эффект клика
function createClickEffect(element) {
  const effect = document.createElement('div');
  effect.style.cssText = `
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 212, 255, 0.1);
    border-radius: inherit;
    animation: click-flash 0.3s ease-out;
    pointer-events: none;
  `;
  
  element.style.position = 'relative';
  element.appendChild(effect);
  
  setTimeout(() => {
    effect.remove();
  }, 300);
}

// Элегантные анимации
function addElegantAnimations() {
  // Плавное появление элементов
  const elements = document.querySelectorAll('.app-section, .action-button');
  
  const observer = new IntersectionObserver((entries) => {
    entries.forEach((entry, index) => {
      if (entry.isIntersecting) {
        setTimeout(() => {
          entry.target.style.animation = 'refined-fade-in 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards';
          entry.target.style.opacity = '1';
        }, index * 100);
        observer.unobserve(entry.target);
      }
    });
  }, { threshold: 0.1 });
  
  elements.forEach(element => {
    element.style.opacity = '0';
    observer.observe(element);
  });
}

// Утонченные уведомления
function showRefinedNotification(message, type = 'info') {
  const notification = document.createElement('div');
  notification.className = `refined-notification refined-notification-${type}`;
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(10, 10, 10, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(0, 212, 255, 0.3);
    border-radius: 12px;
    padding: 12px 16px;
    color: rgba(0, 212, 255, 0.9);
    font-weight: 500;
    font-size: 13px;
    z-index: 10000;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    max-width: 280px;
    text-shadow: 0 0 8px rgba(0, 212, 255, 0.3);
  `;
  
  if (type === 'success') {
    notification.style.borderColor = 'rgba(0, 245, 255, 0.4)';
    notification.style.color = 'rgba(0, 245, 255, 0.9)';
  } else if (type === 'error') {
    notification.style.borderColor = 'rgba(255, 0, 110, 0.4)';
    notification.style.color = 'rgba(255, 0, 110, 0.9)';
  }
  
  notification.textContent = message;
  document.body.appendChild(notification);
  
  // Анимация появления
  setTimeout(() => {
    notification.style.transform = 'translateX(0)';
    notification.style.opacity = '1';
  }, 100);
  
  // Автоматическое скрытие
  setTimeout(() => {
    notification.style.transform = 'translateX(100%)';
    notification.style.opacity = '0';
    setTimeout(() => {
      notification.remove();
    }, 300);
  }, 3500);
}

// Добавление CSS анимаций
function addRefinedCSS() {
  const style = document.createElement('style');
  style.textContent = `
    @keyframes subtle-ripple {
      to {
        transform: scale(3);
        opacity: 0;
      }
    }
    
    @keyframes click-flash {
      0% { opacity: 0.3; }
      50% { opacity: 0.1; }
      100% { opacity: 0; }
    }
    
    /* Улучшенные переходы */
    * {
      transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important;
    }
    
    /* Плавная прокрутка */
    html {
      scroll-behavior: smooth;
    }
    
    /* Утонченный фокус */
    button:focus,
    input:focus,
    select:focus {
      outline: 1px solid rgba(0, 212, 255, 0.3);
      outline-offset: 1px;
    }
    
    /* Скрытие частиц на мобильных */
    @media (max-width: 768px) {
      .refined-particles,
      .energy-lines {
        display: none;
      }
    }
  `;
  document.head.appendChild(style);
}

// Утонченный курсор (только для десктопа)
function initRefinedCursor() {
  if (window.innerWidth <= 768) return;
  
  const cursor = document.createElement('div');
  cursor.style.cssText = `
    position: fixed;
    width: 6px;
    height: 6px;
    background: rgba(0, 212, 255, 0.8);
    border-radius: 50%;
    pointer-events: none;
    z-index: 9999;
    transition: all 0.1s cubic-bezier(0.4, 0, 0.2, 1);
    mix-blend-mode: difference;
    box-shadow: 0 0 8px rgba(0, 212, 255, 0.6);
  `;
  document.body.appendChild(cursor);
  
  document.addEventListener('mousemove', (e) => {
    cursor.style.left = e.clientX - 3 + 'px';
    cursor.style.top = e.clientY - 3 + 'px';
  });
  
  // Эффекты при наведении
  const interactiveElements = document.querySelectorAll('button, a, input, [role="button"]');
  interactiveElements.forEach(element => {
    element.addEventListener('mouseenter', () => {
      cursor.style.transform = 'scale(1.5)';
      cursor.style.background = 'rgba(0, 245, 255, 0.9)';
    });
    
    element.addEventListener('mouseleave', () => {
      cursor.style.transform = 'scale(1)';
      cursor.style.background = 'rgba(0, 212, 255, 0.8)';
    });
  });
}

// Инициализация всех утонченных эффектов
document.addEventListener('DOMContentLoaded', () => {
  // Небольшая задержка чтобы не конфликтовать с main.js
  setTimeout(() => {
    addRefinedCSS();
    initRefinedCyberpunkEffects();
    
    // Утонченный курсор только для десктопа
    if (window.innerWidth > 768) {
      initRefinedCursor();
    }
    
    // Показываем приветственное уведомление
    setTimeout(() => {
      showRefinedNotification('🎯 Утонченный киберпанк активирован!', 'success');
    }, 1000);
  }, 300);
});

// Экспорт функций для использования в других скриптах
window.RefinedCyberpunkEffects = {
  showRefinedNotification,
  createSubtleRipple,
  createClickEffect,
  initRefinedCursor
};
