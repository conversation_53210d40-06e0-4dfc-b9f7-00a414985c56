// === calculator.js ===
// Калькулятор криптовалют

/**
 * Получает префикс валюты для отображения
 * @param {string} currency - Код валюты
 * @returns {string} Префикс валюты
 */
function getCurrencyPrefix(currency) {
  const prefixes = {
    'ton': 'TON ',
    'trx': 'TRX ',
    'usdt': 'USDT ',
    'btc': '₿',
    'eth': 'Ξ',
    'bnb': 'BNB ',
    'ada': 'ADA ',
    'dot': 'DOT ',
    'sol': 'SOL ',
    'matic': 'MATIC ',
    'avax': 'AVAX ',
    'atom': 'ATOM ',
    'near': 'NEAR ',
    'algo': 'ALGO ',
    'xtz': 'XTZ ',
    'egld': 'EGLD ',
    'one': 'ONE ',
    'ftm': 'FTM ',
    'celo': 'CELO ',
    'kava': 'KAVA ',
    'osmo': 'OSMO ',
    'juno': 'JUNO ',
    'evmos': 'EVMOS ',
    'inj': 'INJ ',
    'sei': 'SEI ',
    'tia': 'TIA '
  };
  return prefixes[currency.toLowerCase()] || currency.toUpperCase() + ' ';
}

/**
 * Обновляет отображение калькулятора
 * @param {number} coinAmount - Количество монет
 */
function updateCalculatorDisplay(coinAmount) {
  // Получаем актуальный курс конвертации из настроек
  const conversionRate = window.appSettings?.getCoinValue() || window.coinValue || 0.001;
  const dollarAmount = coinAmount * conversionRate;
  
  // Получаем активную вкладку
  const activeTab = document.querySelector('.currency-tab.active');
  if (!activeTab) return;
  
  const currency = activeTab.dataset.currency;
  const data = window.currencyData[currency];
  if (!data) return;

  // ИСПРАВЛЕНИЕ: Сохраняем данные калькулятора для синхронизации
  if (window.calculatorData) {
    window.calculatorData.amount = coinAmount;
    window.calculatorData.currency = currency;
    window.calculatorData.usdAmount = dollarAmount;
  }
  const coinsText = window.appLocalization ? window.appLocalization.get('currency.coins') : 'монет';
  console.log(`[Calculator] Сохранены данные: ${coinAmount} ${coinsText}, ${currency}, $${dollarAmount.toFixed(2)}`);

  // Обновляем расчеты для выбранной валюты с форматированием
  updateCurrencyCalculationDisplay(currency, coinAmount, dollarAmount);
}

/**
 * Обновляет отображение расчетов для конкретной валюты
 * @param {string} currency - Валюта
 * @param {number} coinAmount - Количество монет
 * @param {number} dollarAmount - Сумма в долларах
 */
function updateCurrencyCalculationDisplay(currency, coinAmount, dollarAmount) {
  const data = window.currencyData[currency];
  if (!data) return;

  // Используем унифицированный калькулятор
  updateCurrencyCalculationWithUnifiedCalculator(currency, coinAmount, dollarAmount);
}

/**
 * Унифицированный калькулятор для всех валют
 * @param {string} currency - Валюта
 * @param {number} coinAmount - Количество монет
 * @param {number} dollarAmount - Сумма в долларах
 */
function updateCurrencyCalculationWithUnifiedCalculator(currency, coinAmount, dollarAmount) {
  const data = window.currencyData[currency];
  if (!data) return;

  const userBalance = window._currentUserBalance || 0;
  
  // Отладочная информация для анализа минимальных сумм
  console.groupCollapsed(`[Calculator] Данные для ${currency.toUpperCase()}`);
  console.log('Сырые данные:', data);
  console.log('Минимум из данных:', data.minCoins);
  console.log('Комиссия сети:', data.networkFee);
  console.log('Курс обмена:', data.rate);
  console.groupEnd();
  
  // ИСПРАВЛЕНИЕ: Проверяем минимум с учетом комиссии
  const networkFee = parseFloat(data.networkFee) || 0;
  const exchangeRate = data.rate || 0;
  
  // Получаем актуальный минимум из данных (должен приходить с бэкенда)
  const minCoins = data.minCoins || 0;
  
  // Убираем повторное объявление переменных
  // const networkFee и exchangeRate уже объявлены выше
  
  // Рассчитываем минимум с учетом комиссии
  let minCoinsWithFee = minCoins;
  let minFeeText = '';
  
  if (exchangeRate > 0) {
      const feeInCoins = Math.ceil((networkFee / exchangeRate) / 0.001); //networkFee is already in USD
      minCoinsWithFee = minCoins + feeInCoins;
      minFeeText = ` (с комиссией ${feeInCoins} монет)`;
  }

  const hasMinimum = coinAmount >= minCoinsWithFee;
  const hasBalance = coinAmount <= userBalance;
  
  // Получаем курс валюты (используем уже объявленную переменную)
  if (!exchangeRate) {
    console.warn(`[Calculator] Курс для ${currency} не найден`);
    return;
  }

  // Рассчитываем сумму в криптовалюте (до вычета комиссии)
  const grossCryptoAmount = dollarAmount / exchangeRate;
  
  // Рассчитываем комиссию в долларах и криптовалюте
  const feeUsd = parseFloat(data.networkFee) || 0;
  const feeCrypto = feeUsd / exchangeRate;
  
  // Итоговая сумма после вычета комиссии
  const cryptoAmountNet = Math.max(0, grossCryptoAmount - feeCrypto);
  const afterFeeUsd = cryptoAmountNet * exchangeRate;

  // Обновляем элементы отображения
  const cryptoAmountElement = document.getElementById(`${currency}-crypto-amount`);
  const dollarAmountElement = document.getElementById(`${currency}-dollar-amount`);
  const statusElement = document.getElementById(`${currency}-status`);
  const finalAmountDisplay = document.getElementById(`${currency}-final-amount`);

  if (cryptoAmountElement) {
    cryptoAmountElement.textContent = `${cryptoAmountNet.toFixed(8)} ${currency.toUpperCase()}`;
    console.log(`[Calculator] Обновлено ${currency}-crypto-amount: ${cryptoAmountNet.toFixed(8)} ${currency.toUpperCase()}`);
  }

  if (dollarAmountElement) {
    dollarAmountElement.textContent = `$${dollarAmount.toFixed(2)}`;
    console.log(`[Calculator] Обновлено ${currency}-dollar-amount: $${dollarAmount.toFixed(2)}`);
  }

  if (statusElement) {
    let statusText = '';
    let statusClass = '';
    
    if (!hasBalance) {
      statusText = window.appLocalization ?
        window.appLocalization.get('earnings.insufficient_balance_have', {balance: userBalance}) :
        `Недостаточно ${window.appLocalization ? window.appLocalization.get('currency.coins') : 'монет'} (есть: ${userBalance})`;
      statusClass = 'error';
    } else if (!hasMinimum) {
      // ИСПРАВЛЕНИЕ: Показываем минимум с учетом комиссии
      statusText = `Минимум: ${minCoinsWithFee.toLocaleString()} монет${minFeeText}`;
      statusClass = 'warning';
    } else {
      statusText = window.appLocalization ?
        window.appLocalization.get('earnings.ready_for_withdrawal') :
        'Готово к выводу';
      statusClass = 'success';
    }
    
    statusElement.textContent = statusText;
    statusElement.className = `status ${statusClass}`;
    console.log(`[Calculator] Обновлен статус для ${currency}: ${statusText}`);
  }

  if (finalAmountDisplay) {
    // ИСПРАВЛЕНИЕ: Добавляем префикс валюты и сумму в криптовалюте
    const currencyPrefix = getCurrencyPrefix(currency);

    // ИСПРАВЛЕНИЕ: Показываем и доллары и криптовалюту с правильным префиксом
    finalAmountDisplay.textContent = `$${afterFeeUsd.toFixed(3)} (${currencyPrefix}${cryptoAmountNet.toFixed(8)})`;
    finalAmountDisplay.title = `Сумма после вычета комиссии: ${currencyPrefix}${cryptoAmountNet.toFixed(8)} = $${afterFeeUsd.toFixed(3)}`;

    console.log(`[Calculator] Обновлено final-amount-display: $${afterFeeUsd.toFixed(3)} (${currencyPrefix}${cryptoAmountNet.toFixed(8)})`);

    // ИСПРАВЛЕНИЕ: Сохраняем результат расчета для синхронизации
    // Сохраняем результат расчета для синхронизации с бэкендом
    calculatorData.cryptoAmount = cryptoAmountNet;
    calculatorData.lastCalculation = {
        cryptoAmount: cryptoAmountNet,
        usdAmount: afterFeeUsd,
        currency: currency,
        currencyPrefix: currencyPrefix,
        displayText: `$${afterFeeUsd.toFixed(3)} (${currencyPrefix}${cryptoAmountNet.toFixed(8)})`,
        timestamp: Date.now(),
        // Добавляем хеш для проверки целостности данных
        hash: btoa(`${cryptoAmountNet}_${currency}_${Date.now()}`).substring(0, 12)
    };
    console.log(`[Calculator] Сохранен результат для синхронизации: ${cryptoAmountNet.toFixed(8)} ${currency.toUpperCase()} (hash: ${calculatorData.lastCalculation.hash})`);
    
    // Глобально доступные данные для формы вывода
    window.calculatorResult = {
        coins: coinAmount,
        crypto: cryptoAmountNet,
        currency: currency,
        hash: calculatorData.lastCalculation.hash
    };
  }
}

/**
 * Проверяет баланс пользователя для калькулятора
 * @param {number} coinAmount - Количество монет
 */
function updateBalanceCheck(coinAmount) {
  const userBalance = _currentUserBalance || 0;
  const balanceCheckElement = document.getElementById('balance-check');

  if (balanceCheckElement) {
    if (coinAmount > userBalance) {
      const coinsText = window.appLocalization ?
        window.appLocalization.get('currency.coins') :
        'монет';
      const insufficientText = window.appLocalization ?
        window.appLocalization.get('earnings.insufficient_available_balance', { balance: userBalance }) :
        `Недостаточно. Доступно к выводу: ${userBalance} ${coinsText}`;
      balanceCheckElement.textContent = insufficientText;
      balanceCheckElement.className = 'balance-check error';
    } else if (coinAmount === 0) {
      balanceCheckElement.textContent = '';
      balanceCheckElement.className = 'balance-check';
    } else {
      const sufficientText = window.appLocalization ?
        window.appLocalization.get('earnings.sufficient_balance') :
        '✅ Достаточно ' + (window.appLocalization ? window.appLocalization.get('currency.coins') : 'монет');
      balanceCheckElement.textContent = sufficientText;
      balanceCheckElement.className = 'balance-check success';
    }
  }
}

/**
 * Инициализирует калькулятор
 */
function initCalculator() {
  const calcAmountInput = document.getElementById('calc-amount');
  
  if (calcAmountInput) {
    calcAmountInput.addEventListener('input', (e) => {
      const amount = parseInt(e.target.value) || 0;
      updateCalculatorDisplay(amount);
      updateBalanceCheck(amount);
    });
    
    console.log('🧮 Калькулятор инициализирован');
  }
}

/**
 * Переключает активную вкладку валюты в калькуляторе
 * @param {string} currency - Код валюты
 */
function switchCurrencyTab(currency) {
  // Убираем активный класс со всех вкладок
  const tabs = document.querySelectorAll('.currency-tab');
  tabs.forEach(tab => tab.classList.remove('active'));
  
  // Добавляем активный класс к выбранной вкладке
  const targetTab = document.querySelector(`[data-currency="${currency}"]`);
  if (targetTab) {
    targetTab.classList.add('active');
    
    // Обновляем калькулятор для новой валюты
    const calcAmountInput = document.getElementById('calc-amount');
    if (calcAmountInput && calcAmountInput.value) {
      const amount = parseInt(calcAmountInput.value) || 0;
      updateCalculatorDisplay(amount);
      updateBalanceCheck(amount);
    }
    
    console.log(`[Calculator] Переключено на валюту: ${currency}`);
  }
}

/**
 * Получает данные текущего расчета калькулятора
 * @returns {Object} Данные расчета
 */
function getCalculatorData() {
  return { ...calculatorData };
}

/**
 * Очищает данные калькулятора
 */
function clearCalculatorData() {
  calculatorData.amount = 0;
  calculatorData.currency = 'ton';
  calculatorData.cryptoAmount = 0;
  calculatorData.usdAmount = 0;
  calculatorData.lastCalculation = null;
  calculatorData.isValid = false;
  
  console.log('[Calculator] Данные очищены');
}

// Делаем функции доступными глобально
window.getCurrencyPrefix = getCurrencyPrefix;
window.updateCalculatorDisplay = updateCalculatorDisplay;
window.updateBalanceCheck = updateBalanceCheck;
window.initCalculator = initCalculator;
window.switchCurrencyTab = switchCurrencyTab;
window.getCalculatorData = getCalculatorData;
window.clearCalculatorData = clearCalculatorData;
