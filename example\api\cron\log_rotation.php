<?php
/**
 * Система ротации логов для рекламной статистики
 * Автоматическое архивирование и сжатие старых логов
 */

// Настройки ротации
$config = [
    'max_file_size' => 10 * 1024 * 1024, // 10 МБ
    'archive_days' => 30, // Архивировать логи старше 30 дней
    'delete_days' => 90,  // Удалять архивы старше 90 дней
    'log_file' => __DIR__ . '/../ad_requests.log',
    'archive_dir' => __DIR__ . '/log_archives',
    'rotation_log' => __DIR__ . '/log_rotation.log'
];

/**
 * Логирование действий ротации
 */
function logRotationAction($message, $config) {
    $timestamp = date('Y-m-d H:i:s');
    $logEntry = "[{$timestamp}] {$message}" . PHP_EOL;
    file_put_contents($config['rotation_log'], $logEntry, FILE_APPEND | LOCK_EX);
}

/**
 * Создание директории для архивов
 */
function ensureArchiveDirectory($config) {
    if (!is_dir($config['archive_dir'])) {
        if (!mkdir($config['archive_dir'], 0755, true)) {
            throw new Exception("Не удалось создать директорию архивов: " . $config['archive_dir']);
        }
        logRotationAction("Создана директория архивов: " . $config['archive_dir'], $config);
    }
}

/**
 * Проверка размера файла и ротация при необходимости
 */
function checkAndRotateBySize($config) {
    if (!file_exists($config['log_file'])) {
        return false;
    }
    
    $fileSize = filesize($config['log_file']);
    
    if ($fileSize >= $config['max_file_size']) {
        $timestamp = date('Y-m-d_H-i-s');
        $archiveFile = $config['archive_dir'] . '/ad_requests_' . $timestamp . '.log';
        
        // Копируем текущий лог в архив
        if (copy($config['log_file'], $archiveFile)) {
            // Очищаем текущий лог
            file_put_contents($config['log_file'], '');
            
            // Сжимаем архивный файл
            if (function_exists('gzopen')) {
                compressLogFile($archiveFile, $config);
            }
            
            logRotationAction("Ротация по размеру: {$fileSize} байт -> {$archiveFile}", $config);
            return true;
        } else {
            logRotationAction("ОШИБКА: Не удалось скопировать лог в архив", $config);
            return false;
        }
    }
    
    return false;
}

/**
 * Ротация логов по дате
 */
function rotateByDate($config) {
    if (!file_exists($config['log_file'])) {
        return false;
    }
    
    $fileModTime = filemtime($config['log_file']);
    $daysSinceModified = (time() - $fileModTime) / (24 * 60 * 60);
    
    if ($daysSinceModified >= $config['archive_days']) {
        $date = date('Y-m-d', $fileModTime);
        $archiveFile = $config['archive_dir'] . '/ad_requests_' . $date . '.log';
        
        // Если архив уже существует, добавляем время
        if (file_exists($archiveFile)) {
            $time = date('H-i-s', $fileModTime);
            $archiveFile = $config['archive_dir'] . '/ad_requests_' . $date . '_' . $time . '.log';
        }
        
        if (rename($config['log_file'], $archiveFile)) {
            // Создаем новый пустой лог
            touch($config['log_file']);
            chmod($config['log_file'], 0644);
            
            // Сжимаем архивный файл
            if (function_exists('gzopen')) {
                compressLogFile($archiveFile, $config);
            }
            
            logRotationAction("Ротация по дате: {$daysSinceModified} дней -> {$archiveFile}", $config);
            return true;
        } else {
            logRotationAction("ОШИБКА: Не удалось переместить лог в архив", $config);
            return false;
        }
    }
    
    return false;
}

/**
 * Сжатие лог-файла
 */
function compressLogFile($filePath, $config) {
    if (!file_exists($filePath)) {
        return false;
    }
    
    $compressedPath = $filePath . '.gz';
    
    $input = fopen($filePath, 'rb');
    $output = gzopen($compressedPath, 'wb9');
    
    if ($input && $output) {
        while (!feof($input)) {
            gzwrite($output, fread($input, 8192));
        }
        
        fclose($input);
        gzclose($output);
        
        // Удаляем несжатый файл
        unlink($filePath);
        
        $originalSize = filesize($filePath);
        $compressedSize = filesize($compressedPath);
        $ratio = round((1 - $compressedSize / $originalSize) * 100, 1);
        
        logRotationAction("Сжатие: {$filePath} -> {$compressedPath} (экономия {$ratio}%)", $config);
        return true;
    }
    
    logRotationAction("ОШИБКА: Не удалось сжать файл {$filePath}", $config);
    return false;
}

/**
 * Удаление старых архивов
 */
function cleanOldArchives($config) {
    if (!is_dir($config['archive_dir'])) {
        return 0;
    }
    
    $deleted = 0;
    $cutoffTime = time() - ($config['delete_days'] * 24 * 60 * 60);
    
    $files = glob($config['archive_dir'] . '/ad_requests_*.{log,gz}', GLOB_BRACE);
    
    foreach ($files as $file) {
        if (filemtime($file) < $cutoffTime) {
            if (unlink($file)) {
                $deleted++;
                logRotationAction("Удален старый архив: " . basename($file), $config);
            } else {
                logRotationAction("ОШИБКА: Не удалось удалить архив: " . basename($file), $config);
            }
        }
    }
    
    return $deleted;
}

/**
 * Получение статистики архивов
 */
function getArchiveStats($config) {
    if (!is_dir($config['archive_dir'])) {
        return [
            'count' => 0,
            'total_size' => 0,
            'oldest_date' => null,
            'newest_date' => null
        ];
    }
    
    $files = glob($config['archive_dir'] . '/ad_requests_*.{log,gz}', GLOB_BRACE);
    $totalSize = 0;
    $dates = [];
    
    foreach ($files as $file) {
        $totalSize += filesize($file);
        $dates[] = filemtime($file);
    }
    
    return [
        'count' => count($files),
        'total_size' => $totalSize,
        'oldest_date' => $dates ? date('Y-m-d', min($dates)) : null,
        'newest_date' => $dates ? date('Y-m-d', max($dates)) : null
    ];
}

// Основная логика выполнения
try {
    logRotationAction("=== НАЧАЛО РОТАЦИИ ЛОГОВ ===", $config);
    
    // Создаем директорию архивов
    ensureArchiveDirectory($config);
    
    // Проверяем ротацию по размеру
    $rotatedBySize = checkAndRotateBySize($config);
    
    // Проверяем ротацию по дате
    $rotatedByDate = rotateByDate($config);
    
    // Удаляем старые архивы
    $deletedCount = cleanOldArchives($config);
    
    // Получаем статистику
    $stats = getArchiveStats($config);
    
    logRotationAction("Ротация завершена. Архивов: {$stats['count']}, размер: " . 
                     round($stats['total_size'] / 1024 / 1024, 2) . " МБ, удалено: {$deletedCount}", $config);
    
    logRotationAction("=== КОНЕЦ РОТАЦИИ ЛОГОВ ===", $config);
    
    // Возвращаем результат для cron
    echo json_encode([
        'success' => true,
        'rotated_by_size' => $rotatedBySize,
        'rotated_by_date' => $rotatedByDate,
        'deleted_archives' => $deletedCount,
        'archive_stats' => $stats,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    
} catch (Exception $e) {
    $errorMsg = "КРИТИЧЕСКАЯ ОШИБКА ротации логов: " . $e->getMessage();
    logRotationAction($errorMsg, $config);
    
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    
    exit(1);
}
?>
