<?php
/**
 * api/vpn-detection.php
 * Серверная система детекции VPN/Proxy
 * 
 * Анализирует IP адреса, заголовки и другие данные
 * для обнаружения VPN/Proxy соединений
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

require_once __DIR__ . '/config.php';

error_log("vpn-detection INFO: Получен запрос на анализ VPN");

try {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['action'])) {
        throw new Exception('Отсутствует параметр action');
    }
    
    $action = $input['action'];
    
    switch ($action) {
        case 'analyze_vpn':
            echo json_encode(analyzeVPN($input));
            break;
            
        case 'check_ip_reputation':
            echo json_encode(checkIPReputation($input));
            break;
            
        default:
            throw new Exception('Неизвестное действие: ' . $action);
    }
    
} catch (Exception $e) {
    error_log('vpn-detection ERROR: ' . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'vpn_detected' => false,
        'risk_score' => 0
    ]);
}

/**
 * ФУНКЦИЯ analyzeVPN() ПЕРЕНЕСЕНА В fraud-detection.php
 * Чтобы избежать конфликта "Cannot redeclare analyzeVPN()"
 */
function analyzeVPN_DISABLED($input) {
    try {
        $clientIP = getRealIpAddr();
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        $clientData = $input['client_data'] ?? [];
        
        $analysis = [
            'ip_analysis' => analyzeIP($clientIP),
            'header_analysis' => analyzeHeaders(),
            'geolocation_analysis' => analyzeGeolocation($clientIP, $clientData),
            'reputation_analysis' => checkIPDatabase($clientIP),
            'client_analysis' => $clientData
        ];
        
        // Вычисляем общий риск-скор
        $riskScore = calculateVPNRiskScore($analysis);
        
        // Логируем результат
        logVPNAnalysis($clientIP, $analysis, $riskScore);
        
        return [
            'success' => true,
            'vpn_detected' => $riskScore >= 50,
            'risk_score' => $riskScore,
            'analysis' => $analysis,
            'client_ip' => $clientIP,
            'timestamp' => time()
        ];
        
    } catch (Exception $e) {
        error_log('analyzeVPN ERROR: ' . $e->getMessage());
        return [
            'success' => false,
            'error' => $e->getMessage(),
            'vpn_detected' => false,
            'risk_score' => 0
        ];
    }
}

/**
 * Анализ IP адреса
 */
function analyzeIP($ip) {
    $analysis = [
        'ip' => $ip,
        'is_private' => isPrivateIP($ip),
        'is_reserved' => isReservedIP($ip),
        'reverse_dns' => null,
        'asn_info' => null
    ];
    
    // Reverse DNS lookup
    try {
        $hostname = gethostbyaddr($ip);
        if ($hostname !== $ip) {
            $analysis['reverse_dns'] = $hostname;
            
            // Проверяем подозрительные паттерны в hostname
            $vpnPatterns = [
                '/vpn/i', '/proxy/i', '/tor/i', '/tunnel/i',
                '/anonymous/i', '/private/i', '/secure/i'
            ];
            
            foreach ($vpnPatterns as $pattern) {
                if (preg_match($pattern, $hostname)) {
                    $analysis['suspicious_hostname'] = true;
                    break;
                }
            }
        }
    } catch (Exception $e) {
        $analysis['reverse_dns_error'] = $e->getMessage();
    }
    
    return $analysis;
}

/**
 * Анализ заголовков HTTP
 */
function analyzeHeaders() {
    $headers = getallheaders();
    $analysis = [
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
        'accept_language' => $_SERVER['HTTP_ACCEPT_LANGUAGE'] ?? '',
        'x_forwarded_for' => $_SERVER['HTTP_X_FORWARDED_FOR'] ?? null,
        'x_real_ip' => $_SERVER['HTTP_X_REAL_IP'] ?? null,
        'via' => $_SERVER['HTTP_VIA'] ?? null,
        'proxy_headers' => []
    ];
    
    // Проверяем наличие прокси заголовков
    $proxyHeaders = [
        'HTTP_X_FORWARDED_FOR',
        'HTTP_X_FORWARDED_HOST',
        'HTTP_X_FORWARDED_PROTO',
        'HTTP_X_REAL_IP',
        'HTTP_VIA',
        'HTTP_FORWARDED',
        'HTTP_CLIENT_IP',
        'HTTP_X_CLUSTER_CLIENT_IP'
    ];
    
    foreach ($proxyHeaders as $header) {
        if (isset($_SERVER[$header])) {
            $analysis['proxy_headers'][$header] = $_SERVER[$header];
        }
    }
    
    // Анализ User Agent
    $suspiciousUAPatterns = [
        '/curl/i', '/wget/i', '/python/i', '/java/i',
        '/bot/i', '/crawler/i', '/spider/i',
        '/headless/i', '/phantom/i', '/selenium/i'
    ];
    
    foreach ($suspiciousUAPatterns as $pattern) {
        if (preg_match($pattern, $analysis['user_agent'])) {
            $analysis['suspicious_user_agent'] = true;
            break;
        }
    }
    
    return $analysis;
}

/**
 * Анализ геолокации
 */
function analyzeGeolocation($ip, $clientData) {
    $analysis = [
        'server_ip_geo' => getIPGeolocation($ip),
        'client_timezone' => $clientData['timezone'] ?? null,
        'client_geo' => $clientData['geolocation'] ?? null,
        'mismatch_detected' => false
    ];
    
    // Сравниваем timezone с IP геолокацией
    if ($analysis['server_ip_geo'] && $analysis['client_timezone']) {
        $ipCountry = $analysis['server_ip_geo']['country'] ?? '';
        $timezoneCountry = getCountryFromTimezone($analysis['client_timezone']);
        
        if ($ipCountry && $timezoneCountry && $ipCountry !== $timezoneCountry) {
            $analysis['mismatch_detected'] = true;
            $analysis['mismatch_details'] = [
                'ip_country' => $ipCountry,
                'timezone_country' => $timezoneCountry
            ];
        }
    }
    
    return $analysis;
}

/**
 * Проверка IP в базах данных
 */
function checkIPDatabase($ip) {
    // Здесь можно интегрировать внешние API для проверки IP
    // Например: AbuseIPDB, VirusTotal, IPQualityScore
    
    $analysis = [
        'checked' => false,
        'is_malicious' => false,
        'is_vpn' => false,
        'confidence' => 0
    ];
    
    // Простая проверка по известным VPN диапазонам
    $knownVPNRanges = [
        '185.220.', // Tor exit nodes
        '199.87.',   // NordVPN
        '103.231.',  // ExpressVPN
        // Добавить больше диапазонов по необходимости
    ];
    
    foreach ($knownVPNRanges as $range) {
        if (strpos($ip, $range) === 0) {
            $analysis['is_vpn'] = true;
            $analysis['confidence'] = 90;
            break;
        }
    }
    
    return $analysis;
}

/**
 * Вычисление общего риск-скора для VPN
 */
function calculateVPNRiskScore($analysis) {
    $score = 0;
    
    // IP анализ
    if ($analysis['ip_analysis']['suspicious_hostname'] ?? false) {
        $score += 40;
    }
    
    // Заголовки
    if (!empty($analysis['header_analysis']['proxy_headers'])) {
        $score += 30;
    }
    
    if ($analysis['header_analysis']['suspicious_user_agent'] ?? false) {
        $score += 20;
    }
    
    // Геолокация
    if ($analysis['geolocation_analysis']['mismatch_detected'] ?? false) {
        $score += 25;
    }
    
    // Репутация IP
    if ($analysis['reputation_analysis']['is_vpn'] ?? false) {
        $score += 50;
    }
    
    // Клиентские данные
    $clientAnalysis = $analysis['client_analysis'];
    if ($clientAnalysis['is_vpn_detected'] ?? false) {
        $score += $clientAnalysis['risk_score'] ?? 0;
    }
    
    return min($score, 100);
}

/**
 * Логирование анализа VPN
 */
function logVPNAnalysis($ip, $analysis, $riskScore) {
    try {
        $logFile = __DIR__ . '/../database/vpn_analysis_log.json';
        
        $logEntry = [
            'timestamp' => time(),
            'ip' => $ip,
            'risk_score' => $riskScore,
            'vpn_detected' => $riskScore >= 50,
            'analysis' => $analysis,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
        ];
        
        $log = [];
        if (file_exists($logFile)) {
            $log = json_decode(file_get_contents($logFile), true) ?: [];
        }
        
        $log[] = $logEntry;
        
        // Ограничиваем размер лога
        if (count($log) > 1000) {
            $log = array_slice($log, -1000);
        }
        
        file_put_contents($logFile, json_encode($log, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        
    } catch (Exception $e) {
        error_log('logVPNAnalysis ERROR: ' . $e->getMessage());
    }
}

/**
 * Получение геолокации по IP
 */
function getIPGeolocation($ip) {
    // Простая заглушка - в реальном проекте использовать внешний API
    return [
        'country' => 'Unknown',
        'city' => 'Unknown',
        'latitude' => null,
        'longitude' => null
    ];
}

/**
 * Получение страны по timezone
 */
function getCountryFromTimezone($timezone) {
    $timezoneToCountry = [
        'Europe/Moscow' => 'RU',
        'America/New_York' => 'US',
        'Europe/London' => 'GB',
        'Asia/Tokyo' => 'JP',
        // Добавить больше соответствий
    ];
    
    return $timezoneToCountry[$timezone] ?? null;
}

/**
 * Проверка приватного IP
 */
function isPrivateIP($ip) {
    return !filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE);
}

/**
 * Проверка зарезервированного IP
 */
function isReservedIP($ip) {
    return !filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_RES_RANGE);
}

/**
 * Получение реального IP адреса
 */
function getRealIpAddr() {
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        return $_SERVER['HTTP_CLIENT_IP'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        return $_SERVER['HTTP_X_FORWARDED_FOR'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED'])) {
        return $_SERVER['HTTP_X_FORWARDED'];
    } elseif (!empty($_SERVER['HTTP_FORWARDED_FOR'])) {
        return $_SERVER['HTTP_FORWARDED_FOR'];
    } elseif (!empty($_SERVER['HTTP_FORWARDED'])) {
        return $_SERVER['HTTP_FORWARDED'];
    } elseif (!empty($_SERVER['REMOTE_ADDR'])) {
        return $_SERVER['REMOTE_ADDR'];
    }
    return 'unknown';
}

?>
