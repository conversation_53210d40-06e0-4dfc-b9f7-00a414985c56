<?php
declare(strict_types=1);

namespace Controllers;

use Models\User;
use Models\AdView;
use Models\Withdrawal;
use Models\Referral;
use Models\FraudLog;
use Models\Translation;

/**
 * ApiController.php
 * Main API controller for handling all API endpoints
 * 
 * This controller handles user authentication, ad views, withdrawals,
 * referrals, and other core application functionality.
 */
class ApiController extends Controller
{
    /**
     * Get user data
     * Endpoint: POST /api/user/data
     */
    public function getUserData(): void
    {
        try {
            $userData = $this->validateTelegramAuth();
            $userId = (int)$userData['id'];
            
            // Find or create user
            $user = User::findByTelegramId($userId);
            if (!$user) {
                $user = User::createFromTelegram($userData);
            } else {
                // Update user activity
                $user->updateActivity();
            }
            
            // Get user statistics
            $stats = $user->getStats();
            
            // Get referral information
            $referralStats = Referral::getUserStats($userId);
            
            // Get today's ad view counts
            $adStats = [];
            foreach (AdView::VALID_AD_TYPES as $adType) {
                $adStats[$adType] = [
                    'today_views' => $user->getTodayAdViews($adType),
                    'can_view' => $user->canViewAd($adType),
                    'reward' => AdView::getRewardAmount($adType)
                ];
            }
            
            $this->logAction('getUserData', ['user_id' => $userId]);
            
            $this->respondWithSuccess([
                'user' => [
                    'id' => $user->telegram_id,
                    'username' => $user->username,
                    'first_name' => $user->first_name,
                    'last_name' => $user->last_name,
                    'language' => $user->language,
                    'balance' => $user->balance ?? 0,
                    'total_earned' => $user->total_earned ?? 0,
                    'blocked' => $user->blocked ?? false
                ],
                'stats' => $stats,
                'referral' => $referralStats,
                'ads' => $adStats
            ]);
            
        } catch (\Exception $e) {
            $this->logAction('getUserData_error', ['error' => $e->getMessage()]);
            $this->respondWithError('Failed to get user data: ' . $e->getMessage());
        }
    }

    /**
     * Register new user
     * Endpoint: POST /api/user/register
     */
    public function registerUser(): void
    {
        try {
            $userData = $this->validateTelegramAuth();
            $userId = (int)$userData['id'];
            
            // Check if user already exists
            $existingUser = User::findByTelegramId($userId);
            if ($existingUser) {
                $this->respondWithSuccess(['message' => 'User already registered']);
                return;
            }
            
            // Check for referral code
            $referrerId = null;
            $startParam = $this->getParam('start_param');
            if ($startParam) {
                $referrerId = Referral::parseReferralCode($startParam);
            }
            
            // Create new user
            $user = User::createFromTelegram($userData, $referrerId);
            
            $this->logAction('registerUser', [
                'user_id' => $userId,
                'referrer_id' => $referrerId
            ]);
            
            $this->respondWithSuccess([
                'message' => 'User registered successfully',
                'user_id' => $user->telegram_id,
                'referrer_id' => $referrerId
            ]);
            
        } catch (\Exception $e) {
            $this->logAction('registerUser_error', ['error' => $e->getMessage()]);
            $this->respondWithError('Failed to register user: ' . $e->getMessage());
        }
    }

    /**
     * Record ad view and reward user
     * Endpoint: POST /api/ad/view
     */
    public function recordAdView(): void
    {
        try {
            $userData = $this->validateTelegramAuth();
            $userId = (int)$userData['id'];
            
            $adType = $this->getRequiredParam('ad_type');
            $adToken = $this->getParam('ad_token');
            
            // Validate ad type
            if (!in_array($adType, AdView::VALID_AD_TYPES)) {
                $this->respondWithError('Invalid ad type');
                return;
            }
            
            // Check for suspicious activity
            if (FRAUD_DETECTION_ENABLED && AdView::checkSuspiciousActivity($userId)) {
                $this->respondWithError('Suspicious activity detected');
                return;
            }
            
            // Record ad view
            $metadata = [
                'ip_address' => $this->requestData['_server']['ip'],
                'user_agent' => $this->requestData['_server']['user_agent'],
                'session_id' => $this->getParam('session_id')
            ];
            
            $adView = AdView::recordView($userId, $adType, $adToken, $metadata);
            
            if (!$adView) {
                $this->respondWithError('Failed to record ad view');
                return;
            }
            
            $this->logAction('recordAdView', [
                'user_id' => $userId,
                'ad_type' => $adType,
                'reward' => $adView->reward_amount
            ]);
            
            $this->respondWithSuccess([
                'message' => 'Ad view recorded successfully',
                'reward' => $adView->reward_amount,
                'ad_type' => $adType,
                'view_id' => $adView->id
            ]);
            
        } catch (\Exception $e) {
            $this->logAction('recordAdView_error', ['error' => $e->getMessage()]);
            $this->respondWithError('Failed to record ad view: ' . $e->getMessage());
        }
    }

    /**
     * Request withdrawal
     * Endpoint: POST /api/withdrawal/request
     */
    public function requestWithdrawal(): void
    {
        try {
            $userData = $this->validateTelegramAuth();
            $userId = (int)$userData['id'];
            
            $amount = (int)$this->getRequiredParam('amount');
            $currency = $this->getRequiredParam('currency');
            $address = $this->getRequiredParam('address');
            
            // Validate inputs
            if ($amount <= 0) {
                $this->respondWithError('Invalid withdrawal amount');
                return;
            }
            
            if (empty($currency) || empty($address)) {
                $this->respondWithError('Currency and address are required');
                return;
            }
            
            // Create withdrawal request
            $withdrawal = Withdrawal::createRequest($userId, $amount, $currency, $address);
            
            if (!$withdrawal) {
                $this->respondWithError('Failed to create withdrawal request');
                return;
            }
            
            $this->logAction('requestWithdrawal', [
                'user_id' => $userId,
                'amount' => $amount,
                'currency' => $currency,
                'withdrawal_id' => $withdrawal->id
            ]);
            
            $this->respondWithSuccess([
                'message' => 'Withdrawal request created successfully',
                'withdrawal_id' => $withdrawal->id,
                'status' => $withdrawal->status,
                'amount' => $withdrawal->amount,
                'crypto_amount' => $withdrawal->crypto_amount,
                'currency' => $withdrawal->currency
            ]);
            
        } catch (\Exception $e) {
            $this->logAction('requestWithdrawal_error', ['error' => $e->getMessage()]);
            $this->respondWithError('Failed to request withdrawal: ' . $e->getMessage());
        }
    }

    /**
     * Get withdrawal history
     * Endpoint: POST /api/withdrawal/history
     */
    public function getWithdrawalHistory(): void
    {
        try {
            $userData = $this->validateTelegramAuth();
            $userId = (int)$userData['id'];
            
            $limit = min((int)$this->getParam('limit', 20), 100);
            
            $withdrawals = Withdrawal::getUserHistory($userId, $limit);
            
            $history = array_map(function($withdrawal) {
                return $withdrawal->toApiArray();
            }, $withdrawals);
            
            $this->logAction('getWithdrawalHistory', ['user_id' => $userId]);
            
            $this->respondWithSuccess([
                'withdrawals' => $history,
                'count' => count($history)
            ]);
            
        } catch (\Exception $e) {
            $this->logAction('getWithdrawalHistory_error', ['error' => $e->getMessage()]);
            $this->respondWithError('Failed to get withdrawal history: ' . $e->getMessage());
        }
    }

    /**
     * Register referral
     * Endpoint: POST /api/referral/register
     */
    public function registerReferral(): void
    {
        try {
            $userData = $this->validateTelegramAuth();
            $userId = (int)$userData['id'];
            
            $referrerId = (int)$this->getRequiredParam('referrer_id');
            
            $referral = Referral::create($referrerId, $userId);
            
            if (!$referral) {
                $this->respondWithError('Failed to create referral');
                return;
            }
            
            $this->logAction('registerReferral', [
                'user_id' => $userId,
                'referrer_id' => $referrerId
            ]);
            
            $this->respondWithSuccess([
                'message' => 'Referral registered successfully',
                'referrer_id' => $referrerId,
                'referred_id' => $userId
            ]);
            
        } catch (\Exception $e) {
            $this->logAction('registerReferral_error', ['error' => $e->getMessage()]);
            $this->respondWithError('Failed to register referral: ' . $e->getMessage());
        }
    }

    /**
     * Get referral statistics
     * Endpoint: POST /api/referral/stats
     */
    public function getReferralStats(): void
    {
        try {
            $userData = $this->validateTelegramAuth();
            $userId = (int)$userData['id'];
            
            $stats = Referral::getUserStats($userId);
            $referralLink = Referral::generateReferralLink($userId);
            
            $this->logAction('getReferralStats', ['user_id' => $userId]);
            
            $this->respondWithSuccess([
                'stats' => $stats,
                'referral_link' => $referralLink
            ]);
            
        } catch (\Exception $e) {
            $this->logAction('getReferralStats_error', ['error' => $e->getMessage()]);
            $this->respondWithError('Failed to get referral stats: ' . $e->getMessage());
        }
    }

    /**
     * Get application settings
     * Endpoint: GET /api/settings
     */
    public function getAppSettings(): void
    {
        try {
            $settings = [
                'conversion_rate' => CONVERSION_RATE,
                'min_withdrawal' => MIN_WITHDRAWAL_AMOUNT,
                'min_balance_for_withdrawal' => MIN_BALANCE_FOR_WITHDRAWAL,
                'referral_bonus_percent' => REFERRAL_BONUS_PERCENT,
                'ad_rewards' => [
                    'native_banner' => AD_REWARD_NATIVE_BANNER,
                    'interstitial' => AD_REWARD_INTERSTITIAL,
                    'rewarded_video' => AD_REWARD_REWARDED_VIDEO
                ],
                'daily_limits' => [
                    'native_banner' => USER_AD_LIMIT_NATIVE_BANNER,
                    'interstitial' => USER_AD_LIMIT_INTERSTITIAL,
                    'rewarded_video' => USER_AD_LIMIT_REWARDED_VIDEO
                ],
                'app_info' => [
                    'name' => APP_NAME,
                    'version' => APP_VERSION,
                    'bot_username' => BOT_USERNAME
                ]
            ];
            
            $this->logAction('getAppSettings');
            
            $this->respondWithSuccess($settings);
            
        } catch (\Exception $e) {
            $this->logAction('getAppSettings_error', ['error' => $e->getMessage()]);
            $this->respondWithError('Failed to get app settings: ' . $e->getMessage());
        }
    }

    /**
     * Get available currencies for withdrawal
     * Endpoint: GET /api/currencies
     */
    public function getAvailableCurrencies(): void
    {
        try {
            $currencies = [
                'BTC' => ['name' => 'Bitcoin', 'symbol' => '₿', 'min_amount' => 0.00001],
                'ETH' => ['name' => 'Ethereum', 'symbol' => 'Ξ', 'min_amount' => 0.001],
                'USDT' => ['name' => 'Tether', 'symbol' => '₮', 'min_amount' => 1.0],
                'TON' => ['name' => 'Toncoin', 'symbol' => 'TON', 'min_amount' => 0.1],
                'LTC' => ['name' => 'Litecoin', 'symbol' => 'Ł', 'min_amount' => 0.01],
                'BCH' => ['name' => 'Bitcoin Cash', 'symbol' => 'BCH', 'min_amount' => 0.001]
            ];
            
            $this->logAction('getAvailableCurrencies');
            
            $this->respondWithSuccess(['currencies' => $currencies]);
            
        } catch (\Exception $e) {
            $this->logAction('getAvailableCurrencies_error', ['error' => $e->getMessage()]);
            $this->respondWithError('Failed to get currencies: ' . $e->getMessage());
        }
    }

    /**
     * Get translations for specified language
     * Endpoint: GET /api/translations
     */
    public function getTranslations(): void
    {
        try {
            $language = $_GET['lang'] ?? 'en';

            if (!in_array($language, ['en', 'ru'])) {
                $this->respondWithError('Invalid language', 400);
                return;
            }

            // Get translations from database
            $translations = Translation::getAllForLanguage($language);

            $this->respondWithSuccess([
                'language' => $language,
                'translations' => $translations,
                'count' => count($translations)
            ]);

        } catch (\Exception $e) {
            $this->logAction('getTranslations_error', ['error' => $e->getMessage()]);
            $this->respondWithError('Failed to get translations: ' . $e->getMessage());
        }
    }

    /**
     * Update user language preference
     * Endpoint: POST /api/user/language
     */
    public function updateUserLanguage(): void
    {
        try {
            $userData = $this->validateTelegramAuth();
            $userId = (int)$userData['id'];

            $input = json_decode(file_get_contents('php://input'), true);
            $language = $input['language'] ?? '';

            if (!in_array($language, ['en', 'ru'])) {
                $this->respondWithError('Invalid language', 400);
                return;
            }

            // Find user
            $user = User::findByTelegramId($userId);

            if (!$user) {
                $this->respondWithError('User not found', 404);
                return;
            }

            // Update language
            $user->setLanguage($language);

            $this->logAction('updateLanguage', ['language' => $language]);

            $this->respondWithSuccess([
                'language' => $language,
                'message' => 'Language updated successfully'
            ]);

        } catch (\Exception $e) {
            $this->logAction('updateUserLanguage_error', ['error' => $e->getMessage()]);
            $this->respondWithError('Failed to update language: ' . $e->getMessage());
        }
    }
}
