<?php
declare(strict_types=1);

header('Content-Type: text/plain');

echo "Testing SQLite support in web environment...\n";

// Check if PDO is available
if (!class_exists('PDO')) {
    echo "❌ PDO class not available\n";
    exit(1);
}

// Check available PDO drivers
$drivers = PDO::getAvailableDrivers();
echo "Available PDO drivers: " . implode(', ', $drivers) . "\n";

if (!in_array('sqlite', $drivers)) {
    echo "❌ SQLite driver not available\n";
    exit(1);
}

echo "✅ SQLite driver is available\n";

// Try to create a test database
try {
    $testDb = __DIR__ . '/../database/test.sqlite';
    $pdo = new PDO('sqlite:' . $testDb);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ SQLite connection successful\n";
    
    // Create test table
    $pdo->exec("CREATE TABLE IF NOT EXISTS test (id INTEGER PRIMARY KEY, name TEXT)");
    echo "✅ Table creation successful\n";
    
    // Insert test data
    $stmt = $pdo->prepare("INSERT INTO test (name) VALUES (?)");
    $stmt->execute(['test_value']);
    echo "✅ Data insertion successful\n";
    
    // Query test data
    $stmt = $pdo->query("SELECT * FROM test");
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "✅ Data query successful: " . count($results) . " rows\n";
    
    // Show results
    foreach ($results as $row) {
        echo "Row: ID={$row['id']}, Name={$row['name']}\n";
    }
    
    echo "✅ Test completed successfully\n";
    
} catch (Exception $e) {
    echo "❌ SQLite test failed: " . $e->getMessage() . "\n";
    exit(1);
}
