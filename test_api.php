<?php
/**
 * test_api.php
 * Simple API test script
 */

// Define APP_ROOT
define('APP_ROOT', __DIR__);

// Include autoloader and config
require_once 'app/autoload.php';
require_once 'app/config.php';

echo "🧪 Testing UniQPaid API...\n\n";

try {
    // Test 1: Database connection
    echo "1. Testing database connection...\n";
    $database = Database::getInstance();
    echo "✅ Database connected successfully\n\n";
    
    // Test 2: App settings API
    echo "2. Testing app settings API...\n";
    $_SERVER['REQUEST_METHOD'] = 'GET';
    $_SERVER['REQUEST_URI'] = '/api/settings';
    
    ob_start();
    $router = new Router();
    $router->addRoute('GET', '/api/settings', 'ApiController@getAppSettings');
    $router->handleRequest('GET', '/api/settings');
    $response = ob_get_clean();
    
    $data = json_decode($response, true);
    if ($data && $data['success']) {
        echo "✅ App settings API working\n";
        echo "   Conversion rate: " . $data['data']['conversion_rate'] . "\n";
        echo "   Min withdrawal: " . $data['data']['min_withdrawal'] . "\n";
    } else {
        echo "❌ App settings API failed\n";
        echo "Response: " . $response . "\n";
    }
    echo "\n";
    
    // Test 3: User data API
    echo "3. Testing user data API...\n";
    $_SERVER['REQUEST_METHOD'] = 'POST';
    $_SERVER['REQUEST_URI'] = '/api/user/data';
    
    // Simulate POST data
    $testInitData = 'user=' . urlencode(json_encode([
        'id' => 12345,
        'first_name' => 'Test',
        'username' => 'testuser',
        'language_code' => 'en'
    ])) . '&auth_date=' . time() . '&hash=test';
    
    // Mock POST input
    $postData = json_encode(['initData' => $testInitData]);
    
    // Temporarily override php://input
    $tempFile = tempnam(sys_get_temp_dir(), 'test_input');
    file_put_contents($tempFile, $postData);
    
    // Override file_get_contents for php://input
    $originalInput = file_get_contents('php://input');
    
    ob_start();
    
    // Simulate the API call
    try {
        $router = new Router();
        $router->addRoute('POST', '/api/user/data', 'ApiController@getUserData');
        
        // Mock the input
        $_POST = [];
        $GLOBALS['HTTP_RAW_POST_DATA'] = $postData;
        
        // Create a mock for file_get_contents
        eval('
        function file_get_contents_mock($filename) {
            if ($filename === "php://input") {
                return \'' . addslashes($postData) . '\';
            }
            return file_get_contents($filename);
        }
        ');
        
        $router->handleRequest('POST', '/api/user/data');
        $response = ob_get_clean();
        
        $data = json_decode($response, true);
        if ($data && $data['success']) {
            echo "✅ User data API working\n";
            echo "   User ID: " . $data['data']['user']['id'] . "\n";
            echo "   Balance: " . $data['data']['user']['balance'] . "\n";
        } else {
            echo "❌ User data API failed\n";
            echo "Response: " . $response . "\n";
        }
    } catch (Exception $e) {
        ob_end_clean();
        echo "❌ User data API error: " . $e->getMessage() . "\n";
    }
    
    unlink($tempFile);
    echo "\n";
    
    // Test 4: Check file database
    echo "4. Testing file database...\n";
    $fileDb = FileDatabase::getInstance();
    $tables = $fileDb->getTables();
    echo "✅ File database working\n";
    echo "   Tables: " . implode(', ', $tables) . "\n";
    
    // Test user creation
    $testUser = [
        'telegram_id' => 12345,
        'first_name' => 'Test',
        'username' => 'testuser',
        'balance' => 0,
        'total_earned' => 0,
        'registered_at' => time(),
        'last_activity' => time()
    ];
    
    $userId = $fileDb->insert('users', $testUser);
    if ($userId) {
        echo "✅ Test user created with ID: $userId\n";
        
        // Test user retrieval
        $retrievedUser = $fileDb->findOne('users', ['telegram_id' => 12345]);
        if ($retrievedUser) {
            echo "✅ Test user retrieved successfully\n";
        } else {
            echo "❌ Failed to retrieve test user\n";
        }
    } else {
        echo "❌ Failed to create test user\n";
    }
    echo "\n";
    
    echo "🎉 API tests completed!\n";
    
} catch (Exception $e) {
    echo "❌ Test failed with error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
