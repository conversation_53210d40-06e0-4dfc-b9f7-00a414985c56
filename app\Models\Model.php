<?php
declare(strict_types=1);

namespace Models;

/**
 * Model.php
 * Base model class for all data models
 * 
 * This abstract class provides common functionality for all models,
 * including database connection management and basic CRUD operations.
 */
abstract class Model
{
    /** @var string Table name for this model */
    protected string $table = '';
    
    /** @var string Primary key field name */
    protected string $primaryKey = 'id';
    
    /** @var array<string> Fields that can be mass assigned */
    protected array $fillable = [];
    
    /** @var array<string, mixed> Model attributes */
    protected array $attributes = [];
    
    /** @var bool Whether the model exists in database */
    protected bool $exists = false;

    /**
     * Constructor
     * 
     * @param array<string, mixed> $attributes Initial attributes
     */
    public function __construct(array $attributes = [])
    {
        $this->fill($attributes);
    }

    /**
     * Get database connection
     * 
     * @return \PDO
     */
    protected function getConnection(): \PDO
    {
        return \Database::getConnection();
    }

    /**
     * Fill model with attributes
     * 
     * @param array<string, mixed> $attributes
     * @return static
     */
    public function fill(array $attributes): static
    {
        foreach ($attributes as $key => $value) {
            if (in_array($key, $this->fillable) || empty($this->fillable)) {
                $this->attributes[$key] = $value;
            }
        }
        return $this;
    }

    /**
     * Get attribute value
     * 
     * @param string $key
     * @return mixed
     */
    public function __get(string $key): mixed
    {
        return $this->attributes[$key] ?? null;
    }

    /**
     * Set attribute value
     * 
     * @param string $key
     * @param mixed $value
     */
    public function __set(string $key, mixed $value): void
    {
        $this->attributes[$key] = $value;
    }

    /**
     * Check if attribute exists
     * 
     * @param string $key
     * @return bool
     */
    public function __isset(string $key): bool
    {
        return isset($this->attributes[$key]);
    }

    /**
     * Get all attributes
     * 
     * @return array<string, mixed>
     */
    public function toArray(): array
    {
        return $this->attributes;
    }

    /**
     * Save model to database
     * 
     * @return bool
     */
    public function save(): bool
    {
        if ($this->exists) {
            return $this->update();
        } else {
            return $this->insert();
        }
    }

    /**
     * Insert new record
     * 
     * @return bool
     */
    protected function insert(): bool
    {
        $fields = array_keys($this->attributes);
        $placeholders = array_fill(0, count($fields), '?');
        
        $sql = "INSERT INTO {$this->table} (" . implode(', ', $fields) . ") VALUES (" . implode(', ', $placeholders) . ")";
        
        try {
            $stmt = $this->getConnection()->prepare($sql);
            $result = $stmt->execute(array_values($this->attributes));
            
            if ($result) {
                $this->attributes[$this->primaryKey] = $this->getConnection()->lastInsertId();
                $this->exists = true;
            }
            
            return $result;
        } catch (\PDOException $e) {
            error_log("Model insert error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Update existing record
     * 
     * @return bool
     */
    protected function update(): bool
    {
        $fields = array_keys($this->attributes);
        $setParts = array_map(fn($field) => "$field = ?", $fields);
        
        $sql = "UPDATE {$this->table} SET " . implode(', ', $setParts) . " WHERE {$this->primaryKey} = ?";
        
        try {
            $stmt = $this->getConnection()->prepare($sql);
            $values = array_values($this->attributes);
            $values[] = $this->attributes[$this->primaryKey];
            
            return $stmt->execute($values);
        } catch (\PDOException $e) {
            error_log("Model update error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Find record by primary key
     * 
     * @param mixed $id
     * @return static|null
     */
    public static function find(mixed $id): ?static
    {
        $instance = new static();
        $sql = "SELECT * FROM {$instance->table} WHERE {$instance->primaryKey} = ? LIMIT 1";
        
        try {
            $stmt = $instance->getConnection()->prepare($sql);
            $stmt->execute([$id]);
            $data = $stmt->fetch();
            
            if ($data) {
                $model = new static($data);
                $model->exists = true;
                return $model;
            }
        } catch (\PDOException $e) {
            error_log("Model find error: " . $e->getMessage());
        }
        
        return null;
    }

    /**
     * Find records by criteria
     * 
     * @param array<string, mixed> $criteria
     * @return array<static>
     */
    public static function where(array $criteria): array
    {
        $instance = new static();
        $conditions = array_map(fn($key) => "$key = ?", array_keys($criteria));
        $sql = "SELECT * FROM {$instance->table} WHERE " . implode(' AND ', $conditions);
        
        try {
            $stmt = $instance->getConnection()->prepare($sql);
            $stmt->execute(array_values($criteria));
            $results = [];
            
            while ($data = $stmt->fetch()) {
                $model = new static($data);
                $model->exists = true;
                $results[] = $model;
            }
            
            return $results;
        } catch (\PDOException $e) {
            error_log("Model where error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Delete record
     * 
     * @return bool
     */
    public function delete(): bool
    {
        if (!$this->exists) {
            return false;
        }
        
        $sql = "DELETE FROM {$this->table} WHERE {$this->primaryKey} = ?";
        
        try {
            $stmt = $this->getConnection()->prepare($sql);
            $result = $stmt->execute([$this->attributes[$this->primaryKey]]);
            
            if ($result) {
                $this->exists = false;
            }
            
            return $result;
        } catch (\PDOException $e) {
            error_log("Model delete error: " . $e->getMessage());
            return false;
        }
    }
}
