// === ads-manager.js ===
// ОТКЛЮЧЕНО: Файл отключен для предотвращения дублирования
// Используется ads-manager-full.js вместо этого файла

console.log('[ads-manager.js] ОТКЛЮЧЕН - используется ads-manager-full.js');

// Останавливаем выполнение этого файла
return;

class AdsManager {
  constructor() {
    this.adsController = null;
    this.isButtonPressed = false;
    this.isInitialized = false;
    this.initializationAttempts = 0;
    this.maxInitializationAttempts = 50;

    // Элементы интерфейса (исправленные ID из HTML)
    this.elements = {
      watchAdButton: document.getElementById('openLinkButton'),
      watchVideoButton: document.getElementById('watchVideoButton'),
      openLinkButton: document.getElementById('openAdButton'),
    };

    // Экспорт переменных для обратной совместимости (из оригинала)
    window.adsController = this.adsController;
    window.isButtonPressed = this.isButtonPressed;
  }

  async init() {
    console.log('[AdsManager] Инициализация...');
    this.toggleAllAdButtons(false, 'Инициализация...');

    try {
      const tgProxy = window.TelegramGameProxy;
      if (tgProxy && typeof tgProxy.initAds === 'function') {
        this.adsController = await tgProxy.initAds({ pubId: window.AppConfig.MY_PUB_ID, appId: window.AppConfig.MY_APP_ID });
        this.isInitialized = true;
        console.log('[AdsManager] ✅ SDK (TelegramGameProxy) инициализирован.');
      } else {
        throw new Error('Рекламный SDK не найден.');
      }
      this.toggleAllAdButtons(true, 'Реклама готова');
    } catch (error) {
      console.error('[AdsManager] ❌ Ошибка инициализации SDK:', error);
      window.appUtils.showAlert(`Ошибка модуля рекламы: ${error.message}`);
    }
    this.setupEventListeners();
  }

  setupEventListeners() {
    this.buttons.openLinkButton.addEventListener('click', () => this.showAd(window.AppConfig.AD_TYPES.NATIVE_BANNER, this.buttons.openLinkButton));
    this.buttons.watchVideoButton.addEventListener('click', () => this.showAd(window.AppConfig.AD_TYPES.REWARDED_VIDEO, this.buttons.watchVideoButton));
    this.buttons.openAdButton.addEventListener('click', () => this.showAd(window.AppConfig.AD_TYPES.INTERSTITIAL, this.buttons.openAdButton));
  }

  async showAd(adType, button) {
    if (!this.canShowAd(button)) return;
    
    this.isAdShowing = true;
    this.toggleAllAdButtons(false, 'Загрузка...');
    window.appUtils.showStatus('Загрузка рекламы...');
    window.appUtils.vibrate('light');
    
    try {
      let adPromise;
      if (this.adsController) {
        switch (adType) {
          case window.AppConfig.AD_TYPES.NATIVE_BANNER: adPromise = this.adsController.showBanner(); break;
          case window.AppConfig.AD_TYPES.REWARDED_VIDEO: adPromise = this.adsController.showVideo(); break;
          case window.AppConfig.AD_TYPES.INTERSTITIAL: adPromise = this.adsController.showInterstitial(); break;
          default: throw new Error('Неизвестный тип рекламы');
        }
      } else {
        throw new Error('Рекламный контроллер не инициализирован');
      }
      
      const result = await adPromise;
      if (result && result.success) {
        await this.handleAdSuccess(adType, button);
      } else {
        throw new Error('Реклама не была показана или была закрыта.');
      }
    } catch (error) {
      this.handleAdError(error, button);
    } finally {
      this.isAdShowing = false;
    }
  }

  canShowAd(button) {
    if (!this.isInitialized) {
      window.appUtils.showAlert('Модуль рекламы не инициализирован.');
      return false;
    }
    if (this.isAdShowing) {
      window.appUtils.showStatus('Подождите...', 'info');
      return false;
    }
    if (button.classList.contains('pressed')) {
        window.appUtils.showStatus('Подождите окончания таймера...', 'info');
        return false;
    }
    return true;
  }

  async handleAdSuccess(adType, button) {
    window.appUtils.showStatus('Начисляем награду...');
    const initData = window.appUtils.getInitData();
    if (!initData) throw new Error('Нет данных пользователя для начисления награды.');

    try {
      const response = await fetch(`${window.API_BASE_URL}/recordAdView.php`, {
        method: "POST", headers: { "Content-Type": "application/json" }, body: JSON.stringify({ initData, adType }),
      });
      if (!response.ok) throw new Error(`Ошибка сервера: ${response.status}`);
      const data = await response.json();
      if (data.error) throw new Error(data.error);

      if (data.newBalance !== undefined) {
        window.balanceManager.updateBalance(data.newBalance, `ad_reward_${adType}`);
        window.appUtils.showStatus(`Награда зачислена!`, "success");
        window.appUtils.vibrate('success');
        window.appUtils.playCoinsSound(data.reward);
        this.startCountdown(button);
      }
    } catch (error) {
      this.handleAdError(error, button);
    }
  }

  handleAdError(error, button) {
    console.warn('[AdsManager] Ошибка показа рекламы:', error);
    let message = 'Реклама недоступна. Попробуйте позже.';
    const errorMessage = error?.message?.toLowerCase() || '';

    if (errorMessage.includes('no ad') || errorMessage.includes('not available')) {
        message = 'Нет доступной рекламы. Попробуйте позже.';
    }
    window.appUtils.showStatus(message, 'error');
    window.appUtils.vibrate('warning');
    // Запускаем короткий таймер при ошибке, чтобы не спамили
    this.startCountdown(button, 15); 
  }
  
  /**
   * Запускает таймер обратного отсчета на кнопке.
   * @param {HTMLElement} button - Кнопка, на которой будет таймер.
   * @param {number} [seconds=AppConfig.AD_REWARD_COOLDOWN || 60] - Длительность таймера в секундах.
   */
  startCountdown(button, seconds) {
    // Используем значение из настроек сервера, если оно есть, иначе 60 секунд
    const cooldownDuration = seconds || (window.appSettings.get('ad_cooldowns')?.[button.id] || 60);

    if (!button || this.isCountdownActive) return;
    this.isCountdownActive = true;
    this.toggleAllAdButtons(false);
    button.classList.add('pressed');
    const overlay = document.createElement('div');
    overlay.className = 'countdown-overlay';
    overlay.innerHTML = `<span class="countdown-time">${cooldownDuration}</span>`;
    button.appendChild(overlay);

    let remainingTime = cooldownDuration;
    this.countdownTimer = setInterval(() => {
      remainingTime--;
      overlay.querySelector('.countdown-time').textContent = remainingTime;
      if (remainingTime <= 0) {
        clearInterval(this.countdownTimer);
        this.isCountdownActive = false;
        
        if (window.AppConfig.AUTO_RELOAD_AFTER_COUNTDOWN && !window.AppConfig.USE_SOFT_REFRESH) {
            window.appUtils.showStatus("Обновление рекламных блоков...", "info");
            if (window.pageManager) {
                window.pageManager.saveCurrentPage();
            }
            setTimeout(() => window.location.reload(), 1000);
        } else {
            button.removeChild(overlay);
            button.classList.remove('pressed');
            this.toggleAllAdButtons(true, 'Готово');
        }
      }
    }, 1000);
  }

  toggleAllAdButtons(isEnabled, reason = '') {
    Object.values(this.buttons).forEach(btn => {
      if (btn) {
        btn.disabled = !isEnabled;
        if (isEnabled) {
            btn.removeAttribute('data-disabled-reason');
        } else {
            btn.setAttribute('data-disabled-reason', reason);
        }
      }
    });
  }
}

// 🚀 ЕДИНАЯ СИСТЕМА: Всегда используем AdsManagerFull
if (window.adsManagerFull) {
  console.log('📺 [AdsManager] ✅ ЕДИНАЯ СИСТЕМА: Используем AdsManagerFull - полная версия с исправлениями');
  window.adsManager = window.adsManagerFull;

  // Функции уже правильно настроены в ads-manager-full.js
  console.log('📺 [AdsManager] ✅ ЕДИНАЯ СИСТЕМА: Функции уже экспортированы из AdsManagerFull');
} else {
  console.warn('📺 [AdsManager] ⚠️ AdsManagerFull не найден! Это может привести к проблемам.');
  console.warn('📺 [AdsManager] ⚠️ Убедитесь что ads-manager-full.js загружается ПЕРЕД ads-manager.js');

  // Создаем заглушку чтобы не сломать приложение
  window.adsManager = {
    init: () => console.warn('[AdsManager] Заглушка: AdsManagerFull не загружен'),
    showAd: () => console.warn('[AdsManager] Заглушка: AdsManagerFull не загружен'),
    initializeRichAds: () => console.warn('[AdsManager] Заглушка: AdsManagerFull не загружен')
  };
}

console.log('📺 [AdsManager] Менеджер рекламы загружен с полной интеграцией.');