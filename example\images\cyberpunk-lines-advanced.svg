<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Продвинутые градиенты -->
    <linearGradient id="darkGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0d0d0d;stop-opacity:1" />
      <stop offset="20%" style="stop-color:#1a1a1a;stop-opacity:1" />
      <stop offset="40%" style="stop-color:#2d2d2d;stop-opacity:1" />
      <stop offset="60%" style="stop-color:#1a1a1a;stop-opacity:1" />
      <stop offset="80%" style="stop-color:#262626;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0d0d0d;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="orangeLine" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#ff6b35;stop-opacity:0" />
      <stop offset="20%" style="stop-color:#ff8c42;stop-opacity:0.6" />
      <stop offset="50%" style="stop-color:#ff6b35;stop-opacity:1" />
      <stop offset="80%" style="stop-color:#ff8c42;stop-opacity:0.6" />
      <stop offset="100%" style="stop-color:#ff6b35;stop-opacity:0" />
    </linearGradient>
    
    <radialGradient id="powerGlow" cx="50%" cy="50%" r="30%">
      <stop offset="0%" style="stop-color:#ff6b35;stop-opacity:0.3" />
      <stop offset="50%" style="stop-color:#ff6b35;stop-opacity:0.1" />
      <stop offset="100%" style="stop-color:#ff6b35;stop-opacity:0" />
    </radialGradient>
    
    <!-- Продвинутая сетка -->
    <pattern id="techGrid" width="80" height="80" patternUnits="userSpaceOnUse">
      <path d="M 80 0 L 0 0 0 80" fill="none" stroke="#ff6b35" stroke-width="0.3" opacity="0.15"/>
      <circle cx="40" cy="40" r="1" fill="#ff6b35" opacity="0.1"/>
    </pattern>
    
    <!-- Мелкая сетка -->
    <pattern id="microGrid" width="20" height="20" patternUnits="userSpaceOnUse">
      <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#ff6b35" stroke-width="0.1" opacity="0.08"/>
    </pattern>
    
    <!-- Эффекты свечения -->
    <filter id="neonGlow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    
    <filter id="powerGlowFilter" x="-100%" y="-100%" width="300%" height="300%">
      <feGaussianBlur stdDeviation="8" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    
    <!-- Анимации -->
    <animateTransform id="rotate" attributeName="transform" type="rotate" 
                      values="0 960 540;360 960 540" dur="60s" repeatCount="indefinite"/>
  </defs>
  
  <!-- Основной темный фон -->
  <rect width="1920" height="1080" fill="url(#darkGradient)"/>
  
  <!-- Мелкая сетка -->
  <rect width="1920" height="1080" fill="url(#microGrid)"/>
  
  <!-- Основная сетка -->
  <rect width="1920" height="1080" fill="url(#techGrid)"/>
  
  <!-- Центральное энергетическое свечение -->
  <rect width="1920" height="1080" fill="url(#powerGlow)"/>
  
  <!-- Основные структурные линии -->
  <g stroke="#ff6b35" stroke-width="3" fill="none" opacity="0.4" filter="url(#neonGlow)">
    <!-- Главные оси -->
    <line x1="0" y1="540" x2="1920" y2="540"/>
    <line x1="960" y1="0" x2="960" y2="1080"/>
    
    <!-- Диагональные -->
    <line x1="0" y1="0" x2="1920" y2="1080"/>
    <line x1="1920" y1="0" x2="0" y2="1080"/>
  </g>
  
  <!-- Вторичные линии -->
  <g stroke="#ff6b35" stroke-width="1.5" fill="none" opacity="0.25">
    <!-- Вертикальные секции -->
    <line x1="320" y1="0" x2="320" y2="1080"/>
    <line x1="640" y1="0" x2="640" y2="1080"/>
    <line x1="1280" y1="0" x2="1280" y2="1080"/>
    <line x1="1600" y1="0" x2="1600" y2="1080"/>
    
    <!-- Горизонтальные секции -->
    <line x1="0" y1="180" x2="1920" y2="180"/>
    <line x1="0" y1="360" x2="1920" y2="360"/>
    <line x1="0" y1="720" x2="1920" y2="720"/>
    <line x1="0" y1="900" x2="1920" y2="900"/>
  </g>
  
  <!-- Киберпанк геометрические узлы -->
  <g fill="none" stroke="#ff6b35" stroke-width="2" opacity="0.3" filter="url(#neonGlow)">
    <!-- Центральный узел -->
    <polygon points="960,440 1080,540 960,640 840,540"/>
    <polygon points="960,480 1040,540 960,600 880,540"/>
    <circle cx="960" cy="540" r="20"/>
    
    <!-- Угловые узлы -->
    <polygon points="160,180 240,180 240,260 160,260"/>
    <polygon points="1680,180 1760,180 1760,260 1680,260"/>
    <polygon points="160,820 240,820 240,900 160,900"/>
    <polygon points="1680,820 1760,820 1760,900 1680,900"/>
    
    <!-- Боковые узлы -->
    <polygon points="320,440 400,540 320,640 240,540"/>
    <polygon points="1520,440 1600,540 1520,640 1440,540"/>
  </g>
  
  <!-- Технологические панели -->
  <g fill="none" stroke="#ff6b35" stroke-width="1" opacity="0.2">
    <!-- Левая панель -->
    <rect x="50" y="300" width="150" height="480" rx="10"/>
    <line x1="70" y1="320" x2="180" y2="320"/>
    <line x1="70" y1="350" x2="180" y2="350"/>
    <line x1="70" y1="380" x2="180" y2="380"/>
    <circle cx="125" cy="540" r="30"/>
    
    <!-- Правая панель -->
    <rect x="1720" y="300" width="150" height="480" rx="10"/>
    <line x1="1740" y1="320" x2="1850" y2="320"/>
    <line x1="1740" y1="350" x2="1850" y2="350"/>
    <line x1="1740" y1="380" x2="1850" y2="380"/>
    <circle cx="1795" cy="540" r="30"/>
    
    <!-- Верхняя панель -->
    <rect x="720" y="50" width="480" height="100" rx="10"/>
    <line x1="740" y1="80" x2="740" y2="120"/>
    <line x1="780" y1="80" x2="780" y2="120"/>
    <line x1="820" y1="80" x2="820" y2="120"/>
    
    <!-- Нижняя панель -->
    <rect x="720" y="930" width="480" height="100" rx="10"/>
    <line x1="740" y1="960" x2="740" y2="1000"/>
    <line x1="780" y1="960" x2="780" y2="1000"/>
    <line x1="820" y1="960" x2="820" y2="1000"/>
  </g>
  
  <!-- Энергетические линии -->
  <g stroke="url(#orangeLine)" stroke-width="4" fill="none" opacity="0.6">
    <line x1="0" y1="540" x2="1920" y2="540"/>
    <line x1="960" y1="0" x2="960" y2="1080"/>
  </g>
  
  <!-- Светящиеся точки -->
  <g fill="#ff6b35" filter="url(#powerGlowFilter)">
    <circle cx="960" cy="540" r="4" opacity="0.8"/>
    <circle cx="320" cy="180" r="2" opacity="0.6"/>
    <circle cx="1600" cy="180" r="2" opacity="0.6"/>
    <circle cx="320" cy="900" r="2" opacity="0.6"/>
    <circle cx="1600" cy="900" r="2" opacity="0.6"/>
    <circle cx="640" cy="360" r="2" opacity="0.4"/>
    <circle cx="1280" cy="360" r="2" opacity="0.4"/>
    <circle cx="640" cy="720" r="2" opacity="0.4"/>
    <circle cx="1280" cy="720" r="2" opacity="0.4"/>
  </g>
  
  <!-- Дополнительные декоративные элементы -->
  <g stroke="#ff6b35" stroke-width="0.5" fill="none" opacity="0.15">
    <!-- Мелкие технические детали -->
    <rect x="100" y="100" width="40" height="40" rx="5"/>
    <rect x="1780" y="100" width="40" height="40" rx="5"/>
    <rect x="100" y="940" width="40" height="40" rx="5"/>
    <rect x="1780" y="940" width="40" height="40" rx="5"/>
    
    <!-- Соединительные линии -->
    <path d="M 140,120 Q 200,120 200,180"/>
    <path d="M 1780,120 Q 1720,120 1720,180"/>
    <path d="M 140,960 Q 200,960 200,900"/>
    <path d="M 1780,960 Q 1720,960 1720,900"/>
  </g>
  
  <!-- Финальные акцентные линии -->
  <g stroke="#ff6b35" stroke-width="1" fill="none" opacity="0.3" filter="url(#neonGlow)">
    <line x1="480" y1="270" x2="1440" y2="270"/>
    <line x1="480" y1="810" x2="1440" y2="810"/>
    <line x1="480" y1="270" x2="480" y2="810"/>
    <line x1="1440" y1="270" x2="1440" y2="810"/>
  </g>
</svg>
