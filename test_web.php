<?php
/**
 * test_web.php
 * Simple web test to check if the application is working
 */

echo "<h1>🧪 UniQPaid Web Test</h1>";

// Test 1: Check if we can access the main page
echo "<h2>1. Testing main page access</h2>";
$mainUrl = "http://" . $_SERVER['HTTP_HOST'] . "/";
echo "<p>Main URL: <a href='$mainUrl' target='_blank'>$mainUrl</a></p>";

// Test 2: Check if we can access API endpoints
echo "<h2>2. Testing API endpoints</h2>";
$apiUrls = [
    '/api/settings' => 'App Settings',
    '/api/currencies' => 'Available Currencies'
];

foreach ($apiUrls as $endpoint => $name) {
    $url = "http://" . $_SERVER['HTTP_HOST'] . $endpoint;
    echo "<p>$name: <a href='$url' target='_blank'>$url</a></p>";
}

// Test 3: Check file structure
echo "<h2>3. File structure check</h2>";
$requiredFiles = [
    'public/index.php' => 'Main entry point',
    'public/css/cyberpunk-styles.css' => 'CSS styles',
    'public/js/app.js' => 'JavaScript app',
    'views/index.php' => 'HTML template',
    'app/config.php' => 'Configuration',
    'database/data/settings.json' => 'Database settings'
];

foreach ($requiredFiles as $file => $description) {
    $exists = file_exists($file);
    $status = $exists ? "✅" : "❌";
    echo "<p>$status $description: $file</p>";
}

// Test 4: Database check
echo "<h2>4. Database check</h2>";
try {
    define('APP_ROOT', __DIR__);
    require_once 'app/autoload.php';
    require_once 'app/config.php';
    
    $database = Database::getInstance();
    echo "<p>✅ Database connection successful</p>";
    
    $fileDb = FileDatabase::getInstance();
    $tables = $fileDb->getTables();
    echo "<p>✅ File database tables: " . implode(', ', $tables) . "</p>";
    
} catch (Exception $e) {
    echo "<p>❌ Database error: " . $e->getMessage() . "</p>";
}

echo "<h2>🎉 Test completed!</h2>";
echo "<p><strong>Next steps:</strong></p>";
echo "<ul>";
echo "<li>Open the main page to see the application</li>";
echo "<li>Test API endpoints to verify functionality</li>";
echo "<li>Check browser console for JavaScript errors</li>";
echo "</ul>";
?>
