<?php
declare(strict_types=1);

/**
 * database/init.php
 * Database initialization script
 * 
 * This script creates the SQLite database and initializes the schema.
 * Run this script once to set up the database.
 */

// Define APP_ROOT first
define('APP_ROOT', dirname(__DIR__));

// Include configuration and autoloader
require_once APP_ROOT . '/app/autoload.php';
require_once APP_ROOT . '/app/config.php';

try {
    echo "Initializing UniQPaid database...\n";
    
    // Create database instance (this will automatically create the schema)
    $database = Database::getInstance();
    
    echo "✅ Database initialized successfully!\n";
    echo "Database location: " . DB_PATH . "\n";
    
    // Test the connection by querying settings
    $connection = Database::getConnection();
    $stmt = $connection->query("SELECT COUNT(*) as count FROM settings");
    $result = $stmt->fetch();
    
    echo "✅ Settings table created with {$result['count']} default entries\n";
    
    // Show table structure
    echo "\n📋 Database tables created:\n";
    $tables = $connection->query("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")->fetchAll();
    foreach ($tables as $table) {
        echo "  - {$table['name']}\n";
    }
    
    echo "\n🎉 Database setup complete!\n";
    
} catch (Exception $e) {
    echo "❌ Error initializing database: " . $e->getMessage() . "\n";
    exit(1);
}
