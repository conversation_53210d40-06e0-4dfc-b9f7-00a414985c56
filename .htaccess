# .htaccess
# Main htaccess file for UniQPaid application
# Redirects all requests to public/ directory

RewriteEngine On

# Redirect all requests to public/ directory
RewriteCond %{REQUEST_URI} !^/public/
RewriteRule ^(.*)$ public/$1 [L]

# Security: Block access to sensitive files and directories
<Files ~ "^\.">
    Order allow,deny
    Deny from all
</Files>

# Block access to app/ directory (temporarily disabled for import)
# RewriteRule ^app/ - [F,L]

# Block access to database/ directory (temporarily disabled for import)
# RewriteRule ^database/ - [F,L]

# Block access to config files
<FilesMatch "\.(env|ini|log|conf)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Block access to PHP files in root (except index.php and test files)
<FilesMatch "^(?!(index|test_web|test_api)\.php$).*\.php$">
    Order allow,deny
    Deny from all
</FilesMatch>
